# Sets default memory used for gradle commands. Can be overridden by user or command line properties.
org.gradle.jvmargs=-Xmx3G
org.gradle.daemon=false

# Mod Properties
mod_id=scriptrecorder
mod_name=Minecraft Script Recorder
mod_license=All Rights Reserved
mod_version=3.5.0
mod_authors=ScriptRecorder Team
mod_description=A comprehensive Minecraft 1.8.9 mod for recording and replaying mouse and keyboard actions. Features script recording, toggle sprint/crouch, keystroke display, and more!

# Minecraft Properties
minecraft_version=1.8.9
minecraft_version_range=[1.8.9]
forge_version=11.15.1.2318
forge_version_range=[11.15.1,)
loader_version_range=[11.15.1,)
