package com.scriptrecorder.mod.waypoints;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import com.scriptrecorder.mod.ScriptRecorderMod;
import net.minecraft.client.Minecraft;
import net.minecraft.util.Vec3;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Manages waypoints - creation, deletion, saving, loading
 */
@SideOnly(Side.CLIENT)
public class WaypointManager {
    
    private static final String WAYPOINTS_FILE = "waypoints.json";
    private static WaypointManager instance;
    private static File waypointsFile;
    
    private List<Waypoint> waypoints;
    private Gson gson;
    
    private WaypointManager() {
        this.waypoints = new ArrayList<>();
        this.gson = new GsonBuilder()
            .setPrettyPrinting()
            .create();
    }
    
    public static WaypointManager getInstance() {
        if (instance == null) {
            instance = new WaypointManager();
            instance.initialize();
        }
        return instance;
    }
    
    public void initialize() {
        // Create waypoints file path
        File minecraftDir = Minecraft.getInstance().gameDirectory;
        waypointsFile = new File(minecraftDir, WAYPOINTS_FILE);
        
        // Load existing waypoints
        loadWaypoints();
        
        ScriptRecorderMod.LOGGER.info("Waypoint Manager initialized from: " + waypointsFile.getAbsolutePath());
    }
    
    public void loadWaypoints() {
        if (waypointsFile == null || !waypointsFile.exists()) {
            ScriptRecorderMod.LOGGER.info("No waypoints file found, starting with empty list");
            return;
        }
        
        try (FileReader reader = new FileReader(waypointsFile)) {
            Type listType = new TypeToken<List<Waypoint>>(){}.getType();
            List<Waypoint> loaded = gson.fromJson(reader, listType);
            if (loaded != null) {
                waypoints.clear();
                waypoints.addAll(loaded);
                ScriptRecorderMod.LOGGER.info("Loaded " + waypoints.size() + " waypoints");
            }
        } catch (IOException e) {
            ScriptRecorderMod.LOGGER.error("Failed to load waypoints", e);
        }
    }
    
    public void saveWaypoints() {
        if (waypointsFile == null) {
            ScriptRecorderMod.LOGGER.error("Waypoints file path not initialized");
            return;
        }
        
        try (FileWriter writer = new FileWriter(waypointsFile)) {
            gson.toJson(waypoints, writer);
            ScriptRecorderMod.LOGGER.debug("Saved " + waypoints.size() + " waypoints");
        } catch (IOException e) {
            ScriptRecorderMod.LOGGER.error("Failed to save waypoints", e);
        }
    }
    
    public boolean addWaypoint(String name, Vec3 position, String dimension) {
        if (name == null || name.trim().isEmpty()) {
            return false;
        }
        
        // Check if waypoint with same name already exists
        if (getWaypoint(name).isPresent()) {
            return false;
        }
        
        Waypoint waypoint = new Waypoint(name.trim(), position.xCoord, position.yCoord, position.zCoord, dimension);
        waypoints.add(waypoint);
        saveWaypoints();
        return true;
    }
    
    public boolean removeWaypoint(String name) {
        Optional<Waypoint> waypoint = getWaypoint(name);
        if (waypoint.isPresent()) {
            waypoints.remove(waypoint.get());
            saveWaypoints();
            return true;
        }
        return false;
    }
    
    public Optional<Waypoint> getWaypoint(String name) {
        return waypoints.stream()
            .filter(w -> w.getName().equalsIgnoreCase(name))
            .findFirst();
    }
    
    public List<Waypoint> getAllWaypoints() {
        return new ArrayList<>(waypoints);
    }
    
    public List<Waypoint> getWaypointsInDimension(String dimension) {
        return waypoints.stream()
            .filter(w -> w.getDimension().equals(dimension))
            .filter(Waypoint::isEnabled)
            .toList();
    }
    
    public void updateWaypoint(Waypoint waypoint) {
        saveWaypoints();
    }
    
    public String getCurrentDimension() {
        Minecraft mc = Minecraft.getMinecraft();
        if (mc.theWorld != null) {
            return mc.theWorld.provider.getDimensionName();
        }
        return "overworld";
    }

    public net.minecraft.util.Vec3 getCurrentPlayerPosition() {
        Minecraft mc = Minecraft.getMinecraft();
        if (mc.thePlayer != null) {
            return new net.minecraft.util.Vec3(mc.thePlayer.posX, mc.thePlayer.posY, mc.thePlayer.posZ);
        }
        return new net.minecraft.util.Vec3(0, 0, 0);
    }
    
    public boolean addWaypointAtCurrentLocation(String name) {
        return addWaypoint(name, getCurrentPlayerPosition(), getCurrentDimension());
    }
    
    public void sendChatMessage(String message, boolean isError) {
        Minecraft mc = Minecraft.getMinecraft();
        if (mc.thePlayer != null) {
            net.minecraft.util.ChatComponentText component = new net.minecraft.util.ChatComponentText("[Waypoints] " + message);
            mc.thePlayer.addChatMessage(component);
        }
    }
    
    public int getWaypointCount() {
        return waypoints.size();
    }
    
    public void clearAllWaypoints() {
        waypoints.clear();
        saveWaypoints();
    }
}
