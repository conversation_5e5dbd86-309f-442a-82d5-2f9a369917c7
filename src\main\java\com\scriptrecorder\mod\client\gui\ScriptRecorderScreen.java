package com.scriptrecorder.mod.client.gui;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.ScriptRecorderClient;
import com.scriptrecorder.mod.client.settings.ModSettings;
import com.scriptrecorder.mod.recording.RecordedScript;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.Minecraft;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import org.lwjgl.glfw.GLFW;

/**
 * Sleek, modern GUI for the Script Recorder mod - 1.20.4
 * Features: In-GUI keybind customization, loop controls, chat notifications
 */
public class ScriptRecorderScreen extends Screen {

    // GUI Dimensions and Colors
    private static final int GUI_WIDTH = 400;
    private static final int GUI_HEIGHT = 320;
    private static final int PANEL_COLOR = 0xE0000000; // Semi-transparent black
    private static final int BORDER_COLOR = 0xFF404040; // Dark gray
    private static final int ACCENT_COLOR = 0xFF00AA00; // Green
    private static final int ERROR_COLOR = 0xFFFF4444; // Red
    private static final int TEXT_COLOR = 0xFFFFFFFF; // White
    private static final int BUTTON_COLOR = 0xFF2A2A2A; // Dark button
    private static final int BUTTON_HOVER_COLOR = 0xFF3A3A3A; // Lighter on hover

    // GUI Components
    private Button recordButton;
    private Button playButton;
    private Button stopButton;
    private Button toggleSprintButton;
    private Button toggleCrouchButton;
    private Button toggleKeystrokesButton;

    // Keybind buttons
    private Button recordKeybindButton;
    private Button stopRecordKeybindButton;
    private Button playKeybindButton;

    // Loop control
    private EditBox loopCountBox;
    private Button infiniteLoopButton;

    // Script management buttons
    private Button exportButton;
    private Button importButton;
    private EditBox scriptNameBox;

    // State
    private boolean waitingForKeybind = false;
    private String keybindWaitingFor = "";
    private int loopCount = 1;
    private boolean infiniteLoop = false;

    public ScriptRecorderScreen() {
        super(Component.literal("Script Recorder"));
    }

    @Override
    protected void init() {
        super.init();

        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int guiLeft = centerX - GUI_WIDTH / 2;
        int guiTop = centerY - GUI_HEIGHT / 2;

        // Load current settings
        loopCount = ModSettings.getLoopCount();
        infiniteLoop = ModSettings.isInfiniteLoop();

        // === MAIN CONTROL BUTTONS ===
        int buttonY = guiTop + 40;

        // Record button
        recordButton = Button.builder(
            getRecordButtonText(),
            button -> onRecordPressed())
            .bounds(guiLeft + 20, buttonY, 100, 24)
            .build();
        this.addRenderableWidget(recordButton);

        // Play button
        playButton = Button.builder(
            getPlayButtonText(),
            button -> onPlayPressed())
            .bounds(guiLeft + 140, buttonY, 100, 24)
            .build();
        this.addRenderableWidget(playButton);

        // Stop button
        stopButton = Button.builder(
            Component.literal("Stop"),
            button -> onStopPressed())
            .bounds(guiLeft + 260, buttonY, 100, 24)
            .build();
        this.addRenderableWidget(stopButton);

        // === LOOP CONTROLS ===
        buttonY += 40;

        // Loop count input
        loopCountBox = new EditBox(this.font, guiLeft + 20, buttonY, 60, 20, Component.literal("Loop Count"));
        loopCountBox.setValue(infiniteLoop ? "∞" : String.valueOf(loopCount));
        loopCountBox.setMaxLength(3);
        this.addRenderableWidget(loopCountBox);

        // Infinite loop toggle
        infiniteLoopButton = Button.builder(
            Component.literal(infiniteLoop ? "∞ ON" : "∞ OFF"),
            button -> toggleInfiniteLoop())
            .bounds(guiLeft + 90, buttonY, 50, 20)
            .build();
        this.addRenderableWidget(infiniteLoopButton);

        // === KEYBIND SECTION ===
        buttonY += 50;

        // Record keybind
        recordKeybindButton = Button.builder(
            getKeybindButtonText("record"),
            button -> startKeybindWait("record"))
            .bounds(guiLeft + 20, buttonY, 110, 20)
            .build();
        this.addRenderableWidget(recordKeybindButton);

        // Stop record keybind
        stopRecordKeybindButton = Button.builder(
            getKeybindButtonText("stop_record"),
            button -> startKeybindWait("stop_record"))
            .bounds(guiLeft + 140, buttonY, 110, 20)
            .build();
        this.addRenderableWidget(stopRecordKeybindButton);

        // Play keybind
        playKeybindButton = Button.builder(
            getKeybindButtonText("play"),
            button -> startKeybindWait("play"))
            .bounds(guiLeft + 260, buttonY, 110, 20)
            .build();
        this.addRenderableWidget(playKeybindButton);

        // === TOGGLE FEATURES ===
        buttonY += 40;

        toggleSprintButton = Button.builder(
            getSprintButtonText(),
            button -> toggleSprint())
            .bounds(guiLeft + 20, buttonY, 110, 20)
            .build();
        this.addRenderableWidget(toggleSprintButton);

        toggleCrouchButton = Button.builder(
            getCrouchButtonText(),
            button -> toggleCrouch())
            .bounds(guiLeft + 140, buttonY, 110, 20)
            .build();
        this.addRenderableWidget(toggleCrouchButton);

        toggleKeystrokesButton = Button.builder(
            getKeystrokesButtonText(),
            button -> toggleKeystrokes())
            .bounds(guiLeft + 260, buttonY, 110, 20)
            .build();
        this.addRenderableWidget(toggleKeystrokesButton);

        // === SCRIPT MANAGEMENT ===
        buttonY += 40;

        // Script name input
        scriptNameBox = new EditBox(this.font, guiLeft + 20, buttonY, 200, 20, Component.literal("Script Name"));
        scriptNameBox.setValue("farmscript");
        scriptNameBox.setMaxLength(50);
        this.addRenderableWidget(scriptNameBox);

        // Export button
        exportButton = Button.builder(
            Component.literal("Export"),
            button -> onExportPressed())
            .bounds(guiLeft + 230, buttonY, 70, 20)
            .build();
        this.addRenderableWidget(exportButton);

        // Import button
        importButton = Button.builder(
            Component.literal("Import"),
            button -> onImportPressed())
            .bounds(guiLeft + 310, buttonY, 70, 20)
            .build();
        this.addRenderableWidget(importButton);

        // === CLOSE BUTTON ===
        this.addRenderableWidget(Button.builder(
            Component.literal("Close"),
            button -> this.onClose())
            .bounds(guiLeft + GUI_WIDTH - 80, guiTop + GUI_HEIGHT - 30, 60, 20)
            .build());

        updateButtonStates();
    }

    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        this.renderBackground(guiGraphics, mouseX, mouseY, partialTick);

        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int guiLeft = centerX - GUI_WIDTH / 2;
        int guiTop = centerY - GUI_HEIGHT / 2;

        // Draw main panel background
        guiGraphics.fill(guiLeft, guiTop, guiLeft + GUI_WIDTH, guiTop + GUI_HEIGHT, PANEL_COLOR);

        // Draw border
        drawBorder(guiGraphics, guiLeft, guiTop, GUI_WIDTH, GUI_HEIGHT, BORDER_COLOR);

        // Draw title with gradient effect
        String title = "Script Recorder v3.1.0";
        guiGraphics.drawCenteredString(this.font, title, centerX, guiTop + 15, ACCENT_COLOR);

        // Draw hotkey info
        String hotkeyInfo = "Hotkey: Ctrl + \\ + ]";
        guiGraphics.drawString(this.font, hotkeyInfo, guiLeft + 10, guiTop + GUI_HEIGHT - 15, 0xFF888888);

        // Draw status section
        drawStatusSection(guiGraphics, guiLeft, guiTop);

        // Draw section labels
        guiGraphics.drawString(this.font, "Controls:", guiLeft + 20, guiTop + 30, TEXT_COLOR);
        guiGraphics.drawString(this.font, "Loop Settings:", guiLeft + 20, guiTop + 80, TEXT_COLOR);
        guiGraphics.drawString(this.font, "Keybinds:", guiLeft + 20, guiTop + 130, TEXT_COLOR);
        guiGraphics.drawString(this.font, "Toggle Features:", guiLeft + 20, guiTop + 180, TEXT_COLOR);

        // Draw current script info
        drawScriptInfo(guiGraphics, guiLeft, guiTop);

        // Draw keybind waiting message
        if (waitingForKeybind) {
            String message = "Press a key to bind to " + keybindWaitingFor + " (ESC to cancel)";
            int messageWidth = this.font.width(message);
            guiGraphics.fill(centerX - messageWidth/2 - 5, centerY - 10,
                           centerX + messageWidth/2 + 5, centerY + 10, 0xE0000000);
            guiGraphics.drawCenteredString(this.font, message, centerX, centerY, 0xFFFFAA);
        }

        super.render(guiGraphics, mouseX, mouseY, partialTick);
    }

    private void drawBorder(GuiGraphics guiGraphics, int x, int y, int width, int height, int color) {
        // Top
        guiGraphics.fill(x, y, x + width, y + 1, color);
        // Bottom
        guiGraphics.fill(x, y + height - 1, x + width, y + height, color);
        // Left
        guiGraphics.fill(x, y, x + 1, y + height, color);
        // Right
        guiGraphics.fill(x + width - 1, y, x + width, y + height, color);
    }

    private void drawStatusSection(GuiGraphics guiGraphics, int guiLeft, int guiTop) {
        String status;
        int statusColor;

        if (ScriptRecorderMod.scriptManager.isRecording()) {
            status = "REC";
            statusColor = ERROR_COLOR;
        } else if (ScriptRecorderMod.scriptManager.isPlaying()) {
            status = "PLAY";
            statusColor = ACCENT_COLOR;
        } else {
            status = "READY";
            statusColor = TEXT_COLOR;
        }

        int statusX = guiLeft + GUI_WIDTH - this.font.width(status) - 20;
        guiGraphics.drawString(this.font, status, statusX, guiTop + 15, statusColor);
    }

    private void drawScriptInfo(GuiGraphics guiGraphics, int guiLeft, int guiTop) {
        RecordedScript script = ScriptRecorderMod.scriptManager.getCurrentScript();
        if (script != null) {
            String info = String.format("Script: %d actions | %.1fs",
                script.getTotalActions(), script.getDuration());
            guiGraphics.drawString(this.font, info, guiLeft + 20, guiTop + GUI_HEIGHT - 50, 0xFFCCCCCC);
        }

        // Draw loop info
        String loopInfo = infiniteLoop ? "Loops: ∞" : "Loops: " + loopCount;
        guiGraphics.drawString(this.font, loopInfo, guiLeft + 200, guiTop + GUI_HEIGHT - 50, 0xFFCCCCCC);

        // Draw script name label
        guiGraphics.drawString(this.font, "Script Name:", guiLeft + 20, guiTop + GUI_HEIGHT - 85, 0xFFCCCCCC);
    }

    // === BUTTON TEXT METHODS ===

    private Component getRecordButtonText() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            return Component.literal("Recording...");
        }
        return Component.literal("Record");
    }

    private Component getPlayButtonText() {
        if (ScriptRecorderMod.scriptManager.isPlaying()) {
            return Component.literal("Playing...");
        }
        return Component.literal("Play");
    }

    private Component getSprintButtonText() {
        boolean enabled = ScriptRecorderClient.isSprintToggled();
        return Component.literal("Sprint: " + (enabled ? "ON" : "OFF"));
    }

    private Component getCrouchButtonText() {
        boolean enabled = ScriptRecorderClient.isCrouchToggled();
        return Component.literal("Crouch: " + (enabled ? "ON" : "OFF"));
    }

    private Component getKeystrokesButtonText() {
        boolean enabled = ModSettings.isKeystrokeDisplayEnabled();
        return Component.literal("Keys: " + (enabled ? "ON" : "OFF"));
    }

    private Component getKeybindButtonText(String keybindType) {
        int keyCode = ModSettings.getKeybind(keybindType);
        String keyName = getKeyName(keyCode);

        switch (keybindType) {
            case "record":
                return Component.literal(keyName + ": Record");
            case "stop_record":
                return Component.literal(keyName + ": Stop Rec");
            case "play":
                return Component.literal(keyName + ": Play");
            default:
                return Component.literal("Unknown");
        }
    }

    // === BUTTON ACTION METHODS ===

    private void onRecordPressed() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            RecordedScript script = ScriptRecorderMod.scriptManager.stopRecording();
            sendChatMessage("Recording stopped. " +
                (script != null ? script.getTotalActions() + " actions captured." : ""), false);
        } else {
            if (ScriptRecorderMod.scriptManager.startRecording("recorded_script")) {
                sendChatMessage("Recording started. Press F7 to stop.", false);
            } else {
                sendChatMessage("Failed to start recording!", true);
            }
        }
        updateButtonStates();
    }

    private void onPlayPressed() {
        if (ScriptRecorderMod.scriptManager.isPlaying()) {
            ScriptRecorderMod.scriptManager.stopPlayback();
            sendChatMessage("Playback stopped.", false);
        } else {
            RecordedScript script = ScriptRecorderMod.scriptManager.getCurrentScript();
            if (script != null) {
                script.setLoopCount(infiniteLoop ? -1 : loopCount);
                if (ScriptRecorderMod.scriptManager.startPlayback(script)) {
                    String loopText = infiniteLoop ? "infinite" : String.valueOf(loopCount);
                    sendChatMessage("Playing script (" + loopText + " loops). Press F8 to stop.", false);
                } else {
                    sendChatMessage("Failed to start playback!", true);
                }
            } else {
                sendChatMessage("No script to play! Record one first.", true);
            }
        }
        updateButtonStates();
    }

    private void onStopPressed() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            onRecordPressed(); // Stop recording
        } else if (ScriptRecorderMod.scriptManager.isPlaying()) {
            onPlayPressed(); // Stop playback
        }
    }

    private void toggleSprint() {
        boolean newState = !ScriptRecorderClient.isSprintToggled();
        ScriptRecorderClient.setSprintToggled(newState);
        sendChatMessage("Sprint toggle: " + (newState ? "ON" : "OFF"), false);
        updateButtonStates();
    }

    private void toggleCrouch() {
        boolean newState = !ScriptRecorderClient.isCrouchToggled();
        ScriptRecorderClient.setCrouchToggled(newState);
        sendChatMessage("Crouch toggle: " + (newState ? "ON" : "OFF"), false);
        updateButtonStates();
    }

    private void toggleKeystrokes() {
        boolean newState = !ModSettings.isKeystrokeDisplayEnabled();
        ModSettings.setKeystrokeDisplayEnabled(newState);
        sendChatMessage("Keystroke display: " + (newState ? "ON" : "OFF"), false);
        updateButtonStates();
    }

    private void toggleInfiniteLoop() {
        infiniteLoop = !infiniteLoop;
        ModSettings.setInfiniteLoop(infiniteLoop);

        if (infiniteLoop) {
            loopCountBox.setValue("∞");
            loopCountBox.setEditable(false);
        } else {
            loopCountBox.setValue(String.valueOf(loopCount));
            loopCountBox.setEditable(true);
        }

        infiniteLoopButton.setMessage(Component.literal(infiniteLoop ? "∞ ON" : "∞ OFF"));
    }

    // === KEYBIND HANDLING ===

    private void startKeybindWait(String keybindType) {
        waitingForKeybind = true;
        keybindWaitingFor = keybindType;
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (waitingForKeybind) {
            if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
                // Cancel keybind waiting
                waitingForKeybind = false;
                keybindWaitingFor = "";
                return true;
            }

            // Set the new keybind
            String keyName = getKeyName(keyCode);
            ModSettings.setKeybind(keybindWaitingFor, keyCode);
            updateKeybindButton(keybindWaitingFor, keyName);

            waitingForKeybind = false;
            keybindWaitingFor = "";

            sendChatMessage("Keybind updated: " + keyName, false);
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    private void updateKeybindButton(String keybindType, String keyName) {
        switch (keybindType) {
            case "record":
                recordKeybindButton.setMessage(getKeybindButtonText("record"));
                break;
            case "stop_record":
                stopRecordKeybindButton.setMessage(getKeybindButtonText("stop_record"));
                break;
            case "play":
                playKeybindButton.setMessage(getKeybindButtonText("play"));
                break;
        }
    }

    private String getKeyName(int keyCode) {
        String keyName = GLFW.glfwGetKeyName(keyCode, 0);
        if (keyName != null) {
            return keyName.toUpperCase();
        }

        // Handle special keys
        switch (keyCode) {
            case GLFW.GLFW_KEY_F1: return "F1";
            case GLFW.GLFW_KEY_F2: return "F2";
            case GLFW.GLFW_KEY_F3: return "F3";
            case GLFW.GLFW_KEY_F4: return "F4";
            case GLFW.GLFW_KEY_F5: return "F5";
            case GLFW.GLFW_KEY_F6: return "F6";
            case GLFW.GLFW_KEY_F7: return "F7";
            case GLFW.GLFW_KEY_F8: return "F8";
            case GLFW.GLFW_KEY_F9: return "F9";
            case GLFW.GLFW_KEY_F10: return "F10";
            case GLFW.GLFW_KEY_F11: return "F11";
            case GLFW.GLFW_KEY_F12: return "F12";
            case GLFW.GLFW_KEY_SPACE: return "SPACE";
            case GLFW.GLFW_KEY_ENTER: return "ENTER";
            case GLFW.GLFW_KEY_TAB: return "TAB";
            default: return "KEY_" + keyCode;
        }
    }

    // === SCRIPT MANAGEMENT METHODS ===

    private void onExportPressed() {
        RecordedScript currentScript = ScriptRecorderMod.scriptManager.getCurrentScript();
        if (currentScript == null) {
            sendChatMessage("No script to export! Record or load a script first.", true);
            return;
        }

        String scriptName = scriptNameBox.getValue().trim();
        if (scriptName.isEmpty()) {
            sendChatMessage("Please enter a script name for export.", true);
            return;
        }

        // Check if script already exists
        if (ScriptRecorderMod.scriptManager.scriptExists(scriptName)) {
            sendChatMessage("Warning: Script '" + scriptName + "' already exists and will be overwritten.", false);
        }

        // Export using the enhanced method
        if (ScriptRecorderMod.scriptManager.exportScript(currentScript, scriptName)) {
            sendChatMessage("Script exported as '" + scriptName + ".json' (" +
                currentScript.getTotalActions() + " actions, " +
                currentScript.getFormattedDuration() + ")", false);
        } else {
            sendChatMessage("Failed to export script!", true);
        }
    }

    private void onImportPressed() {
        String scriptName = scriptNameBox.getValue().trim();
        if (scriptName.isEmpty()) {
            sendChatMessage("Please enter a script name to import.", true);
            return;
        }

        // Check if script exists before trying to import
        if (!ScriptRecorderMod.scriptManager.scriptExists(scriptName)) {
            sendChatMessage("Script '" + scriptName + "' not found. Available scripts: " +
                String.join(", ", ScriptRecorderMod.scriptManager.getAvailableScripts()), true);
            return;
        }

        RecordedScript loadedScript = ScriptRecorderMod.scriptManager.importScript(scriptName);
        if (loadedScript != null) {
            sendChatMessage("Script '" + scriptName + "' imported successfully! " +
                loadedScript.getTotalActions() + " actions, " +
                loadedScript.getFormattedDuration() + " loaded.", false);
        } else {
            sendChatMessage("Failed to import script '" + scriptName + "'. File may be corrupted.", true);
        }
    }

    // === UTILITY METHODS ===

    private void sendChatMessage(String message, boolean isError) {
        if (minecraft != null && minecraft.player != null) {
            MutableComponent chatMessage = Component.literal("[Toggle Sprint Mod] " + message);
            if (isError) {
                chatMessage = chatMessage.withStyle(style -> style.withColor(0xFF4444));
            } else {
                chatMessage = chatMessage.withStyle(style -> style.withColor(0x44FF44));
            }
            minecraft.player.sendSystemMessage(chatMessage);
        }
    }

    private void updateButtonStates() {
        if (recordButton != null) recordButton.setMessage(getRecordButtonText());
        if (playButton != null) playButton.setMessage(getPlayButtonText());
        if (toggleSprintButton != null) toggleSprintButton.setMessage(getSprintButtonText());
        if (toggleCrouchButton != null) toggleCrouchButton.setMessage(getCrouchButtonText());
        if (toggleKeystrokesButton != null) toggleKeystrokesButton.setMessage(getKeystrokesButtonText());

        // Update button enabled states
        boolean isRecording = ScriptRecorderMod.scriptManager.isRecording();
        boolean isPlaying = ScriptRecorderMod.scriptManager.isPlaying();
        boolean hasScript = ScriptRecorderMod.scriptManager.getCurrentScript() != null;

        if (playButton != null) playButton.active = !isRecording && (hasScript || isPlaying);
        if (stopButton != null) stopButton.active = isRecording || isPlaying;

        // Export button is only active when there's a script to export
        if (exportButton != null) exportButton.active = hasScript && !isRecording && !isPlaying;

        // Import button is always active (unless recording/playing)
        if (importButton != null) importButton.active = !isRecording && !isPlaying;
    }

    @Override
    public void tick() {
        super.tick();

        // Update loop count from text box
        if (loopCountBox != null && !infiniteLoop) {
            try {
                String text = loopCountBox.getValue().trim();
                if (!text.isEmpty() && !text.equals("∞")) {
                    int newCount = Integer.parseInt(text);
                    if (newCount > 0 && newCount <= 999) {
                        loopCount = newCount;
                        ModSettings.setLoopCount(loopCount);
                    }
                }
            } catch (NumberFormatException ignored) {
                // Invalid input, ignore
            }
        }

        updateButtonStates();
    }

    @Override
    public boolean isPauseScreen() {
        return false;
    }
}
