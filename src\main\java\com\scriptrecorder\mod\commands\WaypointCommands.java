package com.scriptrecorder.mod.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.context.CommandContext;
import com.scriptrecorder.mod.client.gui.WaypointManagerScreen;
import net.minecraft.client.Minecraft;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

/**
 * Command to open the waypoint management GUI
 */
@OnlyIn(Dist.CLIENT)
public class WaypointCommands {

    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        // ONLY the legitimate waypoint GUI command
        dispatcher.register(Commands.literal("waypointgui")
            .executes(WaypointCommands::executeWaypointGUI));

        // Register all waypoint commands
        WaypointCommand.register(dispatcher);
    }

    private static int executeWaypointGUI(CommandContext<CommandSourceStack> context) {
        Minecraft mc = Minecraft.getInstance();
        mc.execute(() -> {
            mc.setScreen(new WaypointManagerScreen());
        });
        return 1;
    }
}
