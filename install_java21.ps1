# PowerShell script to install Java 21 for Minecraft 1.20.4 mod development
# This script will download and install Eclipse Temurin OpenJDK 21

Write-Host "=== Java 21 Installation for Minecraft 1.20.4 Mod Development ===" -ForegroundColor Green
Write-Host ""

# Check if Java is already installed
try {
    $javaVersion = java -version 2>&1
    if ($javaVersion -match "21\.") {
        Write-Host "Java 21 is already installed!" -ForegroundColor Green
        Write-Host $javaVersion
        exit 0
    } elseif ($javaVersion) {
        Write-Host "Java is installed but not version 21:" -ForegroundColor Yellow
        Write-Host $javaVersion
        Write-Host "Continuing with Java 21 installation..." -ForegroundColor Yellow
    }
} catch {
    Write-Host "Java not found. Installing Java 21..." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Downloading Eclipse Temurin OpenJDK 21..." -ForegroundColor Cyan

# Download URL for Eclipse Temurin OpenJDK 21 (Windows x64)
$downloadUrl = "https://github.com/adoptium/temurin21-binaries/releases/download/jdk-21.0.1%2B12/OpenJDK21U-jdk_x64_windows_hotspot_21.0.1_12.msi"
$installerPath = "$env:TEMP\OpenJDK21-installer.msi"

try {
    # Download the installer
    Invoke-WebRequest -Uri $downloadUrl -OutFile $installerPath -UseBasicParsing
    Write-Host "Download completed!" -ForegroundColor Green
    
    # Run the installer
    Write-Host "Running installer..." -ForegroundColor Cyan
    Write-Host "Please follow the installation wizard and make sure to:" -ForegroundColor Yellow
    Write-Host "1. Check 'Add to PATH' option" -ForegroundColor Yellow
    Write-Host "2. Check 'Set JAVA_HOME' option" -ForegroundColor Yellow
    
    Start-Process -FilePath "msiexec.exe" -ArgumentList "/i", $installerPath -Wait
    
    # Clean up
    Remove-Item $installerPath -ErrorAction SilentlyContinue
    
    Write-Host ""
    Write-Host "Installation completed!" -ForegroundColor Green
    Write-Host "Please restart your PowerShell/Command Prompt and run:" -ForegroundColor Cyan
    Write-Host "java -version" -ForegroundColor White
    Write-Host ""
    Write-Host "Then you can run the build with:" -ForegroundColor Cyan
    Write-Host "gradlew.bat clean build" -ForegroundColor White
    
} catch {
    Write-Host "Error downloading or installing Java 21: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual installation:" -ForegroundColor Yellow
    Write-Host "1. Go to: https://adoptium.net/temurin/releases/?version=21" -ForegroundColor White
    Write-Host "2. Download the Windows x64 MSI installer" -ForegroundColor White
    Write-Host "3. Run the installer and make sure to check 'Add to PATH' and 'Set JAVA_HOME'" -ForegroundColor White
}

Write-Host ""
Write-Host "After Java 21 is installed, you can build the mod with:" -ForegroundColor Green
Write-Host "gradlew.bat clean build" -ForegroundColor White
