package com.scriptrecorder.mod.client.gui.panel;

import com.scriptrecorder.mod.client.autofisher.AutoFisher;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.network.chat.Component;

import java.awt.Color;

/**
 * Panel for AutoFisher controls
 */
public class AutoFisherPanel extends Panel {
    
    // UI Components
    private Button enableButton;
    private Button modeButton;
    private EditBox bubbleDistanceBox;
    private EditBox timeToWaitBox;
    
    // Colors
    private static final Color ENABLED_COLOR = new Color(50, 220, 50);
    private static final Color DISABLED_COLOR = new Color(220, 50, 50);
    private static final Color DATA_TRACKER_COLOR = new Color(100, 150, 255);
    private static final Color BUBBLES_COLOR = new Color(255, 150, 100);
    
    public AutoFisherPanel() {
        super("AutoFisher", 20, 300, 320, 160);
        initializeComponents();
    }
    
    private void initializeComponents() {
        clearWidgets();
        
        // Calculate positions relative to panel
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Main enable button
        enableButton = Button.builder(
            getEnableButtonText(),
            button -> toggleEnabled())
            .bounds(contentX, contentY, 120, 24)
            .build();
        addWidget(enableButton);
        
        // Mode button
        modeButton = Button.builder(
            getModeButtonText(),
            button -> cycleMode())
            .bounds(contentX + 130, contentY, 120, 24)
            .build();
        addWidget(modeButton);
        
        // Bubble distance input (only for bubbles mode)
        bubbleDistanceBox = new EditBox(Minecraft.getInstance().font, 
                                       contentX, contentY + 35, 80, 20, 
                                       Component.literal("Bubble Distance"));
        bubbleDistanceBox.setValue(String.valueOf(AutoFisher.getInstance().getBubbleDistance()));
        bubbleDistanceBox.setMaxLength(3);
        bubbleDistanceBox.setResponder(this::onBubbleDistanceChanged);
        addWidget(bubbleDistanceBox);
        
        // Time to wait input (only for bubbles mode)
        timeToWaitBox = new EditBox(Minecraft.getInstance().font, 
                                   contentX + 90, contentY + 35, 80, 20, 
                                   Component.literal("Time to Wait"));
        timeToWaitBox.setValue(String.valueOf(AutoFisher.getInstance().getTimeToWait()));
        timeToWaitBox.setMaxLength(4);
        timeToWaitBox.setResponder(this::onTimeToWaitChanged);
        addWidget(timeToWaitBox);
    }
    
    @Override
    protected void renderContent(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Always update component positions
        updateComponentPositions();
        
        // Draw labels and status
        int textX = (int)(x + 10);
        int textY = (int)(y + titleBarHeight + 65);
        
        Minecraft mc = Minecraft.getInstance();
        AutoFisher autoFisher = AutoFisher.getInstance();
        
        // Status information
        String status = autoFisher.isEnabled() ? "ACTIVE" : "INACTIVE";
        Color statusColor = autoFisher.isEnabled() ? ENABLED_COLOR : DISABLED_COLOR;
        guiGraphics.drawString(mc.font, "Status: " + status, 
                             textX, textY, statusColor.getRGB());
        
        // Mode-specific information
        AutoFisher.FishingMode mode = autoFisher.getMode();
        Color modeColor = mode == AutoFisher.FishingMode.DATA_TRACKER ? DATA_TRACKER_COLOR : BUBBLES_COLOR;
        guiGraphics.drawString(mc.font, "Mode: " + mode.getDisplayName(), 
                             textX, textY + 12, modeColor.getRGB());
        
        // Show settings for bubbles mode
        if (mode == AutoFisher.FishingMode.BUBBLES) {
            guiGraphics.drawString(mc.font, "Distance:", textX, textY + 24, Color.WHITE.getRGB());
            guiGraphics.drawString(mc.font, "Wait (ms):", textX + 90, textY + 24, Color.WHITE.getRGB());
        } else {
            // Show data tracker info
            guiGraphics.drawString(mc.font, "Uses vanilla fish detection", 
                                 textX, textY + 24, Color.LIGHT_GRAY.getRGB());
            guiGraphics.drawString(mc.font, "Most reliable method", 
                                 textX, textY + 36, Color.LIGHT_GRAY.getRGB());
        }
        
        // Instructions
        guiGraphics.drawString(mc.font, "Hold fishing rod and stand near water", 
                             textX, textY + 48, Color.GRAY.getRGB());
    }
    
    @Override
    protected void updateComponentPositions() {
        // Calculate positions relative to panel
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Update all component positions
        if (enableButton != null) {
            enableButton.setX(contentX);
            enableButton.setY(contentY);
        }
        if (modeButton != null) {
            modeButton.setX(contentX + 130);
            modeButton.setY(contentY);
        }
        if (bubbleDistanceBox != null) {
            bubbleDistanceBox.setX(contentX);
            bubbleDistanceBox.setY(contentY + 35);
        }
        if (timeToWaitBox != null) {
            timeToWaitBox.setX(contentX + 90);
            timeToWaitBox.setY(contentY + 35);
        }
    }
    
    // Button action methods
    private void toggleEnabled() {
        AutoFisher autoFisher = AutoFisher.getInstance();
        boolean newState = !autoFisher.isEnabled();
        autoFisher.setEnabled(newState);
        
        if (newState) {
            autoFisher.reset();
            sendChatMessage("AutoFisher enabled - " + autoFisher.getMode().getDisplayName() + " mode");
        } else {
            sendChatMessage("AutoFisher disabled");
        }
        
        updateButtonStates();
    }
    
    private void cycleMode() {
        AutoFisher autoFisher = AutoFisher.getInstance();
        AutoFisher.FishingMode currentMode = autoFisher.getMode();
        
        AutoFisher.FishingMode newMode;
        if (currentMode == AutoFisher.FishingMode.DATA_TRACKER) {
            newMode = AutoFisher.FishingMode.BUBBLES;
        } else {
            newMode = AutoFisher.FishingMode.DATA_TRACKER;
        }
        
        autoFisher.setMode(newMode);
        sendChatMessage("AutoFisher mode: " + newMode.getDisplayName());
        updateButtonStates();
    }
    
    private void onBubbleDistanceChanged(String value) {
        try {
            if (!value.trim().isEmpty()) {
                double distance = Double.parseDouble(value.trim());
                AutoFisher.getInstance().setBubbleDistance(distance);
            }
        } catch (NumberFormatException e) {
            // Invalid input, ignore
        }
    }
    
    private void onTimeToWaitChanged(String value) {
        try {
            if (!value.trim().isEmpty()) {
                long time = Long.parseLong(value.trim());
                AutoFisher.getInstance().setTimeToWait(time);
            }
        } catch (NumberFormatException e) {
            // Invalid input, ignore
        }
    }
    
    private void updateButtonStates() {
        if (enableButton != null) enableButton.setMessage(getEnableButtonText());
        if (modeButton != null) modeButton.setMessage(getModeButtonText());
        
        // Update input box visibility based on mode
        AutoFisher.FishingMode mode = AutoFisher.getInstance().getMode();
        boolean showBubbleSettings = mode == AutoFisher.FishingMode.BUBBLES;
        
        if (bubbleDistanceBox != null) {
            bubbleDistanceBox.setVisible(showBubbleSettings);
        }
        if (timeToWaitBox != null) {
            timeToWaitBox.setVisible(showBubbleSettings);
        }
    }
    
    // Button text methods
    private Component getEnableButtonText() {
        boolean enabled = AutoFisher.getInstance().isEnabled();
        return Component.literal("AutoFisher: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getModeButtonText() {
        AutoFisher.FishingMode mode = AutoFisher.getInstance().getMode();
        return Component.literal("Mode: " + mode.getDisplayName());
    }
    
    private void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null) {
            mc.player.displayClientMessage(Component.literal("§7[§bAutoFisher§7] §f" + message), false);
        }
    }
    
    /**
     * Update the panel (called every frame)
     */
    public void update() {
        // Always update component positions first
        updateComponentPositions();
        // Update button states
        updateButtonStates();
    }
}
