package com.scriptrecorder.mod.client.overlay;

import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;
import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.settings.ModSettings;
import com.scriptrecorder.mod.client.gui.PanelBasedGUI;
import net.minecraft.client.Minecraft;
import net.minecraft.client.Camera;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderType;

import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.RenderLevelStageEvent;
import net.minecraftforge.client.event.RenderGuiOverlayEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import org.joml.Matrix4f;
import org.joml.Vector4f;

import java.awt.*;
import java.util.HashMap;
import java.util.Map;

import java.util.ArrayList;
import java.util.List;

/**
 * Tracer and ESP overlay for Minecraft 1.20.4
 * Renders lines to entities (tracers) and entity outlines (ESP)
 */
@Mod.EventBusSubscriber(modid = ScriptRecorderMod.MODID, value = Dist.CLIENT, bus = Mod.EventBusSubscriber.Bus.FORGE)
@OnlyIn(Dist.CLIENT)
public class TracerESPOverlay {

    private static boolean tracersEnabled = false;
    private static boolean espEnabled = false;
    private static double tracerDistance = 50.0;
    private static double espDistance = 30.0;
    private static String tracerFilter = "both";
    private static String espFilter = "both";

    // Colors - made fully opaque for debugging
    private static final float[] TRACER_COLOR = {1.0f, 0.0f, 0.0f, 1.0f}; // Red
    private static final float[] ESP_COLOR = {0.0f, 1.0f, 0.0f, 1.0f}; // Green
    private static final float[] PLAYER_TRACER_COLOR = {1.0f, 1.0f, 0.0f, 1.0f}; // Yellow for players
    private static final float[] PLAYER_ESP_COLOR = {1.0f, 0.5f, 0.0f, 1.0f}; // Orange for players

    // ESP Scan variables
    private static long lastScanTime = 0;
    private static final long SCAN_INTERVAL = 5000; // 5 seconds

    // Optimized ESP Projection Cache system
    private static final Map<Entity, ESPProjection> projectionCache = new HashMap<>();
    private static final Map<Entity, ESPProjection> optimizedCache = new HashMap<>();
    private static long lastCacheUpdate = 0;
    private static final long CACHE_UPDATE_INTERVAL = 16; // ~60 FPS (16ms)
    private static int frameCounter = 0;

    // ESP Projection data class
    private static class ESPProjection {
        public final Rectangle screenRect;
        public final boolean visible;
        public final Vec3 entityPos;

        public ESPProjection(Rectangle screenRect, boolean visible, Vec3 entityPos) {
            this.screenRect = screenRect;
            this.visible = visible;
            this.entityPos = entityPos;
        }
    }

    public static void setTracersEnabled(boolean enabled) {
        tracersEnabled = enabled;
    }

    public static void setESPEnabled(boolean enabled) {
        espEnabled = enabled;
    }

    public static void setTracerDistance(double distance) {
        tracerDistance = distance;
    }

    public static void setESPDistance(double distance) {
        espDistance = distance;
    }

    public static void setTracerFilter(String filter) {
        tracerFilter = filter;
    }

    public static void setESPFilter(String filter) {
        espFilter = filter;
    }

    public static boolean isTracersEnabled() {
        return tracersEnabled;
    }

    public static boolean isESPEnabled() {
        return espEnabled;
    }

    @SubscribeEvent
    public static void onRenderLevel(RenderLevelStageEvent event) {
        if (event.getStage() != RenderLevelStageEvent.Stage.AFTER_LEVEL) {
            return;
        }

        // ESP Scan feature and entity detection
        performESPScan();

        // Note: ESP now uses real-time projection in 2D pass
        // No need for 3D rendering or caching
    }

    /**
     * 3D Pass: Calculate all ESP projections and cache them for 2D rendering
     */
    private static void calculateESPProjections(RenderLevelStageEvent event) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) return;

        // Clear previous cache
        projectionCache.clear();

        // Get matrices for projection
        Matrix4f modelView = event.getPoseStack().last().pose();
        Matrix4f projection = event.getProjectionMatrix();

        Vec3 playerPos = mc.player.position().add(0, mc.player.getEyeHeight(), 0);

        // Process all entities
        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity == mc.player) continue;

            double distance = playerPos.distanceTo(entity.position());

            // Performance optimizations
            if (!shouldRenderEntity(entity, "both")) continue;
            if (distance > Math.max(ModSettings.getTracerDistance(), ModSettings.getESPDistance())) continue;

            // Frustum culling
            AABB boundingBox = entity.getBoundingBox();
            if (!mc.levelRenderer.getFrustum().isVisible(boundingBox)) continue;

            // Calculate screen projection
            ESPProjection espProjection = calculateEntityProjection(modelView, projection, entity);
            if (espProjection != null) {
                projectionCache.put(entity, espProjection);
            }
        }

        sendDebugMessage(mc, "[Debug] Calculated projections for " + projectionCache.size() + " entities");
    }

    /**
     * Legacy immediate mode rendering (keeping for compatibility)
     */
    private static void renderLegacyESP(RenderLevelStageEvent event) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) return;

        long currentTime = System.currentTimeMillis();
        PoseStack poseStack = event.getPoseStack();
        Vec3 cameraPos = mc.gameRenderer.getMainCamera().getPosition();
        Vec3 playerPos = mc.player.getEyePosition();

        // Debug: Count all entities
        int totalEntities = 0;
        int playerCount = 0;
        int mobCount = 0;
        List<Entity> entitiesToRender = new ArrayList<>();

        // Use entity detection range if enabled, otherwise use max of tracer/ESP distances
        double renderDistance = Math.max(tracerDistance, espDistance);
        double scanDistance = ModSettings.getInstance().entityDetectionEnabled ?
            ModSettings.getInstance().entityDetectionRange : renderDistance;

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (!(entity instanceof LivingEntity) || entity == mc.player) {
                continue;
            }

            totalEntities++;
            if (entity instanceof Player) {
                playerCount++;
            } else {
                mobCount++;
            }

            Vec3 entityPos = entity.position().add(0, entity.getBbHeight() / 2, 0);
            double distance = playerPos.distanceTo(entityPos);

            // Add to render list if within ESP/tracer range
            if ((tracersEnabled && distance <= tracerDistance && shouldRenderEntity(entity, ModSettings.getTracerFilter())) ||
                (espEnabled && distance <= espDistance && shouldRenderEntity(entity, ModSettings.getESPFilter()))) {
                entitiesToRender.add(entity);
            }
        }

        // ESP Scan feature - show entity counts every 5 seconds
        if (ModSettings.getInstance().entityDetectionEnabled && currentTime - lastScanTime > SCAN_INTERVAL) {
            lastScanTime = currentTime;
            String message = String.format("[ESP Scan] %d entities (%d players, %d mobs) within %.0f blocks",
                totalEntities, playerCount, mobCount, scanDistance);
            sendDebugMessage(mc, message);
        }

        if (entitiesToRender.isEmpty()) {
            return;
        }

        // Use simple immediate mode rendering to avoid vertex format issues
        sendDebugMessage(mc, "[Debug] About to render " + entitiesToRender.size() + " entities using immediate mode");

        // Setup GL state for immediate mode rendering
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest(); // See through walls
        RenderSystem.lineWidth(2.0f); // Reasonable line width

        // Tracers disabled for now - focusing on ESP glow effect
        // TODO: Implement tracers later if needed

        // Render ESP using immediate mode
        if (espEnabled) {
            sendDebugMessage(mc, "[Debug] Rendering ESP with immediate mode...");
            renderESPImmediate(poseStack, cameraPos, entitiesToRender);
        }

        // Restore GL state
        RenderSystem.lineWidth(1.0f);
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
    }

    private static void sendDebugMessage(Minecraft mc, String message) {
        if (mc.player != null) {
            mc.player.sendSystemMessage(net.minecraft.network.chat.Component.literal(message));
        }
    }

    /**
     * ESP Scan feature - count and report entities
     */
    private static void performESPScan() {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) return;

        long currentTime = System.currentTimeMillis();

        // Only scan if ESP Scan is enabled
        if (!ModSettings.getInstance().entityDetectionEnabled) return;

        // Check if it's time for a scan
        if (currentTime - lastScanTime < SCAN_INTERVAL) return;

        lastScanTime = currentTime;

        Vec3 playerPos = mc.player.position().add(0, mc.player.getEyeHeight(), 0);
        double scanRange = ModSettings.getInstance().entityDetectionRange;

        int totalEntities = 0, playerCount = 0, mobCount = 0;

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity == mc.player) continue;
            if (!(entity instanceof LivingEntity)) continue;

            double distance = playerPos.distanceTo(entity.position());
            if (distance > scanRange) continue;

            totalEntities++;
            if (entity instanceof Player) {
                playerCount++;
            } else {
                mobCount++;
            }
        }

        String message = String.format("[ESP Scan] %d entities (%d players, %d mobs) within %.0f blocks",
            totalEntities, playerCount, mobCount, scanRange);
        sendDebugMessage(mc, message);
    }

    /**
     * 2D Pass: Render ESP overlays with REAL-TIME projection calculation
     */
    @SubscribeEvent
    public static void onRenderGameOverlay(RenderGuiOverlayEvent.Post event) {
        if (!espEnabled) return;

        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) return;

        GuiGraphics guiGraphics = event.getGuiGraphics();

        // Use optimized cached projection system
        Camera camera = mc.gameRenderer.getMainCamera();
        Vec3 playerPos = mc.player.getEyePosition();

        // Check if we should use cached projections or recalculate
        long currentTime = System.currentTimeMillis();
        boolean shouldUpdateCache = (currentTime - lastCacheUpdate) > CACHE_UPDATE_INTERVAL ||
                                   !ModSettings.getInstance().espCachedProjections;

        if (shouldUpdateCache) {
            updateOptimizedProjectionCache(camera, playerPos, mc);
            lastCacheUpdate = currentTime;
        }

        // Use cached projections for rendering
        for (Map.Entry<Entity, ESPProjection> entry : optimizedCache.entrySet()) {
            Entity entity = entry.getKey();
            ESPProjection projection = entry.getValue();

            if (!projection.visible) continue;

            // Final distance check (in case entity moved since cache update)
            double distance = playerPos.distanceTo(entity.position());
            if (distance > ModSettings.getESPDistance()) continue;

            // Use simple world-to-screen projection
            Vec3 entityPos = entity.position().add(0, entity.getBbHeight() / 2, 0);
            Vec3 screenPos = worldToScreen(entityPos, camera, mc);

            if (screenPos == null) continue; // Behind camera or invalid

            // Create a distance-scaled box around the screen position
            int boxSize = Math.max(8, Math.min(50, (int)(200 / Math.max(distance, 5)))); // Better distance scaling
            Rectangle rect = new Rectangle(
                (int)screenPos.x - boxSize/2,
                (int)screenPos.y - boxSize/2,
                boxSize,
                boxSize
            );

            // Validate screen coordinates
            if (rect.x < -100 || rect.y < -100 ||
                rect.x > mc.getWindow().getGuiScaledWidth() + 100 ||
                rect.y > mc.getWindow().getGuiScaledHeight() + 100) {
                continue;
            }

            // Draw ESP box with simple projection
            drawESPBox(guiGraphics, entity, rect);

            // Draw health bar on the side of the entity
            if (entity instanceof LivingEntity) {
                drawHealthBar(guiGraphics, (LivingEntity) entity, rect);
            }

            // Draw distance indicator on top
            drawDistanceIndicator(guiGraphics, entity, rect, distance);

            // Draw entity name (only for players to avoid NPC spam)
            if (entity instanceof Player) {
                drawEntityName(guiGraphics, entity, rect);
                // Draw armor and items for players
                drawPlayerEquipment(guiGraphics, (Player) entity, rect);
            }

            // Draw held items for all living entities (including mobs)
            if (entity instanceof LivingEntity) {
                drawEntityHeldItems(guiGraphics, (LivingEntity) entity, rect);
            }

            // Draw tracer line from center of screen to entity
            drawTracerLine(guiGraphics, rect, mc);
        }
    }

    /**
     * Optimized cache update with frustum culling and distance limiting
     */
    private static void updateOptimizedProjectionCache(Camera camera, Vec3 playerPos, Minecraft mc) {
        optimizedCache.clear();
        frameCounter++;

        // Get current matrices
        Matrix4f modelView = RenderSystem.getModelViewMatrix();
        Matrix4f projection = RenderSystem.getProjectionMatrix();

        int entitiesProcessed = 0;
        int entitiesCulled = 0;

        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity == mc.player) continue;

            // Early entity type filtering
            if (!(entity instanceof LivingEntity)) continue;

            // NPC filtering option
            if (!ModSettings.getInstance().espShowNPCs && !(entity instanceof Player)) continue;

            // Distance culling (early rejection)
            double distance = playerPos.distanceTo(entity.position());
            if (ModSettings.getInstance().espDistanceLimiting &&
                distance > ModSettings.getInstance().espMaxRenderDistance) {
                entitiesCulled++;
                continue;
            }

            // ESP distance check
            if (distance > ModSettings.getESPDistance()) continue;

            // Entity filter check
            if (!shouldRenderEntity(entity, ModSettings.getESPFilter())) continue;

            // Frustum culling (expensive, do last)
            if (ModSettings.getInstance().espFrustumCulling) {
                AABB boundingBox = entity.getBoundingBox();
                if (!mc.levelRenderer.getFrustum().isVisible(boundingBox)) {
                    entitiesCulled++;
                    continue;
                }
            }

            // Calculate projection for this entity
            Vec3 screenPos = worldToScreen(entity.position().add(0, entity.getBbHeight() / 2, 0), camera, mc);
            if (screenPos == null) continue;

            // Create ESP box
            int boxSize = Math.max(8, Math.min(50, (int)(200 / Math.max(distance, 5))));
            Rectangle rect = new Rectangle(
                (int)screenPos.x - boxSize/2,
                (int)screenPos.y - boxSize/2,
                boxSize,
                boxSize
            );

            // Validate screen coordinates
            if (rect.x < -100 || rect.y < -100 ||
                rect.x > mc.getWindow().getGuiScaledWidth() + 100 ||
                rect.y > mc.getWindow().getGuiScaledHeight() + 100) {
                continue;
            }

            optimizedCache.put(entity, new ESPProjection(rect, true, entity.position()));
            entitiesProcessed++;

            // Feed detected players to server pinger for vanish detection
            if (entity instanceof Player) {
                feedPlayerToServerPinger(((Player) entity).getName().getString());
            }
        }

        // Debug output every 60 frames (~1 second at 60 FPS)
        if (frameCounter % 60 == 0) {
            System.out.println("[ESP Optimization] Processed: " + entitiesProcessed +
                             ", Culled: " + entitiesCulled +
                             ", Cache size: " + optimizedCache.size());
        }
    }

    /**
     * Simple world-to-screen projection using Minecraft's built-in methods
     */
    private static Vec3 worldToScreen(Vec3 worldPos, Camera camera, Minecraft mc) {
        try {
            // Get camera position and rotation
            Vec3 cameraPos = camera.getPosition();

            // Calculate relative position
            Vec3 relative = worldPos.subtract(cameraPos);

            // Simple distance check
            double distance = relative.length();
            if (distance < 0.1 || distance > 1000) return null;

            // Get camera direction vectors (convert from Vector3f to Vec3)
            org.joml.Vector3f forwardVec = camera.getLookVector();
            org.joml.Vector3f upVec = camera.getUpVector();

            Vec3 forward = new Vec3(forwardVec.x, forwardVec.y, forwardVec.z);
            Vec3 up = new Vec3(upVec.x, upVec.y, upVec.z);
            Vec3 right = up.cross(forward).normalize();

            // Project to camera space
            double x = relative.dot(right);
            double y = relative.dot(up);
            double z = relative.dot(forward);

            // Check if behind camera
            if (z <= 0) return null;

            // Fixed perspective projection with proper scaling
            double fov = mc.options.fov().get();
            double aspectRatio = (double) mc.getWindow().getGuiScaledWidth() / mc.getWindow().getGuiScaledHeight();

            // More stable projection calculation
            double fovRadians = Math.toRadians(fov);
            double tanHalfFov = Math.tan(fovRadians / 2.0);

            // Project to normalized device coordinates (-1 to 1)
            // INVERT X to make ESP move opposite to mouse (correct behavior)
            // Use distance-based sensitivity correction for better accuracy at screen edges
            double distanceFromCenter = Math.sqrt(x*x + y*y);
            double maxDistance = z * tanHalfFov; // Maximum distance at screen edge
            double distanceRatio = Math.min(distanceFromCenter / maxDistance, 1.0);

            // INCREASE sensitivity to make boxes move more opposite to mouse
            double baseSensitivity = 1.2; // Increased from 0.6 to 1.2 for more movement
            double edgeCorrection = 1.0 - (distanceRatio * 0.2); // Reduce edge penalty
            double sensitivity = baseSensitivity * edgeCorrection;

            double ndcX = -x / (z * tanHalfFov * aspectRatio) * sensitivity;  // Negative X for correct direction
            double ndcY = y / (z * tanHalfFov) * sensitivity;

            // Convert to screen coordinates with edge correction
            double screenX = (ndcX + 1.0) * 0.5 * mc.getWindow().getGuiScaledWidth();
            double screenY = (1.0 - ndcY) * 0.5 * mc.getWindow().getGuiScaledHeight();

            // Apply additional correction for screen edge accuracy
            double centerX = mc.getWindow().getGuiScaledWidth() / 2.0;
            double centerY = mc.getWindow().getGuiScaledHeight() / 2.0;

            // Calculate distance from screen center
            double screenDistanceFromCenter = Math.sqrt(Math.pow(screenX - centerX, 2) + Math.pow(screenY - centerY, 2));
            double maxScreenDistance = Math.sqrt(centerX * centerX + centerY * centerY);
            double screenEdgeRatio = Math.min(screenDistanceFromCenter / maxScreenDistance, 1.0);

            // Apply minimal correction towards center (reduced to allow more movement)
            double correctionFactor = 0.98 + (0.02 * (1.0 - screenEdgeRatio)); // 98-100% accuracy (less correction)
            screenX = centerX + (screenX - centerX) * correctionFactor;
            screenY = centerY + (screenY - centerY) * correctionFactor;

            // Debug output occasionally
            if (Math.random() < 0.001) { // Very rarely
                System.out.println("Projection: world=" + worldPos + " screen=" + screenX + "," + screenY + " z=" + z);
            }

            return new Vec3(screenX, screenY, z);
        } catch (Exception e) {
            return null; // Fallback for any errors
        }
    }

    /**
     * Draw health bar on the side of the entity
     */
    private static void drawHealthBar(GuiGraphics guiGraphics, LivingEntity entity, Rectangle rect) {
        float health = entity.getHealth();
        float maxHealth = entity.getMaxHealth();

        if (maxHealth <= 0) return; // Avoid division by zero

        float healthPercent = health / maxHealth;

        // Health bar dimensions
        int barWidth = 3;
        int barHeight = rect.height;
        int barX = rect.x + rect.width + 2; // Right side of ESP box
        int barY = rect.y;

        // Background (black)
        guiGraphics.fill(barX, barY, barX + barWidth, barY + barHeight, 0xFF000000);

        // Health bar color based on health percentage
        int healthColor;
        if (healthPercent > 0.6f) {
            healthColor = 0xFF00FF00; // Green
        } else if (healthPercent > 0.3f) {
            healthColor = 0xFFFFFF00; // Yellow
        } else {
            healthColor = 0xFFFF0000; // Red
        }

        // Health bar fill
        int fillHeight = (int) (barHeight * healthPercent);
        int fillY = barY + barHeight - fillHeight; // Fill from bottom
        guiGraphics.fill(barX, fillY, barX + barWidth, barY + barHeight, healthColor);
    }

    /**
     * Draw distance indicator on top of entity
     */
    private static void drawDistanceIndicator(GuiGraphics guiGraphics, Entity entity, Rectangle rect, double distance) {
        String distanceText = String.format("%.1fm", distance);

        int textWidth = Minecraft.getInstance().font.width(distanceText);
        int x = rect.x + (rect.width - textWidth) / 2;
        int y = rect.y - 24; // Above the name

        // Ensure it doesn't go off-screen
        Minecraft mc = Minecraft.getInstance();
        x = Math.max(2, Math.min(x, mc.getWindow().getGuiScaledWidth() - textWidth - 2));
        y = Math.max(2, y);

        // Draw background
        guiGraphics.fill(x - 1, y - 1, x + textWidth + 1, y + 8, 0x80000000);

        // Draw distance text in white
        guiGraphics.drawString(mc.font, distanceText, x, y, 0xFFFFFFFF);
    }

    /**
     * Draw tracer line from center of screen to entity
     */
    private static void drawTracerLine(GuiGraphics guiGraphics, Rectangle rect, Minecraft mc) {
        // Center of screen
        int centerX = mc.getWindow().getGuiScaledWidth() / 2;
        int centerY = mc.getWindow().getGuiScaledHeight() / 2;

        // Center of ESP box
        int targetX = rect.x + rect.width / 2;
        int targetY = rect.y + rect.height / 2;

        // Draw line using multiple small fills (simple line drawing)
        drawLine(guiGraphics, centerX, centerY, targetX, targetY, 0xFF00FFFF); // Cyan tracer
    }

    /**
     * Simple line drawing using fills
     */
    private static void drawLine(GuiGraphics guiGraphics, int x1, int y1, int x2, int y2, int color) {
        int dx = Math.abs(x2 - x1);
        int dy = Math.abs(y2 - y1);
        int steps = Math.max(dx, dy);

        if (steps == 0) return;

        float xStep = (float)(x2 - x1) / steps;
        float yStep = (float)(y2 - y1) / steps;

        for (int i = 0; i <= steps; i += 2) { // Draw every other pixel for performance
            int x = x1 + (int)(i * xStep);
            int y = y1 + (int)(i * yStep);
            guiGraphics.fill(x, y, x + 1, y + 1, color);
        }
    }

    /**
     * Draw enhanced ESP box around entity with corner brackets
     */
    private static void drawESPBox(GuiGraphics guiGraphics, Entity entity, Rectangle rect) {
        // Color based on entity type and health
        int color;
        if (entity instanceof Player) {
            color = 0xFFFF8000; // Orange for players
        } else if (entity instanceof LivingEntity) {
            LivingEntity living = (LivingEntity) entity;
            float healthPercent = living.getHealth() / living.getMaxHealth();
            if (healthPercent > 0.6f) {
                color = 0xFF00FF00; // Green for healthy mobs
            } else if (healthPercent > 0.3f) {
                color = 0xFFFFFF00; // Yellow for damaged mobs
            } else {
                color = 0xFFFF0000; // Red for low health mobs
            }
        } else {
            color = 0xFF00FF00; // Default green
        }

        // Draw corner brackets instead of full outline for cleaner look
        int cornerSize = Math.min(rect.width / 4, rect.height / 4);
        cornerSize = Math.max(3, Math.min(cornerSize, 8)); // Limit corner size

        // Top-left corner
        guiGraphics.fill(rect.x, rect.y, rect.x + cornerSize, rect.y + 1, color); // Horizontal
        guiGraphics.fill(rect.x, rect.y, rect.x + 1, rect.y + cornerSize, color); // Vertical

        // Top-right corner
        guiGraphics.fill(rect.x + rect.width - cornerSize, rect.y, rect.x + rect.width, rect.y + 1, color); // Horizontal
        guiGraphics.fill(rect.x + rect.width - 1, rect.y, rect.x + rect.width, rect.y + cornerSize, color); // Vertical

        // Bottom-left corner
        guiGraphics.fill(rect.x, rect.y + rect.height - 1, rect.x + cornerSize, rect.y + rect.height, color); // Horizontal
        guiGraphics.fill(rect.x, rect.y + rect.height - cornerSize, rect.x + 1, rect.y + rect.height, color); // Vertical

        // Bottom-right corner
        guiGraphics.fill(rect.x + rect.width - cornerSize, rect.y + rect.height - 1, rect.x + rect.width, rect.y + rect.height, color); // Horizontal
        guiGraphics.fill(rect.x + rect.width - 1, rect.y + rect.height - cornerSize, rect.x + rect.width, rect.y + rect.height, color); // Vertical
    }

    /**
     * Draw held items for any living entity (including mobs)
     */
    private static void drawEntityHeldItems(GuiGraphics guiGraphics, LivingEntity entity, Rectangle rect) {
        // Skip if it's a player (handled separately)
        if (entity instanceof Player) return;

        int startX = rect.x + rect.width + 5; // Right side of ESP box
        int startY = rect.y;

        // Ensure it doesn't go off-screen
        Minecraft mc = Minecraft.getInstance();
        if (startX > mc.getWindow().getGuiScaledWidth() - 100) {
            startX = rect.x - 80; // Left side if right is off-screen
            if (startX < 5) return; // Skip if both sides are off-screen
        }

        int currentY = startY;

        // Draw main hand item
        if (!entity.getMainHandItem().isEmpty()) {
            String itemName = getItemDisplayName(entity.getMainHandItem());
            drawEquipmentText(guiGraphics, "Hand: " + itemName, startX, currentY, 0xFFFFFFFF);
            currentY += 10;
        }

        // Draw offhand item
        if (!entity.getOffhandItem().isEmpty()) {
            String itemName = getItemDisplayName(entity.getOffhandItem());
            drawEquipmentText(guiGraphics, "Off: " + itemName, startX, currentY, 0xFFFFFF00);
        }
    }

    /**
     * Draw player equipment (armor and held items) on the left side of ESP box
     */
    private static void drawPlayerEquipment(GuiGraphics guiGraphics, Player player, Rectangle rect) {
        Minecraft mc = Minecraft.getInstance();

        // Try left side first, if off-screen use right side
        int startX = rect.x - 80; // Left side of ESP box (increased space)
        if (startX < 5) {
            startX = rect.x + rect.width + 10; // Right side if left is off-screen
        }

        int startY = rect.y;

        // Debug output occasionally
        if (Math.random() < 0.01) { // 1% chance
            System.out.println("Drawing equipment for " + player.getName().getString() + " at " + startX + "," + startY);
        }

        int currentY = startY;

        // Draw held item
        if (!player.getMainHandItem().isEmpty()) {
            String itemName = getItemDisplayName(player.getMainHandItem());
            drawEquipmentText(guiGraphics, "Hand: " + itemName, startX, currentY, 0xFFFFFFFF);
            currentY += 10;
        }

        // Draw armor pieces
        if (!player.getInventory().getArmor(3).isEmpty()) { // Helmet
            String armorName = getItemDisplayName(player.getInventory().getArmor(3));
            drawEquipmentText(guiGraphics, "Head: " + armorName, startX, currentY, 0xFFADD8E6);
            currentY += 10;
        }

        if (!player.getInventory().getArmor(2).isEmpty()) { // Chestplate
            String armorName = getItemDisplayName(player.getInventory().getArmor(2));
            drawEquipmentText(guiGraphics, "Chest: " + armorName, startX, currentY, 0xFFADD8E6);
            currentY += 10;
        }

        if (!player.getInventory().getArmor(1).isEmpty()) { // Leggings
            String armorName = getItemDisplayName(player.getInventory().getArmor(1));
            drawEquipmentText(guiGraphics, "Legs: " + armorName, startX, currentY, 0xFFADD8E6);
            currentY += 10;
        }

        if (!player.getInventory().getArmor(0).isEmpty()) { // Boots
            String armorName = getItemDisplayName(player.getInventory().getArmor(0));
            drawEquipmentText(guiGraphics, "Feet: " + armorName, startX, currentY, 0xFFADD8E6);
            currentY += 10;
        }

        // Draw offhand item
        if (!player.getOffhandItem().isEmpty()) {
            String itemName = getItemDisplayName(player.getOffhandItem());
            drawEquipmentText(guiGraphics, "Off: " + itemName, startX, currentY, 0xFFFFFF00);
        }
    }

    /**
     * Get a clean display name for an item
     */
    private static String getItemDisplayName(net.minecraft.world.item.ItemStack itemStack) {
        String name = itemStack.getDisplayName().getString();
        // Remove color codes and limit length
        name = name.replaceAll("§[0-9a-fk-or]", "");
        if (name.length() > 12) {
            name = name.substring(0, 9) + "...";
        }
        return name;
    }

    /**
     * Draw equipment text with background
     */
    private static void drawEquipmentText(GuiGraphics guiGraphics, String text, int x, int y, int color) {
        Minecraft mc = Minecraft.getInstance();
        int textWidth = mc.font.width(text);

        // Draw more visible background
        guiGraphics.fill(x - 2, y - 1, x + textWidth + 2, y + 9, 0xC0000000); // More opaque background

        // Draw text
        guiGraphics.drawString(mc.font, text, x, y, color);

        // Debug output occasionally
        if (Math.random() < 0.005) { // 0.5% chance
            System.out.println("Drawing equipment text: " + text + " at " + x + "," + y);
        }
    }

    /**
     * Draw entity name above ESP box (only for players to avoid NPC description spam)
     */
    private static void drawEntityName(GuiGraphics guiGraphics, Entity entity, Rectangle rect) {
        // Only show names for players to prevent NPC description clutter
        if (!(entity instanceof Player)) {
            return;
        }

        String name = entity.getDisplayName().getString();

        // Clean up the name - remove formatting codes and limit length
        name = name.replaceAll("§[0-9a-fk-or]", ""); // Remove Minecraft color codes
        if (name.length() > 16) {
            name = name.substring(0, 13) + "...";
        }

        // Skip if name is empty or just whitespace
        if (name.trim().isEmpty()) {
            return;
        }

        int textWidth = Minecraft.getInstance().font.width(name);
        int x = rect.x + (rect.width - textWidth) / 2;
        int y = rect.y - 12;

        // Ensure name doesn't go off-screen
        Minecraft mc = Minecraft.getInstance();
        x = Math.max(2, Math.min(x, mc.getWindow().getGuiScaledWidth() - textWidth - 2));
        y = Math.max(2, y);

        // Draw background
        guiGraphics.fill(x - 2, y - 1, x + textWidth + 2, y + 9, 0x80000000);

        // Draw text
        guiGraphics.drawString(mc.font, name, x, y, 0xFFFFFFFF);
    }

    /**
     * Go back to the 2D projection system that was working (with fixes)
     */
    private static void renderSimpleESP(RenderLevelStageEvent event) {
        // Re-enable the 2D projection system since immediate mode isn't working
        calculateESPProjections(event);
    }

    /**
     * Draw a wireframe box using lines
     */
    private static void drawWireframeBox(BufferBuilder buffer, Matrix4f matrix, AABB box, float[] color) {
        float minX = (float) box.minX;
        float minY = (float) box.minY;
        float minZ = (float) box.minZ;
        float maxX = (float) box.maxX;
        float maxY = (float) box.maxY;
        float maxZ = (float) box.maxZ;

        // Bottom face
        buffer.vertex(matrix, minX, minY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, maxX, minY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();

        buffer.vertex(matrix, maxX, minY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, maxX, minY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();

        buffer.vertex(matrix, maxX, minY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, minX, minY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();

        buffer.vertex(matrix, minX, minY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, minX, minY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();

        // Top face
        buffer.vertex(matrix, minX, maxY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, maxX, maxY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();

        buffer.vertex(matrix, maxX, maxY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();

        buffer.vertex(matrix, maxX, maxY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, minX, maxY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();

        buffer.vertex(matrix, minX, maxY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, minX, maxY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();

        // Vertical edges
        buffer.vertex(matrix, minX, minY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, minX, maxY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();

        buffer.vertex(matrix, maxX, minY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, maxX, maxY, minZ).color(color[0], color[1], color[2], color[3]).endVertex();

        buffer.vertex(matrix, maxX, minY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, maxX, maxY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();

        buffer.vertex(matrix, minX, minY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, minX, maxY, maxZ).color(color[0], color[1], color[2], color[3]).endVertex();
    }

    /**
     * Project 3D world coordinates to 2D screen coordinates
     * Fixed to match reference implementation exactly - camera translation BEFORE matrix multiplication
     */
    private static ProjectionResult project(Matrix4f modelView, Matrix4f projection, Vec3 worldPos) {
        Minecraft mc = Minecraft.getInstance();

        // Step 1: Convert to camera-relative coordinates FIRST (like reference)
        Vec3 cameraPos = mc.gameRenderer.getMainCamera().getPosition();
        Vec3 camRelative = worldPos.subtract(cameraPos);

        // Step 2: Apply matrices to camera-relative coordinates (like reference)
        Vector4f vec = new Vector4f((float) camRelative.x, (float) camRelative.y, (float) camRelative.z, 1.0f);
        vec.mul(modelView);    // View transformation
        vec.mul(projection);   // Projection transformation

        // Step 3: Perspective divide and screen conversion (like reference)
        if (vec.w <= 0.0f) {
            return new ProjectionResult(new Vec3(0, 0, 0), false); // Behind camera
        }

        float invW = 1.0f / vec.w;

        // Convert to NDC then screen space (matching reference exactly)
        Vec3 screenPos = new Vec3(
            (vec.x * invW + 1.0f) * 0.5f * mc.getWindow().getGuiScaledWidth(),
            (1.0f - vec.y * invW) * 0.5f * mc.getWindow().getGuiScaledHeight(),
            vec.z * invW
        );

        return new ProjectionResult(screenPos, true);
    }

    private static class ProjectionResult {
        public final Vec3 screenPos;
        public final boolean visible;

        public ProjectionResult(Vec3 screenPos, boolean visible) {
            this.screenPos = screenPos;
            this.visible = visible;
        }
    }

    /**
     * Calculate entity projection and create bounding rectangle
     * Uses proper interpolation for smooth movement tracking
     */
    private static ESPProjection calculateEntityProjection(Matrix4f modelView, Matrix4f projection, Entity entity) {
        // Get interpolated position for smooth movement (key fix for sync issues)
        float partialTicks = Minecraft.getInstance().getFrameTime();
        Vec3 interpolatedPos = entity.getPosition(partialTicks);

        // Calculate interpolated bounding box
        AABB boundingBox = entity.getBoundingBox();
        Vec3 offset = interpolatedPos.subtract(entity.position());
        AABB interpolatedBox = boundingBox.move(offset.x, offset.y, offset.z);

        Vec3[] corners = getBoundingBoxCorners(interpolatedBox);

        double minX = Double.MAX_VALUE, minY = Double.MAX_VALUE;
        double maxX = Double.MIN_VALUE, maxY = Double.MIN_VALUE;
        boolean anyVisible = false;

        // Project all 8 corners and find screen bounding rectangle
        for (Vec3 corner : corners) {
            ProjectionResult result = project(modelView, projection, corner);
            if (result.visible) {
                anyVisible = true;
            }

            minX = Math.min(minX, result.screenPos.x);
            minY = Math.min(minY, result.screenPos.y);
            maxX = Math.max(maxX, result.screenPos.x);
            maxY = Math.max(maxY, result.screenPos.y);
        }

        // Ensure minimum size for visibility
        double width = Math.max(maxX - minX, 8);
        double height = Math.max(maxY - minY, 8);

        Rectangle screenRect = new Rectangle(
            (int) minX, (int) minY,
            (int) width, (int) height
        );

        return new ESPProjection(screenRect, anyVisible, interpolatedPos);
    }

    /**
     * Get all 8 corners of a bounding box
     */
    private static Vec3[] getBoundingBoxCorners(AABB box) {
        return new Vec3[] {
            new Vec3(box.minX, box.minY, box.minZ),
            new Vec3(box.maxX, box.minY, box.minZ),
            new Vec3(box.minX, box.maxY, box.minZ),
            new Vec3(box.maxX, box.maxY, box.minZ),
            new Vec3(box.minX, box.minY, box.maxZ),
            new Vec3(box.maxX, box.minY, box.maxZ),
            new Vec3(box.minX, box.maxY, box.maxZ),
            new Vec3(box.maxX, box.maxY, box.maxZ)
        };
    }

    /**
     * 2D Pass: Render ESP overlays using cached projections
     */
    @SubscribeEvent
    public static void onRenderOverlay(RenderGuiOverlayEvent.Post event) {
        if (!espEnabled || projectionCache.isEmpty()) return;

        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null) return;

        GuiGraphics guiGraphics = event.getGuiGraphics();

        for (Map.Entry<Entity, ESPProjection> entry : projectionCache.entrySet()) {
            Entity entity = entry.getKey();
            ESPProjection projection = entry.getValue();

            if (!projection.visible) continue;

            // Draw ESP box
            drawESPBox(guiGraphics, entity, projection.screenRect);

            // Draw entity name
            drawEntityName(guiGraphics, entity, projection.screenRect);
        }

        sendDebugMessage(mc, "[Debug] Rendered 2D ESP for " + projectionCache.size() + " entities");
    }



    /**
     * Simple immediate mode tracer rendering using basic Tesselator
     */
    private static void renderTracersImmediate(RenderLevelStageEvent event) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) return;

        PoseStack poseStack = event.getPoseStack();
        Vec3 cameraPos = mc.gameRenderer.getMainCamera().getPosition();
        Vec3 playerPos = mc.player.getEyePosition();

        // Collect entities to render tracers for
        List<Entity> entitiesToRender = new ArrayList<>();
        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity == mc.player) continue;
            if (!(entity instanceof LivingEntity)) continue;

            double distance = playerPos.distanceTo(entity.position());
            if (distance > ModSettings.getTracerDistance()) continue;
            if (!shouldRenderEntity(entity, ModSettings.getTracerFilter())) continue;

            entitiesToRender.add(entity);
        }

        if (entitiesToRender.isEmpty()) return;
        poseStack.pushPose();
        poseStack.translate(-cameraPos.x, -cameraPos.y, -cameraPos.z);

        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder buffer = tesselator.getBuilder();
        Matrix4f matrix = poseStack.last().pose();

        buffer.begin(VertexFormat.Mode.LINES, DefaultVertexFormat.POSITION_COLOR);

        for (Entity entity : entitiesToRender) {
            Vec3 entityPos = entity.position().add(0, entity.getBbHeight() / 2, 0);
            float[] color = (entity instanceof Player) ? PLAYER_TRACER_COLOR : TRACER_COLOR;

            // Draw line from player to entity
            buffer.vertex(matrix, (float) playerPos.x, (float) playerPos.y, (float) playerPos.z)
                    .color(color[0], color[1], color[2], color[3]).endVertex();
            buffer.vertex(matrix, (float) entityPos.x, (float) entityPos.y, (float) entityPos.z)
                    .color(color[0], color[1], color[2], color[3]).endVertex();
        }

        // Set shader right before drawing
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        BufferUploader.drawWithShader(buffer.end());

        poseStack.popPose();
    }

    /**
     * Simple immediate mode ESP rendering using basic Tesselator
     */
    private static void renderESPImmediate(PoseStack poseStack, Vec3 cameraPos, List<Entity> entities) {
        poseStack.pushPose();
        poseStack.translate(-cameraPos.x, -cameraPos.y, -cameraPos.z);

        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder buffer = tesselator.getBuilder();
        Matrix4f matrix = poseStack.last().pose();

        buffer.begin(VertexFormat.Mode.LINES, DefaultVertexFormat.POSITION_COLOR);

        for (Entity entity : entities) {
            AABB boundingBox = entity.getBoundingBox();
            float[] color = (entity instanceof Player) ? PLAYER_ESP_COLOR : ESP_COLOR;

            drawBoundingBoxOutlineSimple(buffer, matrix, boundingBox, color);
        }

        // Set shader right before drawing
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        BufferUploader.drawWithShader(buffer.end());

        poseStack.popPose();
    }



    private static void renderAllTracers(PoseStack poseStack, Vec3 cameraPos, Vec3 playerPos, List<Entity> entities, MultiBufferSource.BufferSource bufferSource) {
        poseStack.pushPose();

        // Translate to camera-relative coordinates
        poseStack.translate(-cameraPos.x, -cameraPos.y, -cameraPos.z);

        // Use RenderType.lines() for proper Forge pipeline integration
        VertexConsumer buffer = bufferSource.getBuffer(RenderType.lines());
        Matrix4f matrix = poseStack.last().pose(); // Get matrix AFTER translation

        for (Entity entity : entities) {
            Vec3 entityPos = entity.position().add(0, entity.getBbHeight() / 2, 0);

            // Get the appropriate color based on entity type
            float[] color = (entity instanceof Player) ? PLAYER_TRACER_COLOR : TRACER_COLOR;

            sendDebugMessage(Minecraft.getInstance(), "[Debug] Drawing tracer to " + entity.getClass().getSimpleName());

            // Use world coordinates directly (pose stack is already translated)
            buffer.vertex(matrix, (float) playerPos.x, (float) playerPos.y, (float) playerPos.z)
                    .color(color[0], color[1], color[2], color[3])
                    .endVertex();

            buffer.vertex(matrix, (float) entityPos.x, (float) entityPos.y, (float) entityPos.z)
                    .color(color[0], color[1], color[2], color[3])
                    .endVertex();
        }

        sendDebugMessage(Minecraft.getInstance(), "[Debug] Tracer vertices added to buffer");
        poseStack.popPose();
    }

    private static void renderAllESP(PoseStack poseStack, Vec3 cameraPos, List<Entity> entities, MultiBufferSource.BufferSource bufferSource) {
        poseStack.pushPose();

        // Translate to camera-relative coordinates
        poseStack.translate(-cameraPos.x, -cameraPos.y, -cameraPos.z);

        // Use RenderType.lines() for proper Forge pipeline integration
        VertexConsumer buffer = bufferSource.getBuffer(RenderType.lines());
        Matrix4f matrix = poseStack.last().pose(); // Get matrix AFTER translation

        // Draw a test box at a fixed location for debugging
        AABB testBox = new AABB(0, 64, 0, 1, 65, 1); // Just above bedrock
        drawBoundingBoxOutline(buffer, matrix, testBox, new float[]{1.0f, 1.0f, 1.0f, 1.0f}); // White box
        sendDebugMessage(Minecraft.getInstance(), "[Debug] Drew test box at (0,64,0)");

        for (Entity entity : entities) {
            // Get the appropriate color based on entity type
            float[] color = (entity instanceof Player) ? PLAYER_ESP_COLOR : ESP_COLOR;

            AABB boundingBox = entity.getBoundingBox();

            sendDebugMessage(Minecraft.getInstance(), "[Debug] Drawing ESP box for " + entity.getClass().getSimpleName());

            // Use world coordinates directly (pose stack is already translated)
            drawBoundingBoxOutline(buffer, matrix, boundingBox, color);
        }

        sendDebugMessage(Minecraft.getInstance(), "[Debug] ESP vertices added to buffer");
        poseStack.popPose();
    }

    private static void drawBoundingBoxOutline(VertexConsumer buffer, Matrix4f matrix, AABB box, float[] color) {
        float minX = (float) box.minX;
        float minY = (float) box.minY;
        float minZ = (float) box.minZ;
        float maxX = (float) box.maxX;
        float maxY = (float) box.maxY;
        float maxZ = (float) box.maxZ;

        // Bottom face
        addLine(buffer, matrix, minX, minY, minZ, maxX, minY, minZ, color);
        addLine(buffer, matrix, maxX, minY, minZ, maxX, minY, maxZ, color);
        addLine(buffer, matrix, maxX, minY, maxZ, minX, minY, maxZ, color);
        addLine(buffer, matrix, minX, minY, maxZ, minX, minY, minZ, color);

        // Top face
        addLine(buffer, matrix, minX, maxY, minZ, maxX, maxY, minZ, color);
        addLine(buffer, matrix, maxX, maxY, minZ, maxX, maxY, maxZ, color);
        addLine(buffer, matrix, maxX, maxY, maxZ, minX, maxY, maxZ, color);
        addLine(buffer, matrix, minX, maxY, maxZ, minX, maxY, minZ, color);

        // Vertical edges
        addLine(buffer, matrix, minX, minY, minZ, minX, maxY, minZ, color);
        addLine(buffer, matrix, maxX, minY, minZ, maxX, maxY, minZ, color);
        addLine(buffer, matrix, maxX, minY, maxZ, maxX, maxY, maxZ, color);
        addLine(buffer, matrix, minX, minY, maxZ, minX, maxY, maxZ, color);
    }

    private static void addLine(VertexConsumer buffer, Matrix4f matrix, float x1, float y1, float z1, float x2, float y2, float z2, float[] color) {
        buffer.vertex(matrix, x1, y1, z1)
                .color(color[0], color[1], color[2], color[3])
                .endVertex();

        buffer.vertex(matrix, x2, y2, z2)
                .color(color[0], color[1], color[2], color[3])
                .endVertex();
    }

    /**
     * Simple bounding box outline drawing for BufferBuilder
     */
    private static void drawBoundingBoxOutlineSimple(BufferBuilder buffer, Matrix4f matrix, AABB box, float[] color) {
        float minX = (float) box.minX;
        float minY = (float) box.minY;
        float minZ = (float) box.minZ;
        float maxX = (float) box.maxX;
        float maxY = (float) box.maxY;
        float maxZ = (float) box.maxZ;

        // Bottom face
        addLineSimple(buffer, matrix, minX, minY, minZ, maxX, minY, minZ, color);
        addLineSimple(buffer, matrix, maxX, minY, minZ, maxX, minY, maxZ, color);
        addLineSimple(buffer, matrix, maxX, minY, maxZ, minX, minY, maxZ, color);
        addLineSimple(buffer, matrix, minX, minY, maxZ, minX, minY, minZ, color);

        // Top face
        addLineSimple(buffer, matrix, minX, maxY, minZ, maxX, maxY, minZ, color);
        addLineSimple(buffer, matrix, maxX, maxY, minZ, maxX, maxY, maxZ, color);
        addLineSimple(buffer, matrix, maxX, maxY, maxZ, minX, maxY, maxZ, color);
        addLineSimple(buffer, matrix, minX, maxY, maxZ, minX, maxY, minZ, color);

        // Vertical edges
        addLineSimple(buffer, matrix, minX, minY, minZ, minX, maxY, minZ, color);
        addLineSimple(buffer, matrix, maxX, minY, minZ, maxX, maxY, minZ, color);
        addLineSimple(buffer, matrix, maxX, minY, maxZ, maxX, maxY, maxZ, color);
        addLineSimple(buffer, matrix, minX, minY, maxZ, minX, maxY, maxZ, color);
    }

    private static void addLineSimple(BufferBuilder buffer, Matrix4f matrix, float x1, float y1, float z1, float x2, float y2, float z2, float[] color) {
        buffer.vertex(matrix, x1, y1, z1)
                .color(color[0], color[1], color[2], color[3])
                .endVertex();

        buffer.vertex(matrix, x2, y2, z2)
                .color(color[0], color[1], color[2], color[3])
                .endVertex();
    }

    /**
     * Feed detected player to server pinger for vanish detection
     */
    private static void feedPlayerToServerPinger(String playerName) {
        try {
            // Try to get the current GUI and feed the player to server pinger
            Minecraft mc = Minecraft.getInstance();
            if (mc.screen instanceof PanelBasedGUI) {
                PanelBasedGUI gui = (PanelBasedGUI) mc.screen;
                if (gui.getPanelManager().getPanel("Server Pinger") != null) {
                    // This would require exposing the server pinger panel
                    // For now, we'll implement this integration later
                }
            }
        } catch (Exception e) {
            // Silently ignore errors to avoid spam
        }
    }

    /**
     * Check if an entity should be rendered based on the filter
     */
    private static boolean shouldRenderEntity(Entity entity, String filter) {
        switch (filter) {
            case "players":
                return entity instanceof Player;
            case "mobs":
                return entity instanceof LivingEntity && !(entity instanceof Player);
            case "both":
            default:
                return entity instanceof LivingEntity;
        }
    }

    /**
     * Load settings from ModSettings
     */
    public static void loadSettings() {
        ModSettings settings = ModSettings.getInstance();
        tracersEnabled = settings.tracersEnabled;
        espEnabled = settings.espEnabled;
        tracerDistance = settings.tracerDistance;
        espDistance = settings.espDistance;
        tracerFilter = settings.tracerFilter;
        espFilter = settings.espFilter;
    }

    /**
     * Call this when the config GUI is closed to refresh settings
     */
    public static void refreshSettings() {
        loadSettings();
    }
}
