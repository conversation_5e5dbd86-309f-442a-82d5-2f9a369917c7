package com.scriptrecorder.mod.client.gui;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.ScriptRecorderClient;
import com.scriptrecorder.mod.client.overlay.KeystrokeOverlay;
import com.scriptrecorder.mod.client.overlay.TracerESPOverlay;
import com.scriptrecorder.mod.client.settings.ModSettings;
import com.scriptrecorder.mod.recording.RecordedScript;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import org.lwjgl.glfw.GLFW;

import java.util.List;

/**
 * New tabbed GUI for Script Recorder with separate tabs for different functionality
 */
@OnlyIn(Dist.CLIENT)
public class TabbedScriptRecorderScreen extends Screen {

    // GUI Dimensions and Colors
    private static final int GUI_WIDTH = 450;
    private static final int GUI_HEIGHT = 350;
    private static final int TAB_HEIGHT = 25;
    private static final int PANEL_COLOR = 0xE0000000;
    private static final int BORDER_COLOR = 0xFF404040;
    private static final int ACCENT_COLOR = 0xFF00AA00;
    private static final int ERROR_COLOR = 0xFFFF4444;
    private static final int TEXT_COLOR = 0xFFFFFFFF;
    private static final int TAB_ACTIVE_COLOR = 0xFF2A2A2A;
    private static final int TAB_INACTIVE_COLOR = 0xFF1A1A1A;

    // Tab system
    private enum Tab {
        RECORDER("Routes"),
        SCRIPT_MANAGER("Waypoints"),
        SETTINGS("Settings"),
        TRACERS_ESP("Display");

        private final String displayName;
        Tab(String displayName) { this.displayName = displayName; }
        public String getDisplayName() { return displayName; }
    }

    private Tab currentTab = Tab.RECORDER;

    // Common components
    private Button[] tabButtons = new Button[Tab.values().length];

    // Recorder tab components
    private Button recordButton;
    private Button playButton;
    private Button stopButton;
    private EditBox loopCountBox;
    private Button infiniteLoopButton;

    // Script Manager tab components
    private EditBox scriptNameBox;
    private Button exportButton;
    private Button importButton;
    private Button deleteButton;
    private Button listScriptsButton;
    private String[] availableScripts = new String[0];
    private int selectedScriptIndex = -1;

    // Settings tab components
    private Button toggleSprintButton;
    private Button toggleCrouchButton;
    private Button toggleKeystrokesButton;

    // Tracers/ESP tab components
    private Button toggleTracersButton;
    private Button toggleESPButton;
    private EditBox tracerDistanceBox;
    private EditBox espDistanceBox;
    private Button tracerFilterButton;
    private Button espFilterButton;

    // Entity Detection components
    private Button toggleEntityDetectionButton;
    private EditBox entityDetectionRangeBox;
    private Button toggleShowNPCsButton;

    // State
    private int loopCount = 1;
    private boolean infiniteLoop = false;
    private boolean waitingForKeybind = false;
    private String keybindWaitingFor = "";

    public TabbedScriptRecorderScreen() {
        super(Component.literal("Advanced Configuration"));
    }

    @Override
    protected void init() {
        super.init();

        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int guiLeft = centerX - GUI_WIDTH / 2;
        int guiTop = centerY - GUI_HEIGHT / 2;

        // Create tab buttons
        createTabButtons(guiLeft, guiTop);

        // Update tab button states
        updateTabButtonStates();

        // Initialize current tab
        initCurrentTab(guiLeft, guiTop);

        // Load settings
        loadSettings();
    }

    private void createTabButtons(int guiLeft, int guiTop) {
        Tab[] tabs = Tab.values();
        int tabWidth = GUI_WIDTH / tabs.length;

        for (int i = 0; i < tabs.length; i++) {
            final Tab tab = tabs[i];

            // Create tab button with clear positioning
            String buttonText = tab.getDisplayName();
            if (tab == currentTab) {
                buttonText = "» " + buttonText + " «";
            }

            tabButtons[i] = Button.builder(
                Component.literal(buttonText),
                button -> switchTab(tab))
                .bounds(guiLeft + i * tabWidth + 2, guiTop + 2, tabWidth - 4, TAB_HEIGHT - 4)
                .build();

            this.addRenderableWidget(tabButtons[i]);
        }
    }

    private void switchTab(Tab newTab) {
        if (currentTab == newTab) return;

        // Clear current tab components
        clearTabComponents();

        currentTab = newTab;

        // Update tab button states
        updateTabButtonStates();

        // Initialize new tab
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int guiLeft = centerX - GUI_WIDTH / 2;
        int guiTop = centerY - GUI_HEIGHT / 2;

        initCurrentTab(guiLeft, guiTop);
    }

    private void updateTabButtonStates() {
        Tab[] tabs = Tab.values();
        for (int i = 0; i < tabs.length && i < tabButtons.length; i++) {
            if (tabButtons[i] != null) {
                // Update button message to show active state
                String displayName = tabs[i].getDisplayName();
                if (tabs[i] == currentTab) {
                    displayName = "» " + displayName + " «"; // Mark active tab
                }
                tabButtons[i].setMessage(Component.literal(displayName));
            }
        }
    }

    private void clearTabComponents() {
        // Remove all widgets except tab buttons
        this.children().removeIf(widget -> {
            for (Button tabButton : tabButtons) {
                if (widget == tabButton) return false;
            }
            return true;
        });
        this.renderables.removeIf(widget -> {
            for (Button tabButton : tabButtons) {
                if (widget == tabButton) return false;
            }
            return true;
        });
    }

    private void initCurrentTab(int guiLeft, int guiTop) {
        int contentTop = guiTop + TAB_HEIGHT + 10;

        switch (currentTab) {
            case RECORDER:
                initRecorderTab(guiLeft, contentTop);
                break;
            case SCRIPT_MANAGER:
                initScriptManagerTab(guiLeft, contentTop);
                break;
            case SETTINGS:
                initSettingsTab(guiLeft, contentTop);
                break;
            case TRACERS_ESP:
                initTracersEspTab(guiLeft, contentTop);
                break;
        }

        updateButtonStates();
    }

    private void initRecorderTab(int guiLeft, int contentTop) {
        // Recording controls
        recordButton = Button.builder(
            getRecordButtonText(),
            button -> onRecordPressed())
            .bounds(guiLeft + 20, contentTop + 20, 100, 24)
            .build();
        this.addRenderableWidget(recordButton);

        playButton = Button.builder(
            getPlayButtonText(),
            button -> onPlayPressed())
            .bounds(guiLeft + 140, contentTop + 20, 100, 24)
            .build();
        this.addRenderableWidget(playButton);

        stopButton = Button.builder(
            Component.literal("Stop"),
            button -> onStopPressed())
            .bounds(guiLeft + 260, contentTop + 20, 100, 24)
            .build();
        this.addRenderableWidget(stopButton);

        // Loop controls
        loopCountBox = new EditBox(this.font, guiLeft + 20, contentTop + 60, 60, 20, Component.literal("Loop Count"));
        loopCountBox.setValue(infiniteLoop ? "∞" : String.valueOf(loopCount));
        loopCountBox.setMaxLength(3);
        this.addRenderableWidget(loopCountBox);

        infiniteLoopButton = Button.builder(
            Component.literal(infiniteLoop ? "∞ ON" : "∞ OFF"),
            button -> toggleInfiniteLoop())
            .bounds(guiLeft + 90, contentTop + 60, 50, 20)
            .build();
        this.addRenderableWidget(infiniteLoopButton);
    }

    private void initScriptManagerTab(int guiLeft, int contentTop) {
        // Script name input
        scriptNameBox = new EditBox(this.font, guiLeft + 20, contentTop + 20, 200, 20, Component.literal("Script Name"));
        scriptNameBox.setValue("farmscript");
        scriptNameBox.setMaxLength(50);
        this.addRenderableWidget(scriptNameBox);

        // Export/Import buttons
        exportButton = Button.builder(
            Component.literal("Export"),
            button -> onExportPressed())
            .bounds(guiLeft + 230, contentTop + 20, 70, 20)
            .build();
        this.addRenderableWidget(exportButton);

        importButton = Button.builder(
            Component.literal("Import"),
            button -> onImportPressed())
            .bounds(guiLeft + 310, contentTop + 20, 70, 20)
            .build();
        this.addRenderableWidget(importButton);

        // Delete button
        deleteButton = Button.builder(
            Component.literal("Delete"),
            button -> onDeletePressed())
            .bounds(guiLeft + 230, contentTop + 50, 70, 20)
            .build();
        this.addRenderableWidget(deleteButton);

        // List scripts button
        listScriptsButton = Button.builder(
            Component.literal("List Scripts"),
            button -> onListScriptsPressed())
            .bounds(guiLeft + 310, contentTop + 50, 70, 20)
            .build();
        this.addRenderableWidget(listScriptsButton);

        // Load available scripts
        refreshScriptList();
    }

    private void initSettingsTab(int guiLeft, int contentTop) {
        // Toggle features
        toggleSprintButton = Button.builder(
            getSprintButtonText(),
            button -> toggleSprint())
            .bounds(guiLeft + 20, contentTop + 20, 120, 20)
            .build();
        this.addRenderableWidget(toggleSprintButton);

        toggleCrouchButton = Button.builder(
            getCrouchButtonText(),
            button -> toggleCrouch())
            .bounds(guiLeft + 160, contentTop + 20, 120, 20)
            .build();
        this.addRenderableWidget(toggleCrouchButton);

        toggleKeystrokesButton = Button.builder(
            getKeystrokesButtonText(),
            button -> toggleKeystrokes())
            .bounds(guiLeft + 300, contentTop + 20, 120, 20)
            .build();
        this.addRenderableWidget(toggleKeystrokesButton);
    }

    private void initTracersEspTab(int guiLeft, int contentTop) {
        // Tracers toggle
        toggleTracersButton = Button.builder(
            getTracersButtonText(),
            button -> toggleTracers())
            .bounds(guiLeft + 20, contentTop + 20, 100, 20)
            .build();
        this.addRenderableWidget(toggleTracersButton);

        // ESP toggle
        toggleESPButton = Button.builder(
            getESPButtonText(),
            button -> toggleESP())
            .bounds(guiLeft + 140, contentTop + 20, 100, 20)
            .build();
        this.addRenderableWidget(toggleESPButton);

        // Tracer filter button
        tracerFilterButton = Button.builder(
            getTracerFilterButtonText(),
            button -> cycleTracerFilter())
            .bounds(guiLeft + 260, contentTop + 20, 80, 20)
            .build();
        this.addRenderableWidget(tracerFilterButton);

        // ESP filter button
        espFilterButton = Button.builder(
            getESPFilterButtonText(),
            button -> cycleESPFilter())
            .bounds(guiLeft + 360, contentTop + 20, 80, 20)
            .build();
        this.addRenderableWidget(espFilterButton);

        // Tracer distance input
        tracerDistanceBox = new EditBox(this.font, guiLeft + 20, contentTop + 60, 80, 20, Component.literal("Tracer Distance"));
        tracerDistanceBox.setValue(String.valueOf((int) ModSettings.getTracerDistance()));
        tracerDistanceBox.setMaxLength(3);
        this.addRenderableWidget(tracerDistanceBox);

        // ESP distance input
        espDistanceBox = new EditBox(this.font, guiLeft + 140, contentTop + 60, 80, 20, Component.literal("ESP Distance"));
        espDistanceBox.setValue(String.valueOf((int) ModSettings.getESPDistance()));
        espDistanceBox.setMaxLength(3);
        this.addRenderableWidget(espDistanceBox);

        // Entity Detection toggle
        toggleEntityDetectionButton = Button.builder(
            getEntityDetectionButtonText(),
            button -> toggleEntityDetection())
            .bounds(guiLeft + 20, contentTop + 100, 120, 20)
            .build();
        this.addRenderableWidget(toggleEntityDetectionButton);

        // Entity Detection range input
        entityDetectionRangeBox = new EditBox(this.font, guiLeft + 160, contentTop + 100, 80, 20, Component.literal("Detection Range"));
        entityDetectionRangeBox.setValue(String.valueOf((int) ModSettings.getInstance().entityDetectionRange));
        entityDetectionRangeBox.setMaxLength(4);
        this.addRenderableWidget(entityDetectionRangeBox);

        // Show NPCs toggle
        toggleShowNPCsButton = Button.builder(
            getShowNPCsButtonText(),
            button -> toggleShowNPCs())
            .bounds(guiLeft + 260, contentTop + 100, 100, 20)
            .build();
        this.addRenderableWidget(toggleShowNPCsButton);
    }

    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        this.renderBackground(guiGraphics, mouseX, mouseY, partialTick);

        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int guiLeft = centerX - GUI_WIDTH / 2;
        int guiTop = centerY - GUI_HEIGHT / 2;

        // Draw title
        String title = "Advanced Configuration";
        guiGraphics.drawCenteredString(this.font, title, centerX, guiTop - 20, ACCENT_COLOR);

        // Draw main panel background (below tabs)
        guiGraphics.fill(guiLeft, guiTop + TAB_HEIGHT, guiLeft + GUI_WIDTH, guiTop + GUI_HEIGHT, PANEL_COLOR);

        // Draw tab backgrounds (but NOT over the buttons)
        drawTabBackgrounds(guiGraphics, guiLeft, guiTop);

        // Draw border
        drawBorder(guiGraphics, guiLeft, guiTop, GUI_WIDTH, GUI_HEIGHT, BORDER_COLOR);

        // Draw tab-specific content
        drawTabContent(guiGraphics, guiLeft, guiTop);

        // Render all widgets (including tab buttons) - this must be LAST
        super.render(guiGraphics, mouseX, mouseY, partialTick);

        // Draw tab labels on top of buttons for extra visibility
        drawTabLabels(guiGraphics, guiLeft, guiTop);
    }

    private void drawTabBackgrounds(GuiGraphics guiGraphics, int guiLeft, int guiTop) {
        Tab[] tabs = Tab.values();
        int tabWidth = GUI_WIDTH / tabs.length;

        for (int i = 0; i < tabs.length; i++) {
            int tabLeft = guiLeft + i * tabWidth;
            int color = (tabs[i] == currentTab) ? TAB_ACTIVE_COLOR : TAB_INACTIVE_COLOR;

            // Draw tab background (only the border area, not over buttons)
            guiGraphics.fill(tabLeft, guiTop, tabLeft + tabWidth, guiTop + 1, color); // Top border
            guiGraphics.fill(tabLeft, guiTop, tabLeft + 1, guiTop + TAB_HEIGHT, color); // Left border
            guiGraphics.fill(tabLeft + tabWidth - 1, guiTop, tabLeft + tabWidth, guiTop + TAB_HEIGHT, color); // Right border

            // Draw tab separator
            if (i > 0) {
                guiGraphics.fill(tabLeft, guiTop, tabLeft + 1, guiTop + TAB_HEIGHT, BORDER_COLOR);
            }
        }
    }

    private void drawTabLabels(GuiGraphics guiGraphics, int guiLeft, int guiTop) {
        Tab[] tabs = Tab.values();
        int tabWidth = GUI_WIDTH / tabs.length;

        for (int i = 0; i < tabs.length; i++) {
            int tabLeft = guiLeft + i * tabWidth;
            int textX = tabLeft + tabWidth / 2;
            int textY = guiTop + TAB_HEIGHT / 2 - 4;

            String tabName = tabs[i].getDisplayName();
            int textColor = (tabs[i] == currentTab) ? ACCENT_COLOR : TEXT_COLOR;

            // Draw tab name centered
            guiGraphics.drawCenteredString(this.font, tabName, textX, textY, textColor);
        }
    }

    private void drawTabContent(GuiGraphics guiGraphics, int guiLeft, int guiTop) {
        int contentTop = guiTop + TAB_HEIGHT + 10;

        switch (currentTab) {
            case RECORDER:
                drawRecorderTabContent(guiGraphics, guiLeft, contentTop);
                break;
            case SCRIPT_MANAGER:
                drawScriptManagerTabContent(guiGraphics, guiLeft, contentTop);
                break;
            case SETTINGS:
                drawSettingsTabContent(guiGraphics, guiLeft, contentTop);
                break;
            case TRACERS_ESP:
                drawTracersEspTabContent(guiGraphics, guiLeft, contentTop);
                break;
        }
    }

    private void drawRecorderTabContent(GuiGraphics guiGraphics, int guiLeft, int contentTop) {
        // Draw route info
        RecordedScript script = ScriptRecorderMod.scriptManager.getCurrentScript();
        if (script != null) {
            String info = String.format("Current Route: %d waypoints | %s",
                script.getTotalActions(), script.getFormattedDuration());
            guiGraphics.drawString(this.font, info, guiLeft + 20, contentTop + 100, TEXT_COLOR);
        }

        // Draw status
        String status;
        int statusColor;
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            status = "RECORDING ROUTE";
            statusColor = ERROR_COLOR;
        } else if (ScriptRecorderMod.scriptManager.isPlaying()) {
            status = "FOLLOWING ROUTE";
            statusColor = ACCENT_COLOR;
        } else {
            status = "READY";
            statusColor = TEXT_COLOR;
        }

        guiGraphics.drawString(this.font, "Status: " + status, guiLeft + 20, contentTop + 120, statusColor);

        // Draw loop info
        String loopInfo = infiniteLoop ? "Loops: ∞" : "Loops: " + loopCount;
        guiGraphics.drawString(this.font, loopInfo, guiLeft + 200, contentTop + 120, TEXT_COLOR);
    }

    private void drawScriptManagerTabContent(GuiGraphics guiGraphics, int guiLeft, int contentTop) {
        // Draw waypoint name label
        guiGraphics.drawString(this.font, "Waypoint Name:", guiLeft + 20, contentTop + 10, TEXT_COLOR);

        // Draw available scripts
        guiGraphics.drawString(this.font, "Available Scripts:", guiLeft + 20, contentTop + 80, TEXT_COLOR);

        int yOffset = 100;
        for (int i = 0; i < Math.min(availableScripts.length, 8); i++) {
            int color = (i == selectedScriptIndex) ? ACCENT_COLOR : TEXT_COLOR;
            guiGraphics.drawString(this.font, "• " + availableScripts[i], guiLeft + 20, contentTop + yOffset, color);
            yOffset += 12;
        }

        if (availableScripts.length > 8) {
            guiGraphics.drawString(this.font, "... and " + (availableScripts.length - 8) + " more",
                guiLeft + 20, contentTop + yOffset, 0xFF888888);
        }
    }

    private void drawSettingsTabContent(GuiGraphics guiGraphics, int guiLeft, int contentTop) {
        // Draw settings info
        guiGraphics.drawString(this.font, "Waypoint Settings:", guiLeft + 20, contentTop + 10, TEXT_COLOR);

        // Draw keybind info
        guiGraphics.drawString(this.font, "Configure movement and display options", guiLeft + 20, contentTop + 60, 0xFF888888);
        guiGraphics.drawString(this.font, "Route Recording: F6 | Stop: F7 | Play: F8", guiLeft + 20, contentTop + 80, 0xFF888888);
    }

    private void drawTracersEspTabContent(GuiGraphics guiGraphics, int guiLeft, int contentTop) {
        // Draw section headers
        guiGraphics.drawString(this.font, "Waypoint Display:", guiLeft + 20, contentTop + 10, TEXT_COLOR);

        // Draw distance labels
        guiGraphics.drawString(this.font, "Range:", guiLeft + 20, contentTop + 50, TEXT_COLOR);
        guiGraphics.drawString(this.font, "Range:", guiLeft + 140, contentTop + 50, TEXT_COLOR);

        // Draw current distance values
        String tracerDist = tracerDistanceBox != null ? tracerDistanceBox.getValue() : String.valueOf((int) ModSettings.getTracerDistance());
        String espDist = espDistanceBox != null ? espDistanceBox.getValue() : String.valueOf((int) ModSettings.getESPDistance());

        guiGraphics.drawString(this.font, "(" + tracerDist + ")", guiLeft + 110, contentTop + 63, 0xFF888888);
        guiGraphics.drawString(this.font, "(" + espDist + ")", guiLeft + 230, contentTop + 63, 0xFF888888);

        // Draw ESP scan section
        guiGraphics.drawString(this.font, "ESP Scan:", guiLeft + 20, contentTop + 90, TEXT_COLOR);
        guiGraphics.drawString(this.font, "Range:", guiLeft + 260, contentTop + 90, TEXT_COLOR);

        // Draw current detection range
        String detectionRange = entityDetectionRangeBox != null ? entityDetectionRangeBox.getValue() : String.valueOf((int) ModSettings.getInstance().entityDetectionRange);
        guiGraphics.drawString(this.font, "(" + detectionRange + ")", guiLeft + 250, contentTop + 103, 0xFF888888);

        // Draw filter info
        guiGraphics.drawString(this.font, "Filter options: All -> Players -> Mobs", guiLeft + 20, contentTop + 130, 0xFF888888);
        guiGraphics.drawString(this.font, "Enhanced navigation and entity tracking", guiLeft + 20, contentTop + 145, 0xFF888888);
    }

    // === BUTTON ACTION METHODS ===

    private void onRecordPressed() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            RecordedScript script = ScriptRecorderMod.scriptManager.stopRecording();
            sendChatMessage("Recording stopped. " +
                (script != null ? script.getTotalActions() + " actions captured." : ""), false);
        } else {
            if (ScriptRecorderMod.scriptManager.startRecording("recorded_script")) {
                sendChatMessage("Recording started. Press F7 to stop.", false);
            } else {
                sendChatMessage("Failed to start recording!", true);
            }
        }
        updateButtonStates();
    }

    private void onPlayPressed() {
        if (ScriptRecorderMod.scriptManager.isPlaying()) {
            ScriptRecorderMod.scriptManager.stopPlayback();
            sendChatMessage("Playback stopped.", false);
        } else {
            RecordedScript currentScript = ScriptRecorderMod.scriptManager.getCurrentScript();
            if (currentScript != null && currentScript.isValid()) {
                // Update loop count from input
                updateLoopCountFromInput();
                currentScript.setLoopCount(infiniteLoop ? 0 : loopCount);

                ScriptRecorderMod.scriptManager.startPlayback(currentScript);
                sendChatMessage("Playback started.", false);
            } else {
                sendChatMessage("No valid script to play!", true);
            }
        }
        updateButtonStates();
    }

    private void onStopPressed() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            onRecordPressed(); // Stop recording
        } else if (ScriptRecorderMod.scriptManager.isPlaying()) {
            onPlayPressed(); // Stop playback
        }
    }

    private void onExportPressed() {
        RecordedScript currentScript = ScriptRecorderMod.scriptManager.getCurrentScript();
        if (currentScript == null) {
            sendChatMessage("No script to export! Record or load a script first.", true);
            return;
        }

        String scriptName = scriptNameBox.getValue().trim();
        if (scriptName.isEmpty()) {
            sendChatMessage("Please enter a script name for export.", true);
            return;
        }

        // Check if script already exists
        if (ScriptRecorderMod.scriptManager.scriptExists(scriptName)) {
            sendChatMessage("Warning: Script '" + scriptName + "' already exists and will be overwritten.", false);
        }

        // Export using the enhanced method
        if (ScriptRecorderMod.scriptManager.exportScript(currentScript, scriptName)) {
            sendChatMessage("Script exported as '" + scriptName + ".json' (" +
                currentScript.getTotalActions() + " actions, " +
                currentScript.getFormattedDuration() + ")", false);
            refreshScriptList();
        } else {
            sendChatMessage("Failed to export script!", true);
        }
    }

    private void onImportPressed() {
        String scriptName = scriptNameBox.getValue().trim();
        if (scriptName.isEmpty()) {
            sendChatMessage("Please enter a script name to import.", true);
            return;
        }

        // Check if script exists before trying to import
        if (!ScriptRecorderMod.scriptManager.scriptExists(scriptName)) {
            sendChatMessage("Script '" + scriptName + "' not found. Available scripts: " +
                String.join(", ", ScriptRecorderMod.scriptManager.getAvailableScripts()), true);
            return;
        }

        RecordedScript loadedScript = ScriptRecorderMod.scriptManager.importScript(scriptName);
        if (loadedScript != null) {
            sendChatMessage("Script '" + scriptName + "' imported successfully! " +
                loadedScript.getTotalActions() + " actions, " +
                loadedScript.getFormattedDuration() + " loaded.", false);
        } else {
            sendChatMessage("Failed to import script '" + scriptName + "'. File may be corrupted.", true);
        }
        updateButtonStates();
    }

    private void onDeletePressed() {
        String scriptName = scriptNameBox.getValue().trim();
        if (scriptName.isEmpty()) {
            sendChatMessage("Please enter a script name to delete.", true);
            return;
        }

        if (ScriptRecorderMod.scriptManager.deleteScript(scriptName)) {
            sendChatMessage("Script '" + scriptName + "' deleted successfully.", false);
            refreshScriptList();
        } else {
            sendChatMessage("Failed to delete script '" + scriptName + "'. File not found.", true);
        }
    }

    private void onListScriptsPressed() {
        refreshScriptList();
        List<String> scripts = ScriptRecorderMod.scriptManager.getAvailableScripts();
        if (scripts.isEmpty()) {
            sendChatMessage("No scripts found.", false);
        } else {
            sendChatMessage("Available scripts: " + String.join(", ", scripts), false);
        }
    }

    // === TOGGLE METHODS ===

    private void toggleSprint() {
        boolean newState = !ScriptRecorderClient.isSprintToggled();
        ScriptRecorderClient.setSprintToggled(newState);
        updateButtonStates();
    }

    private void toggleCrouch() {
        boolean newState = !ScriptRecorderClient.isCrouchToggled();
        ScriptRecorderClient.setCrouchToggled(newState);
        updateButtonStates();
    }

    private void toggleKeystrokes() {
        boolean newState = !ModSettings.isKeystrokeDisplayEnabled();
        ModSettings.setKeystrokeDisplayEnabled(newState);
        updateButtonStates();
    }

    private void toggleTracers() {
        boolean newState = !ModSettings.isTracersEnabled();
        ModSettings.setTracersEnabled(newState);
        TracerESPOverlay.setTracersEnabled(newState);
        updateButtonStates();
    }

    private void toggleESP() {
        boolean newState = !ModSettings.isESPEnabled();
        ModSettings.setESPEnabled(newState);
        TracerESPOverlay.setESPEnabled(newState);
        updateButtonStates();
    }

    private void toggleEntityDetection() {
        boolean newState = !ModSettings.getInstance().entityDetectionEnabled;
        ModSettings.getInstance().entityDetectionEnabled = newState;
        updateButtonStates();
    }

    private void toggleShowNPCs() {
        boolean newState = !ModSettings.getInstance().espShowNPCs;
        ModSettings.getInstance().espShowNPCs = newState;
        updateButtonStates();
    }

    private void cycleTracerFilter() {
        String currentFilter = ModSettings.getTracerFilter();
        String newFilter = switch (currentFilter) {
            case "both" -> "players";
            case "players" -> "mobs";
            case "mobs" -> "both";
            default -> "both";
        };
        ModSettings.setTracerFilter(newFilter);
        TracerESPOverlay.setTracerFilter(newFilter);
        updateButtonStates();
    }

    private void cycleESPFilter() {
        String currentFilter = ModSettings.getESPFilter();
        String newFilter = switch (currentFilter) {
            case "both" -> "players";
            case "players" -> "mobs";
            case "mobs" -> "both";
            default -> "both";
        };
        ModSettings.setESPFilter(newFilter);
        TracerESPOverlay.setESPFilter(newFilter);
        updateButtonStates();
    }

    private void toggleInfiniteLoop() {
        infiniteLoop = !infiniteLoop;
        ModSettings.setInfiniteLoop(infiniteLoop);

        if (infiniteLoop) {
            loopCountBox.setValue("∞");
            loopCountBox.setEditable(false);
        } else {
            loopCountBox.setValue(String.valueOf(loopCount));
            loopCountBox.setEditable(true);
        }

        infiniteLoopButton.setMessage(Component.literal(infiniteLoop ? "∞ ON" : "∞ OFF"));
    }

    // === UTILITY METHODS ===

    private void refreshScriptList() {
        List<String> scripts = ScriptRecorderMod.scriptManager.getAvailableScripts();
        availableScripts = scripts.toArray(new String[0]);
        selectedScriptIndex = -1;
    }

    private void updateLoopCountFromInput() {
        if (!infiniteLoop && loopCountBox != null) {
            try {
                String value = loopCountBox.getValue().trim();
                if (!value.isEmpty() && !value.equals("∞")) {
                    loopCount = Math.max(1, Math.min(999, Integer.parseInt(value)));
                    ModSettings.setLoopCount(loopCount);
                }
            } catch (NumberFormatException e) {
                loopCount = 1;
                loopCountBox.setValue("1");
            }
        }
    }

    private void loadSettings() {
        ModSettings settings = ModSettings.getInstance();
        loopCount = settings.loopCount;
        infiniteLoop = settings.infiniteLoop;
    }

    private void sendChatMessage(String message, boolean isError) {
        if (minecraft != null && minecraft.player != null) {
            MutableComponent chatMessage = Component.literal("[Toggle Sprint Mod] " + message);
            if (isError) {
                chatMessage = chatMessage.withStyle(style -> style.withColor(0xFF4444));
            } else {
                chatMessage = chatMessage.withStyle(style -> style.withColor(0x44FF44));
            }
            minecraft.player.sendSystemMessage(chatMessage);
        }
    }

    private void updateButtonStates() {
        boolean isRecording = ScriptRecorderMod.scriptManager.isRecording();
        boolean isPlaying = ScriptRecorderMod.scriptManager.isPlaying();
        boolean hasScript = ScriptRecorderMod.scriptManager.getCurrentScript() != null;

        // Update recorder tab buttons
        if (recordButton != null) recordButton.setMessage(getRecordButtonText());
        if (playButton != null) {
            playButton.setMessage(getPlayButtonText());
            playButton.active = !isRecording && (hasScript || isPlaying);
        }
        if (stopButton != null) stopButton.active = isRecording || isPlaying;

        // Update script manager buttons
        if (exportButton != null) exportButton.active = hasScript && !isRecording && !isPlaying;
        if (importButton != null) importButton.active = !isRecording && !isPlaying;
        if (deleteButton != null) deleteButton.active = !isRecording && !isPlaying;
        if (listScriptsButton != null) listScriptsButton.active = !isRecording && !isPlaying;

        // Update settings buttons
        if (toggleSprintButton != null) toggleSprintButton.setMessage(getSprintButtonText());
        if (toggleCrouchButton != null) toggleCrouchButton.setMessage(getCrouchButtonText());
        if (toggleKeystrokesButton != null) toggleKeystrokesButton.setMessage(getKeystrokesButtonText());

        // Update tracers/ESP buttons
        if (toggleTracersButton != null) toggleTracersButton.setMessage(getTracersButtonText());
        if (toggleESPButton != null) toggleESPButton.setMessage(getESPButtonText());
        if (tracerFilterButton != null) tracerFilterButton.setMessage(getTracerFilterButtonText());
        if (espFilterButton != null) espFilterButton.setMessage(getESPFilterButtonText());
        if (toggleEntityDetectionButton != null) toggleEntityDetectionButton.setMessage(getEntityDetectionButtonText());
        if (toggleShowNPCsButton != null) toggleShowNPCsButton.setMessage(getShowNPCsButtonText());
    }

    // === BUTTON TEXT METHODS ===

    private Component getRecordButtonText() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            return Component.literal("Stop Recording");
        }
        return Component.literal("Record");
    }

    private Component getPlayButtonText() {
        if (ScriptRecorderMod.scriptManager.isPlaying()) {
            return Component.literal("Stop Playing");
        }
        return Component.literal("Play");
    }

    private Component getSprintButtonText() {
        return Component.literal("Sprint: " + (ScriptRecorderClient.isSprintToggled() ? "ON" : "OFF"));
    }

    private Component getCrouchButtonText() {
        return Component.literal("Crouch: " + (ScriptRecorderClient.isCrouchToggled() ? "ON" : "OFF"));
    }

    private Component getKeystrokesButtonText() {
        return Component.literal("Keystrokes: " + (ModSettings.isKeystrokeDisplayEnabled() ? "ON" : "OFF"));
    }

    private Component getTracersButtonText() {
        return Component.literal("Lines: " + (ModSettings.isTracersEnabled() ? "ON" : "OFF"));
    }

    private Component getESPButtonText() {
        return Component.literal("Boxes: " + (ModSettings.isESPEnabled() ? "ON" : "OFF"));
    }

    private Component getTracerFilterButtonText() {
        String filter = ModSettings.getTracerFilter();
        String displayText = filter.equals("both") ? "All" :
                           filter.equals("players") ? "P" : "M";
        return Component.literal("L:" + displayText);
    }

    private Component getESPFilterButtonText() {
        String filter = ModSettings.getESPFilter();
        String displayText = filter.equals("both") ? "All" :
                           filter.equals("players") ? "P" : "M";
        return Component.literal("B:" + displayText);
    }

    private Component getEntityDetectionButtonText() {
        return Component.literal("ESP Scan: " + (ModSettings.getInstance().entityDetectionEnabled ? "ON" : "OFF"));
    }

    private Component getShowNPCsButtonText() {
        return Component.literal("NPCs: " + (ModSettings.getInstance().espShowNPCs ? "ON" : "OFF"));
    }

    // === DISTANCE UPDATE METHODS ===

    private void updateTracerDistance() {
        if (tracerDistanceBox != null) {
            try {
                double distance = Double.parseDouble(tracerDistanceBox.getValue());
                ModSettings.setTracerDistance(distance);
                TracerESPOverlay.setTracerDistance(distance);
            } catch (NumberFormatException e) {
                // Invalid input, ignore
            }
        }
    }

    private void updateESPDistance() {
        if (espDistanceBox != null) {
            try {
                double distance = Double.parseDouble(espDistanceBox.getValue());
                ModSettings.setESPDistance(distance);
                TracerESPOverlay.setESPDistance(distance);
            } catch (NumberFormatException e) {
                // Invalid input, ignore
            }
        }
    }

    private void updateEntityDetectionRange() {
        if (entityDetectionRangeBox != null) {
            try {
                double range = Double.parseDouble(entityDetectionRangeBox.getValue());
                ModSettings.getInstance().entityDetectionRange = range;
            } catch (NumberFormatException e) {
                // Invalid input, ignore
            }
        }
    }

    private void drawBorder(GuiGraphics guiGraphics, int x, int y, int width, int height, int color) {
        guiGraphics.fill(x, y, x + width, y + 1, color); // Top
        guiGraphics.fill(x, y + height - 1, x + width, y + height, color); // Bottom
        guiGraphics.fill(x, y, x + 1, y + height, color); // Left
        guiGraphics.fill(x + width - 1, y, x + width, y + height, color); // Right
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle script list selection in Script Manager tab
        if (currentTab == Tab.SCRIPT_MANAGER && availableScripts.length > 0) {
            int centerX = this.width / 2;
            int centerY = this.height / 2;
            int guiLeft = centerX - GUI_WIDTH / 2;
            int guiTop = centerY - GUI_HEIGHT / 2;
            int contentTop = guiTop + TAB_HEIGHT + 10;

            int listStartY = contentTop + 100;
            int listEndY = listStartY + Math.min(availableScripts.length, 8) * 12;

            if (mouseX >= guiLeft + 20 && mouseX <= guiLeft + 200 &&
                mouseY >= listStartY && mouseY <= listEndY) {

                int clickedIndex = (int) ((mouseY - listStartY) / 12);
                if (clickedIndex >= 0 && clickedIndex < availableScripts.length) {
                    selectedScriptIndex = clickedIndex;
                    scriptNameBox.setValue(availableScripts[clickedIndex]);
                }
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            this.onClose();
            return true;
        }

        // Handle text input for edit boxes
        if (scriptNameBox != null && scriptNameBox.isFocused()) {
            return scriptNameBox.keyPressed(keyCode, scanCode, modifiers);
        }
        if (loopCountBox != null && loopCountBox.isFocused()) {
            return loopCountBox.keyPressed(keyCode, scanCode, modifiers);
        }
        if (tracerDistanceBox != null && tracerDistanceBox.isFocused()) {
            boolean result = tracerDistanceBox.keyPressed(keyCode, scanCode, modifiers);
            updateTracerDistance();
            return result;
        }
        if (espDistanceBox != null && espDistanceBox.isFocused()) {
            boolean result = espDistanceBox.keyPressed(keyCode, scanCode, modifiers);
            updateESPDistance();
            return result;
        }
        if (entityDetectionRangeBox != null && entityDetectionRangeBox.isFocused()) {
            boolean result = entityDetectionRangeBox.keyPressed(keyCode, scanCode, modifiers);
            updateEntityDetectionRange();
            return result;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean charTyped(char codePoint, int modifiers) {
        if (scriptNameBox != null && scriptNameBox.isFocused()) {
            return scriptNameBox.charTyped(codePoint, modifiers);
        }
        if (loopCountBox != null && loopCountBox.isFocused()) {
            return loopCountBox.charTyped(codePoint, modifiers);
        }
        if (tracerDistanceBox != null && tracerDistanceBox.isFocused()) {
            boolean result = tracerDistanceBox.charTyped(codePoint, modifiers);
            updateTracerDistance();
            return result;
        }
        if (espDistanceBox != null && espDistanceBox.isFocused()) {
            boolean result = espDistanceBox.charTyped(codePoint, modifiers);
            updateESPDistance();
            return result;
        }
        if (entityDetectionRangeBox != null && entityDetectionRangeBox.isFocused()) {
            boolean result = entityDetectionRangeBox.charTyped(codePoint, modifiers);
            updateEntityDetectionRange();
            return result;
        }

        return super.charTyped(codePoint, modifiers);
    }

    @Override
    public void tick() {
        super.tick();
        updateButtonStates();
    }

    @Override
    public boolean isPauseScreen() {
        return false;
    }

    @Override
    public void onClose() {
        // Save settings when closing
        ModSettings.getInstance().save();
        // Refresh overlay settings
        TracerESPOverlay.refreshSettings();
        super.onClose();
    }
}
