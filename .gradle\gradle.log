WARNING: You are using an unsupported version of ForgeGradle.
Please consider upgrading to ForgeGradle 5 and helping in the efforts to get old versions working on the modern toolchain.
See https://gist.github.com/TheCurle/fe7ad3ede188cbdd15c235cc75d52d4a for more info on contributing.
#################################################
         ForgeGradle 2.1.3-g6a7c715        
  https://github.com/MinecraftForge/ForgeGradle  
#################################################
               Powered by MCP unknown               
             http://modcoderpack.com             
         by: <PERSON><PERSON>, Prof<PERSON><PERSON>ius, Fesh0<PERSON>,         
         R4wk, Z<PERSON><PERSON>, Ingis<PERSON>ahn, bspkrs           
#################################################
:deobfCompileDummyTask
:deobfProvidedDummyTask
:sourceApiJava
:compileApiJava UP-TO-DATE
:processApiResources UP-TO-DATE
:apiClasses UP-TO-DATE
:sourceMainJava
:compileJava
:processResources
:classes
:jar
:sourceTestJava
:compileTestJava UP-TO-DATE
:processTestResources UP-TO-DATE
:testClasses UP-TO-DATE
:test UP-TO-DATE
:extractMcpData SKIPPED
:extractMcpMappings SKIPPED
:getVersionJson
:extractUserdev UP-TO-DATE
:genSrgs SKIPPED
:reobfJar
:extractRangemapReplacedMain
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java
:retromapReplacedMain
remapping source...
:sourceJar
:assemble
:check UP-TO-DATE
:build

BUILD SUCCESSFUL

Total time: 12.469 secs
