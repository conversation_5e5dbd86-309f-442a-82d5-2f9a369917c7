WARNING: You are using an unsupported version of ForgeGradle.
Please consider upgrading to ForgeGradle 5 and helping in the efforts to get old versions working on the modern toolchain.
See https://gist.github.com/TheCurle/fe7ad3ede188cbdd15c235cc75d52d4a for more info on contributing.
#################################################
         ForgeGradle 2.1.3-g6a7c715        
  https://github.com/MinecraftForge/ForgeGradle  
#################################################
               Powered by MCP unknown               
             http://modcoderpack.com             
         by: Searge, ProfMobius, Fesh0r,         
         R4wk, ZeuX, IngisKahn, bspkrs           
#################################################
:deobfCompileDummyTask
:deobfProvidedDummyTask
:sourceApiJava
:compileApiJava UP-TO-DATE
:processApiResources UP-TO-DATE
:apiClasses UP-TO-DATE
:sourceMainJava
:compileJavaC:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:3: error: package com.mojang.brigadier does not exist
import com.mojang.brigadier.CommandDispatcher;
                           ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:4: error: package com.mojang.brigadier.arguments does not exist
import com.mojang.brigadier.arguments.StringArgumentType;
                                     ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:5: error: package com.mojang.brigadier.context does not exist
import com.mojang.brigadier.context.CommandContext;
                                   ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:10: error: package net.minecraft.commands does not exist
import net.minecraft.commands.CommandSourceStack;
                             ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:11: error: package net.minecraft.commands does not exist
import net.minecraft.commands.Commands;
                             ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:12: error: package net.minecraft.network.chat does not exist
import net.minecraft.network.chat.Component;
                                 ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:13: error: package net.minecraft.world.phys does not exist
import net.minecraft.world.phys.Vec3;
                               ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:14: error: package net.minecraftforge.api.distmarker does not exist
import net.minecraftforge.api.distmarker.Dist;
                                        ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:15: error: package net.minecraftforge.api.distmarker does not exist
import net.minecraftforge.api.distmarker.OnlyIn;
                                        ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:22: error: cannot find symbol
@OnlyIn(Dist.CLIENT)
 ^
  symbol: class OnlyIn
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:25: error: cannot find symbol
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
                                ^
  symbol:   class CommandDispatcher
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:25: error: cannot find symbol
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
                                                  ^
  symbol:   class CommandSourceStack
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:43: error: cannot find symbol
    private static int addWaypoint(CommandContext<CommandSourceStack> context) {
                                   ^
  symbol:   class CommandContext
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:43: error: cannot find symbol
    private static int addWaypoint(CommandContext<CommandSourceStack> context) {
                                                  ^
  symbol:   class CommandSourceStack
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:56: error: cannot find symbol
    private static int removeWaypoint(CommandContext<CommandSourceStack> context) {
                                      ^
  symbol:   class CommandContext
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:56: error: cannot find symbol
    private static int removeWaypoint(CommandContext<CommandSourceStack> context) {
                                                     ^
  symbol:   class CommandSourceStack
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:69: error: cannot find symbol
    private static int listWaypoints(CommandContext<CommandSourceStack> context) {
                                     ^
  symbol:   class CommandContext
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:69: error: cannot find symbol
    private static int listWaypoints(CommandContext<CommandSourceStack> context) {
                                                    ^
  symbol:   class CommandSourceStack
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:91: error: cannot find symbol
    private static int clearWaypoints(CommandContext<CommandSourceStack> context) {
                                      ^
  symbol:   class CommandContext
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:91: error: cannot find symbol
    private static int clearWaypoints(CommandContext<CommandSourceStack> context) {
                                                     ^
  symbol:   class CommandSourceStack
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:3: error: package com.mojang.brigadier does not exist
import com.mojang.brigadier.CommandDispatcher;
                           ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:4: error: package com.mojang.brigadier.context does not exist
import com.mojang.brigadier.context.CommandContext;
                                   ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:5: error: cannot find symbol
import com.scriptrecorder.mod.client.gui.WaypointManagerScreen;
                                        ^
  symbol:   class WaypointManagerScreen
  location: package com.scriptrecorder.mod.client.gui
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:7: error: package net.minecraft.commands does not exist
import net.minecraft.commands.CommandSourceStack;
                             ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:8: error: package net.minecraft.commands does not exist
import net.minecraft.commands.Commands;
                             ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:9: error: package net.minecraft.network.chat does not exist
import net.minecraft.network.chat.Component;
                                 ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:10: error: package net.minecraftforge.api.distmarker does not exist
import net.minecraftforge.api.distmarker.Dist;
                                        ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:11: error: package net.minecraftforge.api.distmarker does not exist
import net.minecraftforge.api.distmarker.OnlyIn;
                                        ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:16: error: cannot find symbol
@OnlyIn(Dist.CLIENT)
 ^
  symbol: class OnlyIn
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:19: error: cannot find symbol
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
                                ^
  symbol:   class CommandDispatcher
  location: class WaypointCommands
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:19: error: cannot find symbol
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
                                                  ^
  symbol:   class CommandSourceStack
  location: class WaypointCommands
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:28: error: cannot find symbol
    private static int executeWaypointGUI(CommandContext<CommandSourceStack> context) {
                                          ^
  symbol:   class CommandContext
  location: class WaypointCommands
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:28: error: cannot find symbol
    private static int executeWaypointGUI(CommandContext<CommandSourceStack> context) {
                                                         ^
  symbol:   class CommandSourceStack
  location: class WaypointCommands
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:7: error: cannot find symbol
import net.minecraft.client.KeyMapping;
                           ^
  symbol:   class KeyMapping
  location: package net.minecraft.client
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:8: error: package org.lwjgl.glfw does not exist
import org.lwjgl.glfw.GLFW;
                     ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\ScriptRecorderMod.java:4: error: cannot find symbol
import com.scriptrecorder.mod.client.ScriptRecorderClient;
                                    ^
  symbol:   class ScriptRecorderClient
  location: package com.scriptrecorder.mod.client
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\waypoints\Waypoint.java:4: error: package net.minecraft.world.phys does not exist
import net.minecraft.world.phys.Vec3;
                               ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\waypoints\Waypoint.java:66: error: cannot find symbol
    public Vec3 getPosition() {
           ^
  symbol:   class Vec3
  location: class Waypoint
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\waypoints\Waypoint.java:70: error: cannot find symbol
    public void setPosition(Vec3 pos) {
                            ^
  symbol:   class Vec3
  location: class Waypoint
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\waypoints\Waypoint.java:76: error: cannot find symbol
    public double getDistanceTo(Vec3 pos) {
                                ^
  symbol:   class Vec3
  location: class Waypoint
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\waypoints\Waypoint.java:84: error: cannot find symbol
    public String getFormattedDistance(Vec3 playerPos) {
                                       ^
  symbol:   class Vec3
  location: class Waypoint
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:37: error: cannot find symbol
            .then(Commands.literal("clear")
                  ^
  symbol:   variable Commands
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:35: error: cannot find symbol
            .then(Commands.literal("list")
                  ^
  symbol:   variable Commands
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:33: error: cannot find symbol
                .then(Commands.argument("name", StringArgumentType.greedyString())
                                                ^
  symbol:   variable StringArgumentType
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:33: error: cannot find symbol
                .then(Commands.argument("name", StringArgumentType.greedyString())
                      ^
  symbol:   variable Commands
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:32: error: cannot find symbol
            .then(Commands.literal("remove")
                  ^
  symbol:   variable Commands
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:30: error: cannot find symbol
                .then(Commands.argument("name", StringArgumentType.greedyString())
                                                ^
  symbol:   variable StringArgumentType
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:30: error: cannot find symbol
                .then(Commands.argument("name", StringArgumentType.greedyString())
                      ^
  symbol:   variable Commands
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:29: error: cannot find symbol
            .then(Commands.literal("add")
                  ^
  symbol:   variable Commands
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:28: error: cannot find symbol
        dispatcher.register(Commands.literal("waypoint")
                            ^
  symbol:   variable Commands
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:44: error: cannot find symbol
        String name = StringArgumentType.getString(context, "name");
                      ^
  symbol:   variable StringArgumentType
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:57: error: cannot find symbol
        String name = StringArgumentType.getString(context, "name");
                      ^
  symbol:   variable StringArgumentType
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommand.java:77: error: cannot find symbol
            Vec3 playerPos = manager.getCurrentPlayerPosition();
            ^
  symbol:   class Vec3
  location: class WaypointCommand
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:21: error: cannot find symbol
        dispatcher.register(Commands.literal("waypointgui")
                            ^
  symbol:   variable Commands
  location: class WaypointCommands
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:29: error: cannot find symbol
        Minecraft mc = Minecraft.getInstance();
                                ^
  symbol:   method getInstance()
  location: class Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:30: error: cannot find symbol
        mc.execute(() -> {
          ^
  symbol:   method execute(()->{ mc.s[...])); })
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\commands\WaypointCommands.java:31: error: cannot find symbol
            mc.setScreen(new WaypointManagerScreen());
                             ^
  symbol:   class WaypointManagerScreen
  location: class WaypointCommands
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:165: error: cannot find symbol
            Minecraft mc = Minecraft.getInstance();
                                    ^
  symbol:   method getInstance()
  location: class Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:166: error: cannot find symbol
            if (mc != null && mc.options != null) {
                                ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:168: error: cannot find symbol
                mc.options.keyAttack.setDown(false);
                  ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:169: error: cannot find symbol
                mc.options.keyUse.setDown(false);
                  ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:170: error: cannot find symbol
                mc.options.keyPickItem.setDown(false);
                  ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:239: error: cannot find symbol
            Minecraft mc = Minecraft.getInstance();
                                    ^
  symbol:   method getInstance()
  location: class Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:240: error: cannot find symbol
            if (mc.player == null || mc.screen != null) return;
                  ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:240: error: cannot find symbol
            if (mc.player == null || mc.screen != null) return;
                                       ^
  symbol:   variable screen
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:271: error: cannot find symbol
            Minecraft mc = Minecraft.getInstance();
                                    ^
  symbol:   method getInstance()
  location: class Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:272: error: cannot find symbol
            if (mc.player == null || mc.level == null) return;
                  ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:272: error: cannot find symbol
            if (mc.player == null || mc.level == null) return;
                                       ^
  symbol:   variable level
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:297: error: cannot find symbol
            mc.options.keyAttack.setDown(true);
              ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:306: error: cannot find symbol
            mc.options.keyAttack.setDown(false);
              ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:315: error: cannot find symbol
            mc.options.keyUse.setDown(true);
              ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:324: error: cannot find symbol
            mc.options.keyUse.setDown(false);
              ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:333: error: cannot find symbol
            mc.options.keyPickItem.setDown(true);
              ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:342: error: cannot find symbol
            mc.options.keyPickItem.setDown(false);
              ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:352: error: cannot find symbol
                Minecraft mc = Minecraft.getInstance();
                                        ^
  symbol:   method getInstance()
  location: class Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:370: error: cannot find symbol
            Minecraft mc = Minecraft.getInstance();
                                    ^
  symbol:   method getInstance()
  location: class Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:371: error: cannot find symbol
            if (mc.player == null) return;
                  ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:376: error: cannot find symbol
                int currentSlot = mc.player.getInventory().selected;
                                    ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:385: error: cannot find symbol
                mc.player.getInventory().selected = newSlot;
                  ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:398: error: package com.mojang.blaze3d.platform does not exist
                KeyMapping.set(com.mojang.blaze3d.platform.InputConstants.getKey(keyCode, 0), true);
                                                          ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:398: error: cannot find symbol
                KeyMapping.set(com.mojang.blaze3d.platform.InputConstants.getKey(keyCode, 0), true);
                ^
  symbol:   variable KeyMapping
  location: class ScriptManager
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:411: error: package com.mojang.blaze3d.platform does not exist
                KeyMapping.set(com.mojang.blaze3d.platform.InputConstants.getKey(keyCode, 0), false);
                                                          ^
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:411: error: cannot find symbol
                KeyMapping.set(com.mojang.blaze3d.platform.InputConstants.getKey(keyCode, 0), false);
                ^
  symbol:   variable KeyMapping
  location: class ScriptManager
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:421: error: cannot find symbol
            Minecraft mc = Minecraft.getInstance();
                                    ^
  symbol:   method getInstance()
  location: class Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:422: error: cannot find symbol
            if (mc.player == null) return;
                  ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:495: error: cannot find symbol
        float sensitivity = mc.options.sensitivity().get().floatValue() * 0.6F + 0.2F;
                              ^
  symbol:   variable options
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:503: error: cannot find symbol
        float newYaw = mc.player.getYRot() + yawChange;
                         ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:504: error: cannot find symbol
        float newPitch = mc.player.getXRot() + pitchChange; // Fixed: removed inversion that was causing up/down to be backwards
                           ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:509: error: cannot find symbol
        mc.player.setYRot(newYaw);
          ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:510: error: cannot find symbol
        mc.player.setXRot(newPitch);
          ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:513: error: cannot find symbol
        if (mc.cameraEntity == mc.player) {
              ^
  symbol:   variable cameraEntity
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:513: error: cannot find symbol
        if (mc.cameraEntity == mc.player) {
                                 ^
  symbol:   variable player
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:514: error: cannot find symbol
            mc.cameraEntity.setYRot(newYaw);
              ^
  symbol:   variable cameraEntity
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:515: error: cannot find symbol
            mc.cameraEntity.setXRot(newPitch);
              ^
  symbol:   variable cameraEntity
  location: variable mc of type Minecraft
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:610: error: cannot find symbol
                GLFW.GLFW_KEY_W, GLFW.GLFW_KEY_A, GLFW.GLFW_KEY_S, GLFW.GLFW_KEY_D, // WASD
                ^
  symbol:   variable GLFW
  location: class ScriptManager
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:610: error: cannot find symbol
                GLFW.GLFW_KEY_W, GLFW.GLFW_KEY_A, GLFW.GLFW_KEY_S, GLFW.GLFW_KEY_D, // WASD
                                 ^
  symbol:   variable GLFW
  location: class ScriptManager
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:610: error: cannot find symbol
                GLFW.GLFW_KEY_W, GLFW.GLFW_KEY_A, GLFW.GLFW_KEY_S, GLFW.GLFW_KEY_D, // WASD
                                                  ^
  symbol:   variable GLFW
  location: class ScriptManager
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:610: error: cannot find symbol
                GLFW.GLFW_KEY_W, GLFW.GLFW_KEY_A, GLFW.GLFW_KEY_S, GLFW.GLFW_KEY_D, // WASD
                                                                   ^
  symbol:   variable GLFW
  location: class ScriptManager
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:611: error: cannot find symbol
                GLFW.GLFW_KEY_SPACE, GLFW.GLFW_KEY_LEFT_SHIFT, GLFW.GLFW_KEY_RIGHT_SHIFT,   // Space, Shift
                ^
  symbol:   variable GLFW
  location: class ScriptManager
C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\sources\main\java\com\scriptrecorder\mod\recording\ScriptManager.java:611: error: cannot find symbol
                GLFW.GLFW_KEY_SPACE, GLFW.GLFW_KEY_LEFT_SHIFT, GLFW.GLFW_KEY_RIGHT_SHIFT,   // Space, Shift
                                     ^
  symbol:   variable GLFW
  location: class ScriptManager
100 errors
 FAILED

FAILURE: Build failed with an exception.

* What went wrong:
Execution failed for task ':compileJava'.
> Compilation failed; see the compiler error output for details.

* Try:
Run with --stacktrace option to get the stack trace. Run with --info or --debug option to get more log output.

BUILD FAILED

Total time: 6.082 secs
