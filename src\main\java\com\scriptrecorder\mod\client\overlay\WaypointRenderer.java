package com.scriptrecorder.mod.client.overlay;

import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.*;
import com.scriptrecorder.mod.waypoints.Waypoint;
import com.scriptrecorder.mod.waypoints.WaypointManager;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.Font;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.network.chat.Component;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.RenderLevelStageEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import org.joml.Matrix4f;

import java.util.List;

/**
 * Renders waypoints in the world with beams and markers
 */
@OnlyIn(Dist.CLIENT)
public class WaypointRenderer {
    
    private static boolean enabled = true;
    private static double renderDistance = 1000.0;
    
    public static void setEnabled(boolean enabled) {
        WaypointRenderer.enabled = enabled;
    }
    
    public static void setRenderDistance(double distance) {
        WaypointRenderer.renderDistance = distance;
    }
    
    @SubscribeEvent
    public void onRenderLevel(RenderLevelStageEvent event) {
        if (event.getStage() != RenderLevelStageEvent.Stage.AFTER_TRANSLUCENT_BLOCKS) {
            return;
        }
        if (!enabled) return;
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) {
            return;
        }

        // Don't render when GUI is open
        if (mc.screen != null) {
            return;
        }

        WaypointManager manager = WaypointManager.getInstance();
        List<Waypoint> waypoints = manager.getWaypointsInDimension(manager.getCurrentDimension());
        
        if (waypoints.isEmpty()) {
            return;
        }

        PoseStack poseStack = event.getPoseStack();
        Vec3 cameraPos = mc.gameRenderer.getMainCamera().getPosition();
        Vec3 playerPos = mc.player.position();

        // Setup GL state for rendering
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest(); // See through walls
        RenderSystem.setShader(GameRenderer::getPositionColorShader);

        for (Waypoint waypoint : waypoints) {
            if (!waypoint.isEnabled()) continue;
            
            Vec3 waypointPos = waypoint.getPosition();
            double distance = playerPos.distanceTo(waypointPos);
            
            if (distance <= renderDistance) {
                renderWaypoint(poseStack, cameraPos, waypoint, distance);
            }
        }

        // Restore GL state
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
    }
    
    private void renderWaypoint(PoseStack poseStack, Vec3 cameraPos, Waypoint waypoint, double distance) {
        Vec3 waypointPos = waypoint.getPosition();
        Vec3 adjustedPos = waypointPos.subtract(cameraPos);
        
        // Get color from waypoint
        int colorInt = waypoint.getColor();
        float[] color = {
            ((colorInt >> 16) & 0xFF) / 255.0f, // Red
            ((colorInt >> 8) & 0xFF) / 255.0f,  // Green
            (colorInt & 0xFF) / 255.0f,         // Blue
            0.3f                                // Alpha (more transparent)
        };
        
        poseStack.pushPose();
        
        // Render beam from ground to sky
        renderBeam(poseStack, adjustedPos, color);
        
        // Render waypoint marker
        renderMarker(poseStack, adjustedPos, color);

        // Render waypoint name
        renderWaypointName(poseStack, adjustedPos, waypoint.getName(), distance);

        poseStack.popPose();
    }
    
    private void renderBeam(PoseStack poseStack, Vec3 pos, float[] color) {
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder buffer = tesselator.getBuilder();
        Matrix4f matrix = poseStack.last().pose();
        
        buffer.begin(VertexFormat.Mode.QUADS, DefaultVertexFormat.POSITION_COLOR);
        
        float x = (float) pos.x;
        float y = (float) pos.y;
        float z = (float) pos.z;
        float width = 0.2f;
        
        // Beam from ground (y-64) to sky (y+320)
        float minY = y - 64;
        float maxY = y + 320;
        
        // Front face
        buffer.vertex(matrix, x - width, minY, z - width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x + width, minY, z - width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x + width, maxY, z - width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x - width, maxY, z - width).color(color[0], color[1], color[2], color[3]).endVertex();
        
        // Back face
        buffer.vertex(matrix, x + width, minY, z + width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x - width, minY, z + width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x - width, maxY, z + width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x + width, maxY, z + width).color(color[0], color[1], color[2], color[3]).endVertex();
        
        // Left face
        buffer.vertex(matrix, x - width, minY, z + width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x - width, minY, z - width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x - width, maxY, z - width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x - width, maxY, z + width).color(color[0], color[1], color[2], color[3]).endVertex();
        
        // Right face
        buffer.vertex(matrix, x + width, minY, z - width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x + width, minY, z + width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x + width, maxY, z + width).color(color[0], color[1], color[2], color[3]).endVertex();
        buffer.vertex(matrix, x + width, maxY, z - width).color(color[0], color[1], color[2], color[3]).endVertex();
        
        BufferUploader.drawWithShader(buffer.end());
    }
    
    private void renderMarker(PoseStack poseStack, Vec3 pos, float[] color) {
        Tesselator tesselator = Tesselator.getInstance();
        BufferBuilder buffer = tesselator.getBuilder();
        Matrix4f matrix = poseStack.last().pose();
        
        buffer.begin(VertexFormat.Mode.LINES, DefaultVertexFormat.POSITION_COLOR);
        
        float x = (float) pos.x;
        float y = (float) pos.y + 1.0f; // Slightly above ground
        float z = (float) pos.z;
        float size = 1.0f;
        
        // Draw X marker
        buffer.vertex(matrix, x - size, y, z - size).color(color[0], color[1], color[2], 1.0f).endVertex();
        buffer.vertex(matrix, x + size, y, z + size).color(color[0], color[1], color[2], 1.0f).endVertex();
        
        buffer.vertex(matrix, x + size, y, z - size).color(color[0], color[1], color[2], 1.0f).endVertex();
        buffer.vertex(matrix, x - size, y, z + size).color(color[0], color[1], color[2], 1.0f).endVertex();
        
        // Draw vertical line
        buffer.vertex(matrix, x, y - 0.5f, z).color(color[0], color[1], color[2], 1.0f).endVertex();
        buffer.vertex(matrix, x, y + 0.5f, z).color(color[0], color[1], color[2], 1.0f).endVertex();
        
        BufferUploader.drawWithShader(buffer.end());
    }

    private void renderWaypointName(PoseStack poseStack, Vec3 pos, String name, double distance) {
        Minecraft mc = Minecraft.getInstance();
        Font font = mc.font;

        poseStack.pushPose();

        // Position text above the waypoint
        poseStack.translate(pos.x, pos.y + 2.0, pos.z);

        // Make text face the player
        poseStack.mulPose(mc.gameRenderer.getMainCamera().rotation());
        poseStack.scale(-0.025f, -0.025f, 0.025f);

        // Create text with distance
        String displayText = name + " [" + String.format("%.0fm", distance) + "]";
        Component textComponent = Component.literal(displayText);

        // Get text width for centering
        int textWidth = font.width(textComponent);

        // Create buffer source for text rendering
        MultiBufferSource.BufferSource bufferSource = mc.renderBuffers().bufferSource();

        // Render text with background
        font.drawInBatch(textComponent, -textWidth / 2.0f, 0, 0xFFFFFFFF, false,
                        poseStack.last().pose(), bufferSource, Font.DisplayMode.SEE_THROUGH, 0x40000000, 15728880);

        bufferSource.endBatch();

        poseStack.popPose();
    }
}
