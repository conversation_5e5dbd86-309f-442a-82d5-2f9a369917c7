package com.scriptrecorder.mod.client.gui;

import com.scriptrecorder.mod.client.settings.ModSettings;
import net.minecraft.client.gui.GuiButton;
import net.minecraft.client.gui.GuiScreen;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.io.IOException;

/**
 * Simple GUI for 1.8.9 compatibility
 */
@SideOnly(Side.CLIENT)
public class SimpleGUI189 extends GuiScreen {
    
    private static final int BUTTON_WIDTH = 200;
    private static final int BUTTON_HEIGHT = 20;
    private static final int BUTTON_SPACING = 25;
    
    // Button IDs
    private static final int TOGGLE_SPRINT_ID = 1;
    private static final int TOGGLE_CROUCH_ID = 2;
    private static final int TOGGLE_KEYSTROKES_ID = 3;
    private static final int CLOSE_ID = 4;
    
    private ModSettings settings;
    
    @Override
    public void initGui() {
        super.initGui();
        
        settings = ModSettings.getInstance();
        
        int centerX = this.width / 2;
        int startY = this.height / 2 - 60;
        
        // Toggle Sprint button
        String sprintText = "Toggle Sprint: " + (settings.sprintToggled ? "ON" : "OFF");
        this.buttonList.add(new GuiButton(TOGGLE_SPRINT_ID, centerX - BUTTON_WIDTH/2, startY, BUTTON_WIDTH, BUTTON_HEIGHT, sprintText));
        
        // Toggle Crouch button
        String crouchText = "Toggle Crouch: " + (settings.crouchToggled ? "ON" : "OFF");
        this.buttonList.add(new GuiButton(TOGGLE_CROUCH_ID, centerX - BUTTON_WIDTH/2, startY + BUTTON_SPACING, BUTTON_WIDTH, BUTTON_HEIGHT, crouchText));
        
        // Toggle Keystrokes button
        String keystrokesText = "Keystroke Display: " + (settings.keystrokeDisplayEnabled ? "ON" : "OFF");
        this.buttonList.add(new GuiButton(TOGGLE_KEYSTROKES_ID, centerX - BUTTON_WIDTH/2, startY + BUTTON_SPACING * 2, BUTTON_WIDTH, BUTTON_HEIGHT, keystrokesText));
        
        // Close button
        this.buttonList.add(new GuiButton(CLOSE_ID, centerX - BUTTON_WIDTH/2, startY + BUTTON_SPACING * 4, BUTTON_WIDTH, BUTTON_HEIGHT, "Close"));
    }
    
    @Override
    protected void actionPerformed(GuiButton button) throws IOException {
        switch (button.id) {
            case TOGGLE_SPRINT_ID:
                settings.sprintToggled = !settings.sprintToggled;
                settings.save();
                updateButtonText();
                break;
                
            case TOGGLE_CROUCH_ID:
                settings.crouchToggled = !settings.crouchToggled;
                settings.save();
                updateButtonText();
                break;
                
            case TOGGLE_KEYSTROKES_ID:
                settings.keystrokeDisplayEnabled = !settings.keystrokeDisplayEnabled;
                settings.save();
                updateButtonText();
                break;
                
            case CLOSE_ID:
                this.mc.displayGuiScreen(null);
                break;
        }
    }
    
    private void updateButtonText() {
        // Update button text to reflect current state
        for (Object obj : this.buttonList) {
            GuiButton button = (GuiButton) obj;
            switch (button.id) {
                case TOGGLE_SPRINT_ID:
                    button.displayString = "Toggle Sprint: " + (settings.sprintToggled ? "ON" : "OFF");
                    break;
                case TOGGLE_CROUCH_ID:
                    button.displayString = "Toggle Crouch: " + (settings.crouchToggled ? "ON" : "OFF");
                    break;
                case TOGGLE_KEYSTROKES_ID:
                    button.displayString = "Keystroke Display: " + (settings.keystrokeDisplayEnabled ? "ON" : "OFF");
                    break;
            }
        }
    }
    
    @Override
    public void drawScreen(int mouseX, int mouseY, float partialTicks) {
        // Draw background
        this.drawDefaultBackground();
        
        // Draw title
        String title = "Waypoint Mod Settings";
        int titleWidth = this.fontRendererObj.getStringWidth(title);
        this.fontRendererObj.drawString(title, (this.width - titleWidth) / 2, this.height / 2 - 90, 0xFFFFFF);
        
        // Draw buttons
        super.drawScreen(mouseX, mouseY, partialTicks);
    }
    
    @Override
    public boolean doesGuiPauseGame() {
        return false;
    }
}
