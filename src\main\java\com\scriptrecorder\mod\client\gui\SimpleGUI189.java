package com.scriptrecorder.mod.client.gui;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.settings.ModSettings;
import com.scriptrecorder.mod.recording.RecordedScript;
import net.minecraft.client.gui.GuiButton;
import net.minecraft.client.gui.GuiScreen;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.io.IOException;

/**
 * Simple GUI for 1.8.9 compatibility
 */
@SideOnly(Side.CLIENT)
public class SimpleGUI189 extends GuiScreen {
    
    private static final int BUTTON_WIDTH = 200;
    private static final int BUTTON_HEIGHT = 20;
    private static final int BUTTON_SPACING = 25;
    
    // Button IDs
    private static final int TOGGLE_SPRINT_ID = 1;
    private static final int TOGGLE_CROUCH_ID = 2;
    private static final int TOGGLE_KEYSTROKES_ID = 3;
    private static final int RECORD_SCRIPT_ID = 4;
    private static final int STOP_RECORD_ID = 5;
    private static final int PLAY_SCRIPT_ID = 6;
    private static final int CLOSE_ID = 7;
    
    private ModSettings settings;
    
    @Override
    public void initGui() {
        super.initGui();
        
        settings = ModSettings.getInstance();
        
        int centerX = this.width / 2;
        int startY = this.height / 2 - 100;

        // Toggle Sprint button
        String sprintText = "Toggle Sprint: " + (settings.sprintToggled ? "ON" : "OFF");
        this.buttonList.add(new GuiButton(TOGGLE_SPRINT_ID, centerX - BUTTON_WIDTH/2, startY, BUTTON_WIDTH, BUTTON_HEIGHT, sprintText));

        // Toggle Crouch button
        String crouchText = "Toggle Crouch: " + (settings.crouchToggled ? "ON" : "OFF");
        this.buttonList.add(new GuiButton(TOGGLE_CROUCH_ID, centerX - BUTTON_WIDTH/2, startY + BUTTON_SPACING, BUTTON_WIDTH, BUTTON_HEIGHT, crouchText));

        // Toggle Keystrokes button
        String keystrokesText = "Keystroke Display: " + (settings.keystrokeDisplayEnabled ? "ON" : "OFF");
        this.buttonList.add(new GuiButton(TOGGLE_KEYSTROKES_ID, centerX - BUTTON_WIDTH/2, startY + BUTTON_SPACING * 2, BUTTON_WIDTH, BUTTON_HEIGHT, keystrokesText));

        // Script recording buttons
        boolean isRecording = ScriptRecorderMod.scriptManager.isRecording();
        boolean isPlaying = ScriptRecorderMod.scriptManager.isPlaying();
        RecordedScript lastScript = ScriptRecorderMod.scriptManager.getLastRecordedScript();

        String recordText = isRecording ? "Stop Recording" : "Start Recording";
        this.buttonList.add(new GuiButton(isRecording ? STOP_RECORD_ID : RECORD_SCRIPT_ID, centerX - BUTTON_WIDTH/2, startY + BUTTON_SPACING * 4, BUTTON_WIDTH, BUTTON_HEIGHT, recordText));

        String playText = isPlaying ? "Playing..." : (lastScript != null ? "Play Last Script" : "No Script");
        GuiButton playButton = new GuiButton(PLAY_SCRIPT_ID, centerX - BUTTON_WIDTH/2, startY + BUTTON_SPACING * 5, BUTTON_WIDTH, BUTTON_HEIGHT, playText);
        playButton.enabled = !isPlaying && lastScript != null;
        this.buttonList.add(playButton);

        // Close button
        this.buttonList.add(new GuiButton(CLOSE_ID, centerX - BUTTON_WIDTH/2, startY + BUTTON_SPACING * 7, BUTTON_WIDTH, BUTTON_HEIGHT, "Close"));
    }
    
    @Override
    protected void actionPerformed(GuiButton button) throws IOException {
        switch (button.id) {
            case TOGGLE_SPRINT_ID:
                settings.sprintToggled = !settings.sprintToggled;
                settings.save();
                updateButtonText();
                break;

            case TOGGLE_CROUCH_ID:
                settings.crouchToggled = !settings.crouchToggled;
                settings.save();
                updateButtonText();
                break;

            case TOGGLE_KEYSTROKES_ID:
                settings.keystrokeDisplayEnabled = !settings.keystrokeDisplayEnabled;
                settings.save();
                updateButtonText();
                break;

            case RECORD_SCRIPT_ID:
                String scriptName = "gui_script_" + System.currentTimeMillis();
                if (ScriptRecorderMod.scriptManager.startRecording(scriptName)) {
                    // Close GUI to allow recording
                    this.mc.displayGuiScreen(null);
                }
                break;

            case STOP_RECORD_ID:
                ScriptRecorderMod.scriptManager.stopRecording();
                updateButtonText();
                break;

            case PLAY_SCRIPT_ID:
                RecordedScript lastScript = ScriptRecorderMod.scriptManager.getLastRecordedScript();
                if (lastScript != null) {
                    ScriptRecorderMod.scriptManager.startPlayback(lastScript);
                    // Close GUI to allow playback
                    this.mc.displayGuiScreen(null);
                }
                break;

            case CLOSE_ID:
                this.mc.displayGuiScreen(null);
                break;
        }
    }
    
    private void updateButtonText() {
        // Reinitialize GUI to update all button states
        this.buttonList.clear();
        this.initGui();
    }
    
    @Override
    public void drawScreen(int mouseX, int mouseY, float partialTicks) {
        // Draw background
        this.drawDefaultBackground();
        
        // Draw title
        String title = "Waypoint Mod Settings";
        int titleWidth = this.fontRendererObj.getStringWidth(title);
        this.fontRendererObj.drawString(title, (this.width - titleWidth) / 2, this.height / 2 - 130, 0xFFFFFF);

        // Draw status information
        boolean isRecording = ScriptRecorderMod.scriptManager.isRecording();
        boolean isPlaying = ScriptRecorderMod.scriptManager.isPlaying();
        RecordedScript lastScript = ScriptRecorderMod.scriptManager.getLastRecordedScript();

        String status = "";
        if (isRecording) {
            status = "Recording in progress...";
        } else if (isPlaying) {
            status = "Playing script...";
        } else if (lastScript != null) {
            status = "Last script: " + lastScript.getTotalActions() + " actions";
        } else {
            status = "No script recorded";
        }

        int statusWidth = this.fontRendererObj.getStringWidth(status);
        this.fontRendererObj.drawString(status, (this.width - statusWidth) / 2, this.height / 2 + 100, 0xCCCCCC);
        
        // Draw buttons
        super.drawScreen(mouseX, mouseY, partialTicks);
    }
    
    @Override
    public boolean doesGuiPauseGame() {
        return false;
    }
}
