package com.scriptrecorder.mod.client.overlay;

import com.scriptrecorder.mod.client.autoclicker.AutoClicker;
import com.scriptrecorder.mod.client.settings.ModSettings;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.RenderGuiOverlayEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;

import java.awt.Color;
import java.util.ArrayList;
import java.util.List;

/**
 * Overlay that displays CPS (Clicks Per Second) information for the AutoClicker
 */
@OnlyIn(Dist.CLIENT)
@Mod.EventBusSubscriber(modid = "waypointmod", bus = Mod.EventBusSubscriber.Bus.FORGE, value = Dist.CLIENT)
public class CPSOverlay {
    
    private static boolean enabled = false;
    private static int displayX = 10;
    private static int displayY = 50;
    
    // CPS tracking
    private static List<Long> leftClicks = new ArrayList<>();
    private static List<Long> rightClicks = new ArrayList<>();
    private static final long CPS_WINDOW = 1000; // 1 second window
    
    // Colors
    private static final Color BACKGROUND_COLOR = new Color(0, 0, 0, 100);
    private static final Color TEXT_COLOR = new Color(255, 255, 255, 255);
    private static final Color ACTIVE_COLOR = new Color(50, 220, 50, 255);
    private static final Color INACTIVE_COLOR = new Color(220, 50, 50, 255);
    
    @SubscribeEvent
    public static void onRenderOverlay(RenderGuiOverlayEvent.Post event) {
        if (!enabled || Minecraft.getInstance().player == null) {
            return;
        }
        
        Minecraft mc = Minecraft.getInstance();
        GuiGraphics guiGraphics = event.getGuiGraphics();
        
        // Clean old clicks
        cleanOldClicks();
        
        // Calculate current CPS
        double leftCPS = leftClicks.size();
        double rightCPS = rightClicks.size();
        
        // Get AutoClicker info
        AutoClicker autoClicker = AutoClicker.getInstance();
        boolean autoClickerEnabled = autoClicker.isEnabled();
        boolean attackEnabled = autoClicker.isAttackEnabled();
        boolean useEnabled = autoClicker.isUseEnabled();
        
        // Render background
        int width = 120;
        int height = autoClickerEnabled ? 65 : 45;
        guiGraphics.fill(displayX - 2, displayY - 2, displayX + width, displayY + height, BACKGROUND_COLOR.getRGB());
        
        // Render CPS info
        String leftCPSText = String.format("LMB: %.1f CPS", leftCPS);
        String rightCPSText = String.format("RMB: %.1f CPS", rightCPS);
        
        guiGraphics.drawString(mc.font, leftCPSText, displayX, displayY, TEXT_COLOR.getRGB());
        guiGraphics.drawString(mc.font, rightCPSText, displayX, displayY + 12, TEXT_COLOR.getRGB());
        
        // Show AutoClicker status if enabled
        if (autoClickerEnabled) {
            Color statusColor = ACTIVE_COLOR;
            String statusText = "AutoClicker: ON";
            guiGraphics.drawString(mc.font, statusText, displayX, displayY + 26, statusColor.getRGB());
            
            // Show attack/use status
            if (attackEnabled) {
                String attackText = String.format("Attack: %.1f-%.1f", autoClicker.getAttackCPSMin(), autoClicker.getAttackCPSMax());
                guiGraphics.drawString(mc.font, attackText, displayX, displayY + 38, ACTIVE_COLOR.getRGB());
            }
            if (useEnabled) {
                String useText = String.format("Use: %.1f-%.1f", autoClicker.getUseCPSMin(), autoClicker.getUseCPSMax());
                guiGraphics.drawString(mc.font, useText, displayX, displayY + 50, ACTIVE_COLOR.getRGB());
            }
        } else {
            String statusText = "AutoClicker: OFF";
            guiGraphics.drawString(mc.font, statusText, displayX, displayY + 26, INACTIVE_COLOR.getRGB());
        }
    }
    
    /**
     * Record a left click for CPS tracking
     */
    public static void recordLeftClick() {
        if (enabled) {
            leftClicks.add(System.currentTimeMillis());
        }
    }
    
    /**
     * Record a right click for CPS tracking
     */
    public static void recordRightClick() {
        if (enabled) {
            rightClicks.add(System.currentTimeMillis());
        }
    }
    
    /**
     * Clean clicks older than the CPS window
     */
    private static void cleanOldClicks() {
        long currentTime = System.currentTimeMillis();
        leftClicks.removeIf(time -> currentTime - time > CPS_WINDOW);
        rightClicks.removeIf(time -> currentTime - time > CPS_WINDOW);
    }
    
    // Settings methods
    public static boolean isEnabled() {
        return enabled;
    }
    
    public static void setEnabled(boolean enabled) {
        CPSOverlay.enabled = enabled;
        ModSettings.setCPSOverlayEnabled(enabled);
    }
    
    public static void toggle() {
        setEnabled(!enabled);
    }
    
    public static void setPosition(int x, int y) {
        displayX = x;
        displayY = y;
        ModSettings.setCPSOverlayPosition(x, y);
    }
    
    public static int getDisplayX() {
        return displayX;
    }
    
    public static int getDisplayY() {
        return displayY;
    }
    
    /**
     * Load settings from ModSettings
     */
    public static void loadSettings() {
        enabled = ModSettings.isCPSOverlayEnabled();
        int[] pos = ModSettings.getCPSOverlayPosition();
        displayX = pos[0];
        displayY = pos[1];
    }
}
