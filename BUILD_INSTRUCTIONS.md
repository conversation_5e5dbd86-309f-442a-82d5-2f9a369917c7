# Minecraft Script Recorder Mod - Build Instructions

## Prerequisites

### 1. Java 21 Installation
Minecraft 1.20.4 with Forge requires Java 21. 

**Quick Installation:**
```powershell
# Run the provided installation script
.\install_java21.ps1
```

**Manual Installation:**
1. Go to [Eclipse Temurin](https://adoptium.net/temurin/releases/?version=21)
2. Download the Windows x64 MSI installer for Java 21
3. Run the installer and **make sure to check**:
   - ✅ Add to PATH
   - ✅ Set JAVA_HOME
4. Restart your terminal/PowerShell

**Verify Installation:**
```cmd
java -version
```
Should show something like: `openjdk version "21.0.1"`

## Building the Mod

### Clean Build (Recommended)
```cmd
gradlew.bat clean build
```

### Quick Build
```cmd
gradlew.bat build
```

### Development Testing
```cmd
gradlew.bat runClient
```

## Build Output

After a successful build, you'll find the mod JAR file in:
```
build/libs/minecraft-script-recorder-1.20.4-3.1.0.jar
```

## Installation

1. Install Minecraft Forge 1.20.4 (version 49.2.0 or compatible)
2. Copy the built JAR file to your Minecraft `mods` folder
3. Launch Minecraft with the Forge profile

## Mod Features

- **Script Recording**: Record and replay mouse/keyboard actions
- **Toggle Sprint/Crouch**: Persistent toggle states
- **Keystroke Display**: Real-time key press overlay
- **GUI Access**: Shift + \ to open the control panel
- **Hotkeys**: F6 (record), F7 (playback)

## Troubleshooting

### Build Fails with "Java not found"
- Make sure Java 21 is installed and in PATH
- Restart your terminal after Java installation
- Try: `java -version` to verify

### Build Fails with Gradle errors
- Try: `gradlew.bat clean` then `gradlew.bat build`
- Check internet connection (Gradle downloads dependencies)

### Mod doesn't load in Minecraft
- Verify you're using Minecraft 1.20.4 with Forge 49.2.0+
- Check the `logs/latest.log` file for error messages
- Ensure the JAR file is in the correct `mods` folder

## Development

### Project Structure
```
src/main/java/com/scriptrecorder/mod/
├── ScriptRecorderMod.java          # Main mod class
├── client/
│   ├── KeyBindings.java            # Key registration
│   ├── ScriptRecorderClient.java   # Event handling
│   ├── gui/                        # GUI components
│   ├── overlay/                    # HUD overlays
│   └── settings/                   # Settings management
└── recording/                      # Script recording logic
```

### Version Information
- **Mod Version**: 3.1.0
- **Minecraft Version**: 1.20.4
- **Forge Version**: 49.2.0
- **Java Version**: 21

## Support

If you encounter issues:
1. Check the build logs for specific error messages
2. Verify all prerequisites are installed correctly
3. Try a clean build: `gradlew.bat clean build`
