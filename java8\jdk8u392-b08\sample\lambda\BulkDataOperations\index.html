<!DOCTYPE html>
<html>
<head>
    <title>Bulk Data Operations Demo</title>
</head>
<body>
<h2>Bulk Data Operations Demo</h2>

<p>
    This demo shows how to use bulk data operations with the new JDK8
    Collections API.
    The demo also demonstrates new features of JDK8 such as lambda expressions
    and method/constructor references.
</p>

<ul>
    <li><h3>CSV Processor</h3>

        <p>
            Analyzes a CSV file, finds and collects useful information, computes
            different statistics. For more information, see the source file.
        </p>
        Source: <a href="src/CSVProcessor.java">src/CSVProcessor.java</a>
    <li><h3>Grep</h3>

        <p>
            Behaves like the standard Linux tool Grep. For more information, see
            the source file.
        </p>
        Source: <a href="src/Grep.java">src/Grep.java</a>
    <li><h3>PasswordGenerator</h3>

        <p>
            Produces a password of desired length. For more information see
            source file.
        </p>
        Source: <a
                href="src/PasswordGenerator.java">src/PasswordGenerator.java</a>
    <li><h3>WC</h3>

        <p>
            Counts newlines, words, characters, and the maximum line length of a
            text file. For more information, see the source
            file.
        </p>
        Source: <a href="src/WC.java">src/WC.java</a>
</ul>
</body>
</html>