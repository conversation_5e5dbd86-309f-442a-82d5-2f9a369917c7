package com.scriptrecorder.mod;

import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;

public class ClientProxyMinimal extends CommonProxyMinimal {
    
    @Override
    public void preInit(FMLPreInitializationEvent event) {
        super.preInit(event);
        ScriptRecorderModMinimal.LOGGER.info("Client pre-init");
    }
    
    @Override
    public void init(FMLInitializationEvent event) {
        super.init(event);
        ScriptRecorderModMinimal.LOGGER.info("Client init");
    }
}
