package com.scriptrecorder.mod.client.gui.panel;

import com.scriptrecorder.mod.client.settings.ModSettings;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.network.chat.Component;
import java.awt.Color;

/**
 * Panel for general settings and toggles
 */
public class SettingsPanel extends Panel {
    
    // UI Components
    private Button toggleSprintButton;
    private Button toggleCrouchButton;
    private Button toggleKeystrokesButton;
    private Button resetPositionsButton;
    private Button saveSettingsButton;
    
    public SettingsPanel() {
        super("Settings", 20, 180, 280, 140);
        initializeComponents();
    }
    
    private void initializeComponents() {
        clearWidgets();
        
        // Calculate positions relative to panel
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Row 1: Toggle buttons
        toggleSprintButton = Button.builder(
            getSprintButtonText(),
            button -> toggleSprint())
            .bounds(contentX, contentY, 80, 20)
            .build();
        addWidget(toggleSprintButton);
        
        toggleCrouchButton = Button.builder(
            getCrouchButtonText(),
            button -> toggleCrouch())
            .bounds(contentX + 90, contentY, 80, 20)
            .build();
        addWidget(toggleCrouchButton);
        
        toggleKeystrokesButton = Button.builder(
            getKeystrokesButtonText(),
            button -> toggleKeystrokes())
            .bounds(contentX + 180, contentY, 80, 20)
            .build();
        addWidget(toggleKeystrokesButton);
        
        // Row 2: Utility buttons
        resetPositionsButton = Button.builder(
            Component.literal("Reset Layout"),
            button -> resetPanelPositions())
            .bounds(contentX, contentY + 30, 120, 20)
            .build();
        addWidget(resetPositionsButton);
        
        saveSettingsButton = Button.builder(
            Component.literal("Save Settings"),
            button -> saveSettings())
            .bounds(contentX + 130, contentY + 30, 120, 20)
            .build();
        addWidget(saveSettingsButton);
    }
    
    @Override
    protected void renderContent(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Update component positions when panel moves
        updateComponentPositions();
        
        // Draw status information
        int textX = (int)(x + 10);
        int textY = (int)(y + titleBarHeight + 70);
        
        // Draw current states
        String sprintState = ModSettings.getInstance().sprintToggled ? "ON" : "OFF";
        String crouchState = ModSettings.getInstance().crouchToggled ? "ON" : "OFF";
        String keystrokeState = ModSettings.getInstance().keystrokeDisplayEnabled ? "ON" : "OFF";
        
        guiGraphics.drawString(Minecraft.getInstance().font, 
                             "Sprint: " + sprintState, textX, textY, Color.WHITE.getRGB());
        guiGraphics.drawString(Minecraft.getInstance().font, 
                             "Crouch: " + crouchState, textX + 90, textY, Color.WHITE.getRGB());
        guiGraphics.drawString(Minecraft.getInstance().font, 
                             "Keystrokes: " + keystrokeState, textX + 180, textY, Color.WHITE.getRGB());
        
        // Draw help text
        guiGraphics.drawString(Minecraft.getInstance().font, 
                             "Movement and display options", textX, textY + 15, Color.GRAY.getRGB());
        guiGraphics.drawString(Minecraft.getInstance().font, 
                             "Hotkeys: F6 Record | F7 Stop | F8 Play", textX, textY + 27, Color.GRAY.getRGB());
    }
    
    @Override
    protected void updateComponentPositions() {
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Update button positions
        if (toggleSprintButton != null) {
            toggleSprintButton.setX(contentX);
            toggleSprintButton.setY(contentY);
        }
        if (toggleCrouchButton != null) {
            toggleCrouchButton.setX(contentX + 90);
            toggleCrouchButton.setY(contentY);
        }
        if (toggleKeystrokesButton != null) {
            toggleKeystrokesButton.setX(contentX + 180);
            toggleKeystrokesButton.setY(contentY);
        }
        if (resetPositionsButton != null) {
            resetPositionsButton.setX(contentX);
            resetPositionsButton.setY(contentY + 30);
        }
        if (saveSettingsButton != null) {
            saveSettingsButton.setX(contentX + 130);
            saveSettingsButton.setY(contentY + 30);
        }
    }
    
    // Button action methods
    private void toggleSprint() {
        ModSettings.getInstance().sprintToggled = !ModSettings.getInstance().sprintToggled;
        updateButtonStates();
        sendChatMessage("Toggle Sprint: " + (ModSettings.getInstance().sprintToggled ? "ON" : "OFF"));
    }
    
    private void toggleCrouch() {
        ModSettings.getInstance().crouchToggled = !ModSettings.getInstance().crouchToggled;
        updateButtonStates();
        sendChatMessage("Toggle Crouch: " + (ModSettings.getInstance().crouchToggled ? "ON" : "OFF"));
    }
    
    private void toggleKeystrokes() {
        ModSettings.getInstance().keystrokeDisplayEnabled = !ModSettings.getInstance().keystrokeDisplayEnabled;
        updateButtonStates();
        sendChatMessage("Keystroke Display: " + (ModSettings.getInstance().keystrokeDisplayEnabled ? "ON" : "OFF"));
    }
    
    private void resetPanelPositions() {
        PanelManager.getInstance().resetPositions();
        sendChatMessage("Panel positions reset to default");
    }
    
    private void saveSettings() {
        ModSettings.getInstance().save();
        sendChatMessage("Settings saved successfully");
    }
    
    private void updateButtonStates() {
        if (toggleSprintButton != null) {
            toggleSprintButton.setMessage(getSprintButtonText());
        }
        if (toggleCrouchButton != null) {
            toggleCrouchButton.setMessage(getCrouchButtonText());
        }
        if (toggleKeystrokesButton != null) {
            toggleKeystrokesButton.setMessage(getKeystrokesButtonText());
        }
    }
    
    // Button text methods
    private Component getSprintButtonText() {
        return Component.literal("Sprint: " + (ModSettings.getInstance().sprintToggled ? "ON" : "OFF"));
    }
    
    private Component getCrouchButtonText() {
        return Component.literal("Crouch: " + (ModSettings.getInstance().crouchToggled ? "ON" : "OFF"));
    }
    
    private Component getKeystrokesButtonText() {
        return Component.literal("Keys: " + (ModSettings.getInstance().keystrokeDisplayEnabled ? "ON" : "OFF"));
    }
    
    private void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null) {
            mc.player.displayClientMessage(Component.literal("[Settings] " + message), false);
        }
    }
    
    /**
     * Update the panel (called every frame)
     */
    public void update() {
        // Always update component positions first
        updateComponentPositions();
        updateButtonStates();
    }
}
