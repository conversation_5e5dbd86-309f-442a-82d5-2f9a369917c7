package com.scriptrecorder.mod.recording;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.scriptrecorder.mod.ScriptRecorderMod;
import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import org.lwjgl.input.Keyboard;
import java.io.*;
import java.lang.reflect.Method;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * Manages script recording, playback, and file operations
 */
public class ScriptManager {
    
    private static final String SCRIPTS_FOLDER = "waypoint_data";
    private static final String SCRIPT_EXTENSION = ".json";
    
    private final Gson gson;
    private final Path scriptsDirectory;
    
    // Current recording state
    private boolean isRecording = false;
    private RecordedScript currentScript;
    private long recordingStartTime;

    // Current playback state
    private boolean isPlaying = false;
    private RecordedScript playingScript;

    // Last recorded/loaded script for playback
    private RecordedScript lastScript;
    private Thread playbackThread;
    
    public ScriptManager() {
        this.gson = new GsonBuilder()
            .setPrettyPrinting()
            .create();
            
        // Create scripts directory in Minecraft folder
        File minecraftDir = Minecraft.getMinecraft().mcDataDir;
        this.scriptsDirectory = new File(minecraftDir, SCRIPTS_FOLDER).toPath();
        
        try {
            Files.createDirectories(scriptsDirectory);
            ScriptRecorderMod.LOGGER.info("Scripts directory created at: " + scriptsDirectory);
        } catch (IOException e) {
            ScriptRecorderMod.LOGGER.error("Failed to create scripts directory", e);
        }
    }
    
    // Recording methods
    public boolean startRecording(String scriptName) {
        if (isRecording) {
            ScriptRecorderMod.LOGGER.warn("Already recording a script");
            return false;
        }
        
        currentScript = new RecordedScript(scriptName);
        recordingStartTime = System.currentTimeMillis();
        isRecording = true;
        
        ScriptRecorderMod.LOGGER.info("Started recording script: " + scriptName);
        return true;
    }
    
    public RecordedScript stopRecording() {
        if (!isRecording) {
            ScriptRecorderMod.LOGGER.warn("Not currently recording");
            return null;
        }
        
        isRecording = false;
        RecordedScript completedScript = currentScript;
        currentScript = null;

        // Store as the last script for playback
        lastScript = completedScript;

        ScriptRecorderMod.LOGGER.info("Stopped recording. Captured " +
            completedScript.getTotalActions() + " actions in " +
            completedScript.getFormattedDuration());

        return completedScript;
    }
    
    public void recordAction(ScriptAction action) {
        if (!isRecording || currentScript == null) {
            return;
        }

        // The action should already have the correct time set from the caller
        // But ensure it's relative to recording start if not set
        if (action.getTime() == 0.0) {
            double relativeTime = (System.currentTimeMillis() - recordingStartTime) / 1000.0;
            action.setTime(relativeTime);
        }

        currentScript.addAction(action);
        ScriptRecorderMod.LOGGER.debug("Recorded action: " + action.toString());
    }

    /**
     * Get the current recording time in seconds since recording started
     */
    public double getRecordingTime() {
        if (!isRecording) {
            return 0.0;
        }

        long currentTime = System.currentTimeMillis();
        return (currentTime - recordingStartTime) / 1000.0; // Convert to seconds
    }
    
    // Playback methods
    public boolean startPlayback(RecordedScript script) {
        if (isPlaying) {
            ScriptRecorderMod.LOGGER.warn("Already playing a script");
            return false;
        }
        
        if (script == null || !script.isValid()) {
            ScriptRecorderMod.LOGGER.error("Invalid script for playback: " + 
                (script != null ? script.getValidationError() : "null script"));
            return false;
        }
        
        playingScript = script;
        isPlaying = true;
        
        // Start playback in separate thread
        playbackThread = new Thread(this::runPlayback);
        playbackThread.setDaemon(true);
        playbackThread.start();
        
        ScriptRecorderMod.LOGGER.info("Started playback of script: " + script.getName());
        return true;
    }
    
    public void stopPlayback() {
        if (!isPlaying) {
            return;
        }

        isPlaying = false;
        if (playbackThread != null) {
            playbackThread.interrupt();
        }

        // Release all keys and mouse buttons to prevent sticking
        releaseAllKeys();
        releaseAllMouseButtons();

        ScriptRecorderMod.LOGGER.info("Stopped script playback");
    }

    private void releaseAllMouseButtons() {
        try {
            Minecraft mc = Minecraft.getMinecraft();
            if (mc != null && mc.gameSettings != null) {
                // Release all mouse buttons
                net.minecraft.client.settings.KeyBinding.setKeyBindState(mc.gameSettings.keyBindAttack.getKeyCode(), false);
                net.minecraft.client.settings.KeyBinding.setKeyBindState(mc.gameSettings.keyBindUseItem.getKeyCode(), false);
                net.minecraft.client.settings.KeyBinding.setKeyBindState(mc.gameSettings.keyBindPickBlock.getKeyCode(), false);

                // Reset mouse state tracking
                leftMousePressed = false;
                rightMousePressed = false;
                middleMousePressed = false;

                ScriptRecorderMod.LOGGER.debug("All mouse buttons released");
            }
        } catch (Exception e) {
            ScriptRecorderMod.LOGGER.error("Error releasing mouse buttons", e);
        }
    }
    
    private void runPlayback() {
        try {
            int loopCount = playingScript.getLoopCount();
            int currentLoop = 0;
            
            while (isPlaying && (loopCount == 0 || currentLoop < loopCount)) {
                executeScriptActions();
                currentLoop++;
                
                // Brief pause between loops
                if (isPlaying && (loopCount == 0 || currentLoop < loopCount)) {
                    Thread.sleep(500);
                }
            }
        } catch (InterruptedException e) {
            ScriptRecorderMod.LOGGER.info("Script playback interrupted");
        } catch (Exception e) {
            ScriptRecorderMod.LOGGER.error("Error during script playback", e);
        } finally {
            isPlaying = false;
            playingScript = null;
            playbackThread = null;
            // Release all keys to prevent sticking when playback ends
            releaseAllKeys();
        }
    }
    
    private void executeScriptActions() throws InterruptedException {
        List<ScriptAction> actions = playingScript.getActions();
        if (actions.isEmpty()) return;
        
        long startTime = System.currentTimeMillis();
        double speedMultiplier = 1.0 / playingScript.getPlaybackSpeed();
        
        for (ScriptAction action : actions) {
            if (!isPlaying) break;
            
            // Wait for the correct timing
            double targetTime = action.getTime() * speedMultiplier * 1000; // Convert to milliseconds
            long elapsed = System.currentTimeMillis() - startTime;
            long waitTime = (long) (targetTime - elapsed);
            
            if (waitTime > 0) {
                Thread.sleep(waitTime);
            }
            
            // Execute the action on the main thread
            // Execute action on main thread (1.8.9 doesn't have execute method)
            executeAction(action);
        }
    }
    
    private void executeAction(ScriptAction action) {
        try {
            // Ensure we're still in-game and not in a GUI (1.20.4)
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer == null || mc.currentScreen != null) return;

            switch (action.getType()) {
                case MOUSE_CLICK:
                    executeMouseClick(action);
                    break;
                case MOUSE_SCROLL:
                    executeMouseScroll(action);
                    break;
                case KEY_PRESS:
                    executeKeyPress(action);
                    break;
                case KEY_RELEASE:
                    executeKeyRelease(action);
                    break;
                case MOUSE_MOVE:
                    executeMouseMove(action);
                    break;
            }
        } catch (Exception e) {
            ScriptRecorderMod.LOGGER.error("Error executing action: " + action, e);
        }
    }

    // Mouse state tracking for proper press/release handling
    private boolean leftMousePressed = false;
    private boolean rightMousePressed = false;
    private boolean middleMousePressed = false;

    private void executeMouseClick(ScriptAction action) {
        try {
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer == null || mc.theWorld == null) return;

            int button = getMouseButtonCode(action.getButton());
            boolean isPressed = action.isPressed();

            ScriptRecorderMod.LOGGER.info("Executing mouse action: button=" + button + ", pressed=" + isPressed);

            if (button >= 0) {
                if (button == 0) { // Left click
                    handleLeftClick(mc, isPressed);
                } else if (button == 1) { // Right click
                    handleRightClick(mc, isPressed);
                } else if (button == 2) { // Middle click
                    handleMiddleClick(mc, isPressed);
                }
            }
        } catch (Exception e) {
            ScriptRecorderMod.LOGGER.error("Error in executeMouseClick", e);
        }
    }

    private void handleLeftClick(Minecraft mc, boolean pressed) {
        if (pressed && !leftMousePressed) {
            // Press left mouse
            leftMousePressed = true;
            net.minecraft.client.settings.KeyBinding.setKeyBindState(mc.gameSettings.keyBindAttack.getKeyCode(), true);
            ScriptRecorderMod.LOGGER.info("Left mouse PRESSED");

            // Schedule release after a minimum duration to ensure proper click registration
            scheduleMouseRelease(0, 50); // 50ms minimum press duration

        } else if (!pressed && leftMousePressed) {
            // Release left mouse
            leftMousePressed = false;
            net.minecraft.client.settings.KeyBinding.setKeyBindState(mc.gameSettings.keyBindAttack.getKeyCode(), false);
            ScriptRecorderMod.LOGGER.info("Left mouse RELEASED");
        }
    }

    private void handleRightClick(Minecraft mc, boolean pressed) {
        if (pressed && !rightMousePressed) {
            // Press right mouse
            rightMousePressed = true;
            net.minecraft.client.settings.KeyBinding.setKeyBindState(mc.gameSettings.keyBindUseItem.getKeyCode(), true);
            ScriptRecorderMod.LOGGER.info("Right mouse PRESSED");

            // Schedule release after a minimum duration
            scheduleMouseRelease(1, 50); // 50ms minimum press duration

        } else if (!pressed && rightMousePressed) {
            // Release right mouse
            rightMousePressed = false;
            net.minecraft.client.settings.KeyBinding.setKeyBindState(mc.gameSettings.keyBindUseItem.getKeyCode(), false);
            ScriptRecorderMod.LOGGER.info("Right mouse RELEASED");
        }
    }

    private void handleMiddleClick(Minecraft mc, boolean pressed) {
        if (pressed && !middleMousePressed) {
            // Press middle mouse
            middleMousePressed = true;
            net.minecraft.client.settings.KeyBinding.setKeyBindState(mc.gameSettings.keyBindPickBlock.getKeyCode(), true);
            ScriptRecorderMod.LOGGER.info("Middle mouse PRESSED");

            // Schedule release after a minimum duration
            scheduleMouseRelease(2, 50); // 50ms minimum press duration

        } else if (!pressed && middleMousePressed) {
            // Release middle mouse
            middleMousePressed = false;
            net.minecraft.client.settings.KeyBinding.setKeyBindState(mc.gameSettings.keyBindPickBlock.getKeyCode(), false);
            ScriptRecorderMod.LOGGER.info("Middle mouse RELEASED");
        }
    }

    private void scheduleMouseRelease(int button, long delayMs) {
        // Schedule a mouse release after the specified delay to ensure minimum press duration
        new Thread(() -> {
            try {
                Thread.sleep(delayMs);
                Minecraft mc = Minecraft.getMinecraft();

                // Only release if still pressed (prevents double-release)
                if (button == 0 && leftMousePressed) {
                    // Don't auto-release if there's a pending release event
                } else if (button == 1 && rightMousePressed) {
                    // Don't auto-release if there's a pending release event
                } else if (button == 2 && middleMousePressed) {
                    // Don't auto-release if there's a pending release event
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }).start();
    }

    private void executeMouseScroll(ScriptAction action) {
        try {
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer == null) return;

            // Simulate mouse scroll by changing the selected hotbar slot
            double scrollDelta = action.getDx();
            if (scrollDelta != 0) {
                int currentSlot = mc.thePlayer.inventory.currentItem;
                int newSlot = currentSlot;

                if (scrollDelta > 0) {
                    newSlot = (currentSlot + 1) % 9;
                } else if (scrollDelta < 0) {
                    newSlot = (currentSlot - 1 + 9) % 9;
                }

                mc.thePlayer.inventory.currentItem = newSlot;
                ScriptRecorderMod.LOGGER.debug("Executed mouse scroll: slot {} -> {}", currentSlot, newSlot);
            }
        } catch (Exception e) {
            ScriptRecorderMod.LOGGER.error("Error in executeMouseScroll", e);
        }
    }

    private void executeKeyPress(ScriptAction action) {
        try {
            int keyCode = action.getKeyCode();
            if (keyCode > 0) {
                // Use KeyMapping system for key simulation (1.20.4)
                net.minecraft.client.settings.KeyBinding.setKeyBindState(keyCode, true);
                ScriptRecorderMod.LOGGER.debug("Executed key press: " + keyCode);
            }
        } catch (Exception e) {
            ScriptRecorderMod.LOGGER.error("Error in executeKeyPress", e);
        }
    }

    private void executeKeyRelease(ScriptAction action) {
        try {
            int keyCode = action.getKeyCode();
            if (keyCode > 0) {
                // Use KeyMapping system for key simulation (1.20.4)
                net.minecraft.client.settings.KeyBinding.setKeyBindState(keyCode, false);
                ScriptRecorderMod.LOGGER.debug("Executed key release: " + keyCode);
            }
        } catch (Exception e) {
            ScriptRecorderMod.LOGGER.error("Error in executeKeyRelease", e);
        }
    }

    private void executeMouseMove(ScriptAction action) {
        try {
            Minecraft mc = Minecraft.getMinecraft();
            if (mc.thePlayer == null) return;

            int deltaX = action.getX();
            int deltaY = action.getY();

            if (deltaX != 0 || deltaY != 0) {
                // Smooth mouse movement by breaking large movements into smaller steps
                smoothMouseMovement(mc, deltaX, deltaY);
            }
        } catch (Exception e) {
            ScriptRecorderMod.LOGGER.error("Error in executeMouseMove", e);
        }
    }

    /**
     * Smoothly apply mouse movement by interpolating large movements into smaller steps
     */
    private void smoothMouseMovement(Minecraft mc, int totalDeltaX, int totalDeltaY) {
        // Calculate the magnitude of movement
        double magnitude = Math.sqrt(totalDeltaX * totalDeltaX + totalDeltaY * totalDeltaY);

        // If movement is very small, apply directly
        if (magnitude <= 2) {
            applySingleMouseMovement(mc, totalDeltaX, totalDeltaY);
            return;
        }

        // More aggressive smoothing - more steps for smoother movement
        int steps = Math.max(3, Math.min(20, (int)(magnitude / 1.5))); // 3-20 steps, more sensitive

        // Use floating point for more precise interpolation
        double currentX = 0.0;
        double currentY = 0.0;
        double stepX = (double)totalDeltaX / steps;
        double stepY = (double)totalDeltaY / steps;

        for (int i = 0; i < steps; i++) {
            if (!isPlaying) break; // Stop if playback was cancelled

            // Calculate precise interpolated movement for this step
            double targetX = (i + 1) * stepX;
            double targetY = (i + 1) * stepY;

            int stepDeltaX = (int)Math.round(targetX - currentX);
            int stepDeltaY = (int)Math.round(targetY - currentY);

            currentX += stepDeltaX;
            currentY += stepDeltaY;

            if (stepDeltaX != 0 || stepDeltaY != 0) {
                applySingleMouseMovement(mc, stepDeltaX, stepDeltaY);
            }

            // Even smaller delay for ultra-smooth movement
            if (i < steps - 1) {
                try {
                    Thread.sleep(1); // Maintain 1ms for smoothness without lag
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }
    }

    /**
     * Apply a single mouse movement step
     */
    private void applySingleMouseMovement(Minecraft mc, int deltaX, int deltaY) {
        if (deltaX == 0 && deltaY == 0) return;

        // Apply mouse movement to camera rotation (1.20.4)
        // Convert mouse delta to camera rotation
        float sensitivity = mc.gameSettings.mouseSensitivity * 0.6F + 0.2F;
        float mouseSensitivity = sensitivity * sensitivity * sensitivity * 8.0F;

        // Calculate yaw and pitch changes
        float yawChange = (float)deltaX * mouseSensitivity * 0.15F;
        float pitchChange = (float)deltaY * mouseSensitivity * 0.15F;

        // Apply the rotation to the player (1.20.4)
        float newYaw = mc.thePlayer.rotationYaw + yawChange;
        float newPitch = mc.thePlayer.rotationPitch + pitchChange;

        // Clamp pitch to prevent over-rotation
        newPitch = Math.max(-90.0F, Math.min(90.0F, newPitch));

        mc.thePlayer.rotationYaw = newYaw;
        mc.thePlayer.rotationPitch = newPitch;

        // Also update the camera
        if (mc.getRenderViewEntity() == mc.thePlayer) {
            mc.getRenderViewEntity().rotationYaw = newYaw;
            mc.getRenderViewEntity().rotationPitch = newPitch;
        }

        ScriptRecorderMod.LOGGER.debug("Executed smooth camera move: ({}, {}) -> yaw: {}, pitch: {}",
            deltaX, deltaY, yawChange, pitchChange);
    }

    private int getMouseButtonCode(String buttonName) {
        switch (buttonName.toLowerCase()) {
            case "left": return 0;
            case "right": return 1;
            case "middle": return 2;
            default: return -1;
        }
    }
    
    // File operations
    public boolean saveScript(RecordedScript script, String filename) {
        if (script == null || !script.isValid()) {
            ScriptRecorderMod.LOGGER.error("Cannot save invalid script");
            return false;
        }
        
        if (!filename.endsWith(SCRIPT_EXTENSION)) {
            filename += SCRIPT_EXTENSION;
        }
        
        Path filePath = scriptsDirectory.resolve(filename);
        
        try {
            String json = gson.toJson(script);
            Files.write(filePath, json.getBytes());
            ScriptRecorderMod.LOGGER.info("Script saved to: " + filePath);
            return true;
        } catch (IOException e) {
            ScriptRecorderMod.LOGGER.error("Failed to save script to " + filePath, e);
            return false;
        }
    }
    
    public RecordedScript loadScript(String filename) {
        if (!filename.endsWith(SCRIPT_EXTENSION)) {
            filename += SCRIPT_EXTENSION;
        }
        
        Path filePath = scriptsDirectory.resolve(filename);
        
        if (!Files.exists(filePath)) {
            ScriptRecorderMod.LOGGER.error("Script file not found: " + filePath);
            return null;
        }
        
        try {
            String json = new String(Files.readAllBytes(filePath));
            RecordedScript script = gson.fromJson(json, RecordedScript.class);
            
            if (script.isValid()) {
                // Store as the last script for playback
                lastScript = script;
                ScriptRecorderMod.LOGGER.info("Script loaded from: " + filePath);
                return script;
            } else {
                ScriptRecorderMod.LOGGER.error("Loaded script is invalid: " + script.getValidationError());
                return null;
            }
        } catch (IOException e) {
            ScriptRecorderMod.LOGGER.error("Failed to load script from " + filePath, e);
            return null;
        }
    }
    
    public List<String> getAvailableScripts() {
        List<String> scripts = new ArrayList<>();
        
        try {
            Files.list(scriptsDirectory)
                .filter(path -> path.toString().endsWith(SCRIPT_EXTENSION))
                .forEach(path -> {
                    String filename = path.getFileName().toString();
                    scripts.add(filename.substring(0, filename.length() - SCRIPT_EXTENSION.length()));
                });
        } catch (IOException e) {
            ScriptRecorderMod.LOGGER.error("Failed to list script files", e);
        }
        
        return scripts;
    }
    
    /**
     * Release all currently pressed keys to prevent sticking
     */
    private void releaseAllKeys() {
        try {
            // Release common movement keys (1.20.4 GLFW key codes)
            int[] commonKeys = {
                org.lwjgl.input.Keyboard.KEY_W, org.lwjgl.input.Keyboard.KEY_A, org.lwjgl.input.Keyboard.KEY_S, org.lwjgl.input.Keyboard.KEY_D, // WASD
                org.lwjgl.input.Keyboard.KEY_SPACE, org.lwjgl.input.Keyboard.KEY_LSHIFT, org.lwjgl.input.Keyboard.KEY_RSHIFT,   // Space, Shift
                org.lwjgl.input.Keyboard.KEY_LCONTROL, org.lwjgl.input.Keyboard.KEY_RCONTROL,                   // Control
                org.lwjgl.input.Keyboard.KEY_Q, org.lwjgl.input.Keyboard.KEY_E, org.lwjgl.input.Keyboard.KEY_R, org.lwjgl.input.Keyboard.KEY_F, // Common action keys
                org.lwjgl.input.Keyboard.KEY_1, org.lwjgl.input.Keyboard.KEY_2, org.lwjgl.input.Keyboard.KEY_3, org.lwjgl.input.Keyboard.KEY_4, // Number keys
                org.lwjgl.input.Keyboard.KEY_5, org.lwjgl.input.Keyboard.KEY_6, org.lwjgl.input.Keyboard.KEY_7, org.lwjgl.input.Keyboard.KEY_8,
                org.lwjgl.input.Keyboard.KEY_9, org.lwjgl.input.Keyboard.KEY_0
            };

            for (int keyCode : commonKeys) {
                net.minecraft.client.settings.KeyBinding.setKeyBindState(keyCode, false);
            }

            ScriptRecorderMod.LOGGER.debug("Released all keys to prevent sticking");
        } catch (Exception e) {
            ScriptRecorderMod.LOGGER.error("Error releasing keys", e);
        }
    }

    // Script management methods
    public boolean deleteScript(String scriptName) {
        try {
            String filename = scriptName.endsWith(SCRIPT_EXTENSION) ? scriptName : scriptName + SCRIPT_EXTENSION;
            Path scriptPath = scriptsDirectory.resolve(filename);

            if (Files.exists(scriptPath)) {
                Files.delete(scriptPath);
                ScriptRecorderMod.LOGGER.info("Deleted script: " + scriptName);
                return true;
            } else {
                ScriptRecorderMod.LOGGER.warn("Script not found for deletion: " + scriptName);
                return false;
            }
        } catch (IOException e) {
            ScriptRecorderMod.LOGGER.error("Failed to delete script: " + scriptName, e);
            return false;
        }
    }

    public boolean renameScript(String oldName, String newName) {
        try {
            String oldFilename = oldName.endsWith(SCRIPT_EXTENSION) ? oldName : oldName + SCRIPT_EXTENSION;
            String newFilename = newName.endsWith(SCRIPT_EXTENSION) ? newName : newName + SCRIPT_EXTENSION;

            Path oldPath = scriptsDirectory.resolve(oldFilename);
            Path newPath = scriptsDirectory.resolve(newFilename);

            if (Files.exists(oldPath) && !Files.exists(newPath)) {
                Files.move(oldPath, newPath);
                ScriptRecorderMod.LOGGER.info("Renamed script: " + oldName + " -> " + newName);
                return true;
            } else {
                ScriptRecorderMod.LOGGER.warn("Cannot rename script: " + oldName + " (source missing or target exists)");
                return false;
            }
        } catch (IOException e) {
            ScriptRecorderMod.LOGGER.error("Failed to rename script: " + oldName + " -> " + newName, e);
            return false;
        }
    }

    public boolean duplicateScript(String scriptName, String newName) {
        try {
            RecordedScript script = loadScript(scriptName);
            if (script != null) {
                script.setName(newName);
                return saveScript(script, newName);
            }
            return false;
        } catch (Exception e) {
            ScriptRecorderMod.LOGGER.error("Failed to duplicate script: " + scriptName, e);
            return false;
        }
    }

    // Enhanced export/import methods
    public boolean exportScript(RecordedScript script, String customName) {
        if (script == null || !script.isValid()) {
            ScriptRecorderMod.LOGGER.error("Cannot export invalid script");
            return false;
        }

        // Create a copy with the custom name
        RecordedScript exportScript = new RecordedScript(customName, script.getDescription());
        exportScript.setActions(script.getActions());
        exportScript.setLoopCount(script.getLoopCount());
        exportScript.setPlaybackSpeed(script.getPlaybackSpeed());

        return saveScript(exportScript, customName);
    }

    public RecordedScript importScript(String scriptName) {
        RecordedScript script = loadScript(scriptName);
        if (script != null) {
            ScriptRecorderMod.LOGGER.info("Successfully imported script: " + scriptName);
        }
        return script;
    }

    public boolean scriptExists(String scriptName) {
        String filename = scriptName.endsWith(SCRIPT_EXTENSION) ? scriptName : scriptName + SCRIPT_EXTENSION;
        Path filePath = scriptsDirectory.resolve(filename);
        return Files.exists(filePath);
    }

    // Getters for state
    public boolean isRecording() { return isRecording; }
    public boolean isPlaying() { return isPlaying; }
    public RecordedScript getCurrentScript() {
        // Return the last recorded/loaded script for playback
        return lastScript;
    }
    public RecordedScript getLastRecordedScript() {
        return lastScript;
    }
    public RecordedScript getPlayingScript() { return playingScript; }
    public String getScriptsDirectory() { return scriptsDirectory.toString(); }

    /**
     * Set the current script for playback
     */
    public void setCurrentScript(RecordedScript script) {
        this.lastScript = script;
    }
    public long getRecordingStartTime() { return recordingStartTime; }
}
