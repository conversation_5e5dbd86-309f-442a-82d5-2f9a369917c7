Java Launcher: C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin\java.exe
Arguments: '--input, C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\libs\waypoint-mod-1.20.4-3.5.0.jar, --output, C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\reobfJar\output.jar, --names, C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\reobfJar\mappings.tsrg, --lib, C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.20.4-49.2.0_mapped_official_1.20.4\forge-1.20.4-49.2.0_mapped_official_1.20.4.jar, --lib, C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_repo\versions\1.20.4\client-extra.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\fmlloader\1.20.4-49.2.0\67d8962f2233c0952d97f77209933ea5a4d405d4\fmlloader-1.20.4-49.2.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\JarJarSelector\0.3.26\1e032096a4d07e5bbb7b2f3b2beee8d5e02879e9\JarJarSelector-0.3.26.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\JarJarMetadata\0.3.26\6db967d6cce100e4abdeed0abfd594b9f42e0d10\JarJarMetadata-0.3.26.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\32.1.2-jre\5e64ec7e056456bef3a4bc4c6fdaef71e8ab6318\guava-32.1.2-jre.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\24.0.1\13c5c75c4206580aa4d683bffee658caae6c9f43\annotations-24.0.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\ca.weblite\java-objc-bridge\1.1\1227f9e0666314f9de41477e3ec277e542ed7f7b\java-objc-bridge-1.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\mergetool\1.1.7\aa7c6d9d187d585a8a215c288d65b0038be80b2a\mergetool-1.1.7-api.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\bootstrap\2.1.8\7ce55508387cf35eeb0eb4895bd84bec854ead93\bootstrap-2.1.8.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\bootstrap-api\2.1.8\f77d6527282dfc12e2460a3155467f40f9a2673b\bootstrap-api-2.1.8.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\accesstransformers\8.1.1\55b3a0604fec2b22c183a3e49b6f3da4e7aaaf27\accesstransformers-8.1.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.antlr\antlr4\4.9.1\e92af8ab33e428461927b484e90bb155a4f3a052\antlr4-4.9.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.antlr\antlr4-runtime\4.9.1\428664f05d2b7f7b7610204b5aa7c1763f62011a\antlr4-runtime-4.9.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\fmlcore\1.20.4-49.2.0\a3f0b582ea3603564b73e0c83c98a877eb562936\fmlcore-1.20.4-49.2.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\eventbus\6.2.27\1a7914b85a47ef9e8f333b694a6f8c0dc6a57db7\eventbus-6.2.27.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.jodah\typetools\0.6.3\a01aaa6ddaea9ec07ec4f209487b7a46a526283a\typetools-0.6.3.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\forgespi\7.1.3\a70210a2f27f3a8fa83b6c1d1abf7acfd4ec05da\forgespi-7.1.3.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\coremods\5.2.6\9528a9f2133ec5284122ac3d62cfff8bc8c6bba0\coremods-5.2.6.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.openjdk.nashorn\nashorn-core\15.4\f67f5ffaa5f5130cf6fb9b133da00c7df3b532a5\nashorn-core-15.4.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\modlauncher\10.1.2\58d8bf1f2b5f892bdcbc852098eccb687dcb55fb\modlauncher-10.1.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\mergetool-api\1.0\589fac8080d2eb657bb37f464151abcd25524d64\mergetool-api-1.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.electronwill.night-config\toml\3.6.7\ae2870aef9c31e5e392ffd662ea2d8a4e98a31bd\toml-3.6.7.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.electronwill.night-config\core\3.6.7\39bbc8694e2f1dc5eab92f01bc3248606ecd0ebd\core-3.6.7.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.maven\maven-artifact\3.8.8\c4324db6f73f14e1327da2a1acec293d75212ae7\maven-artifact-3.8.8.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecrell\terminalconsoleappender\1.2.0\96d02cd3b384ff015a8fef4223bcb4ccf1717c95\terminalconsoleappender-1.2.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-reader\3.25.1\46d87459a98d74be8c77645f2bc3c65bb2fc4f3a\jline-reader-3.25.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-terminal-jna\3.25.1\7e39efe62b2710b9f2bbefd2911e176b2efe1105\jline-terminal-jna-3.25.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-terminal\3.25.1\44e0e53397c39d01c525bad3735097596a7d889a\jline-terminal-3.25.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.spongepowered\mixin\0.8.5\9d1c0c3a304ae6697ecd477218fa61b850bf57fc\mixin-0.8.5.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\JarJarFileSystems\0.3.26\4b48cd32749b87a18afee96f33b825305feea289\JarJarFileSystems-0.3.26.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\fmlearlydisplay\1.20.4-49.2.0\d236d0544ae8053d3ac3be07f3bf0d65c7018891\fmlearlydisplay-1.20.4-49.2.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\javafmllanguage\1.20.4-49.2.0\4cb601d0a8894de9c15eab632f5fe3e3b9cb4523\javafmllanguage-1.20.4-49.2.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\lowcodelanguage\1.20.4-49.2.0\adabdb6a285ba61bcacb3b52d50a85863aa33917\lowcodelanguage-1.20.4-49.2.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\mclanguage\1.20.4-49.2.0\2fae82893317b483d9a54b3f5a4e31479afd8e8c\mclanguage-1.20.4-49.2.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\securemodules\2.2.21\a036e07b6064da635df3a3480591884fca3714d4\securemodules-2.2.21.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\unsafe\0.9.2\33bad02861cabd77c54a8c9d1205e8638bbbbb4f\unsafe-0.9.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-util\9.7.1\9e23359b598ec6b74b23e53110dd5c577adf2243\asm-util-9.7.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-commons\9.7.1\406c6a2225cfe1819f102a161e54cc16a5c24f75\asm-commons-9.7.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-analysis\9.7.1\f97a3b319f0ed6a8cd944dc79060d3912a28985f\asm-analysis-9.7.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-tree\9.7.1\3a53139787663b139de76b627fca0084ab60d32c\asm-tree-9.7.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm\9.7.1\f0ed132a49244b042cd0e15702ab9f2ce3cc8436\asm-9.7.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.oshi\oshi-core\6.4.5\943ba26de047eb6b28fff47f5ee939a34eb5fc8e\oshi-core-6.4.5.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.ibm.icu\icu4j\73.2\61ad4ef7f9131fcf6d25c34b817f90d6da06c9e9\icu4j-73.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\authlib\6.0.52\65085e2eb921c4a3ebdde3d248637f1776e6d80d\authlib-6.0.52.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\blocklist\1.0.10\5c685c5ffa94c4cd39496c7184c1d122e515ecef\blocklist-1.0.10.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\brigadier\1.2.9\73e324f2ee541493a5179abf367237faa782ed21\brigadier-1.2.9.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\datafixerupper\6.0.8\3ba4a30557a9b057760af4011f909ba619fc5125\datafixerupper-6.0.8.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\logging\1.1.1\832b8e6674a9b325a5175a3a6267dfaf34c85139\logging-1.1.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\patchy\2.2.10\da05971b07cbb379d002cf7eaec6a2048211fefc\patchy-2.2.10.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\text2speech\1.17.9\3cad216e3a7f0c19b4b394388bc9ffc446f13b14\text2speech-1.17.9.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.httpcomponents\httpclient\4.5.13\e5f6cae5ca7ecaac1ec2827a9e2d65ae2869cada\httpclient-4.5.13.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.16.0\4e3eb3d79888d76b54e28b350915b5dc3919c9de\commons-codec-1.16.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.13.0\8bb2bc9b4df17e2411533a0708a69f983bf5e83b\commons-io-2.13.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-logging\commons-logging\1.2\4bfc12adfe4842bf07b657f0369c4cb522955686\commons-logging-1.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-handler\4.1.97.Final\abb86c6906bf512bf2b797a41cd7d2e8d3cd7c36\netty-handler-4.1.97.Final.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-codec\4.1.97.Final\384ba4d75670befbedb45c4d3b497a93639c206d\netty-codec-4.1.97.Final.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-classes-epoll\4.1.97.Final\795da37ded759e862457a82d9d92c4d39ce8ecee\netty-transport-classes-epoll-4.1.97.Final.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-native-unix-common\4.1.97.Final\d469d84265ab70095b01b40886cabdd433b6e664\netty-transport-native-unix-common-4.1.97.Final.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport\4.1.97.Final\f37380d23c9bb079bc702910833b2fd532c9abd0\netty-transport-4.1.97.Final.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-buffer\4.1.97.Final\f8f3d8644afa5e6e1a40a3a6aeb9d9aa970ecb4f\netty-buffer-4.1.97.Final.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-resolver\4.1.97.Final\cec8348108dc76c47cf87c669d514be52c922144\netty-resolver-4.1.97.Final.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-common\4.1.97.Final\7cceacaf11df8dc63f23d0fb58e9d4640fc88404\netty-common-4.1.97.Final.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\it.unimi.dsi\fastutil\8.5.12\c24946d46824bd528054bface3231d2ecb7e95e8\fastutil-8.5.12.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.java.dev.jna\jna-platform\5.13.0\88e9a306715e9379f3122415ef4ae759a352640d\jna-platform-5.13.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.java.dev.jna\jna\5.14.0\67bf3eaea4f0718cb376a181a629e5f88fa1c9dd\jna-5.14.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.sf.jopt-simple\jopt-simple\5.0.4\4fdac2fbe92dfad86aa6e9301736f6b4342a3f5c\jopt-simple-5.0.4.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-compress\1.22\691a8b4e6cf4248c3bc72c8b719337d5cb7359fa\commons-compress-1.22.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-lang3\3.13.0\b7263237aa89c1f99b327197c41d0669707a462e\commons-lang3-3.13.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.httpcomponents\httpcore\4.4.16\51cf043c87253c9f58b539c9f7e44c8894223850\httpcore-4.4.16.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-core\2.19.0\3b6eeb4de4c49c0fe38a4ee27188ff5fee44d0bb\log4j-core-2.19.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-slf4j2-impl\2.19.0\5c04bfdd63ce9dceb2e284b81e96b6a70010ee10\log4j-slf4j2-impl-2.19.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-api\2.19.0\ea1b37f38c327596b216542bc636cfdc0b8036fa\log4j-api-2.19.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.joml\joml\1.10.5\22566d58af70ad3d72308bab63b8339906deb649\joml-1.10.5.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.2\757920418805fb90bfebb3d46b1d9e7669fca2eb\lwjgl-glfw-3.3.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.2\1251e3cb7e5d6159334cfb9244f789ce992f03b\lwjgl-glfw-3.3.2-natives-windows.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.2\e79c4857a887bd79ba78bdf2d422a7d333028a2d\lwjgl-glfw-3.3.2-natives-windows-arm64.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.2\17e1f9ec031ef72c2f7825c38eeb3a79c4d8bb17\lwjgl-glfw-3.3.2-natives-windows-x86.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.2\877e17e39ebcd58a9c956dc3b5b777813de0873a\lwjgl-jemalloc-3.3.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.2\db886c1f9e313c3fa2a25543b99ccd250d3f9fb5\lwjgl-jemalloc-3.3.2-natives-windows.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.2\598790de603c286dbc4068b27829eacc37592786\lwjgl-jemalloc-3.3.2-natives-windows-arm64.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.2\9b07558f81a5d54dfaeb861bab3ccc86bb4477c9\lwjgl-jemalloc-3.3.2-natives-windows-x86.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.2\ae5357ed6d934546d3533993ea84c0cfb75eed95\lwjgl-openal-3.3.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.2\e74f299a602192faaf14b917632e4cbbb493c940\lwjgl-openal-3.3.2-natives-windows.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.2\545ddec7959007a78b6662d616e00dacf00e1c29\lwjgl-openal-3.3.2-natives-windows-arm64.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.2\21fcb44d32ccf101017ec939fc740197677557d5\lwjgl-openal-3.3.2-natives-windows-x86.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.2\ee8e95be0b438602038bc1f02dc5e3d011b1b216\lwjgl-opengl-3.3.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.2\83cd34469d4e0bc335bf74c7f62206530a9480bf\lwjgl-opengl-3.3.2-natives-windows.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.2\21df035bf03dbf5001f92291b24dc951da513481\lwjgl-opengl-3.3.2-natives-windows-arm64.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.2\22fa4149159154b24f6c1bd46a342d4958a9fba1\lwjgl-opengl-3.3.2-natives-windows-x86.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.2\a2550795014d622b686e9caac50b14baa87d2c70\lwjgl-stb-3.3.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.2\1c4f4b8353bdb78c5264ab921436f03fc9aa1ba5\lwjgl-stb-3.3.2-natives-windows.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.2\c29df97c3cca97dc00d34e171936153764c9f78b\lwjgl-stb-3.3.2-natives-windows-arm64.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.2\a0de7bde6722fa68d25ba6afbd7395508c53c730\lwjgl-stb-3.3.2-natives-windows-x86.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.2\9f65c248dd77934105274fcf8351abb75b34327c\lwjgl-tinyfd-3.3.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.2\54a93ed247d20007a6f579355263fdc2c030753a\lwjgl-tinyfd-3.3.2-natives-windows.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.2\500f5daa3b731ca282d4b90aeafda94c528d3e27\lwjgl-tinyfd-3.3.2-natives-windows-arm64.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.2\c1dfa1c438e0262453e7bf625289540e5cbffb2\lwjgl-tinyfd-3.3.2-natives-windows-x86.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.2\4421d94af68e35dcaa31737a6fc59136a1e61b94\lwjgl-3.3.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.2\a55169ced70ffcd15f2162daf4a9c968578f6cd5\lwjgl-3.3.2-natives-windows.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.2\d900e4678449ba97ff46fa64b22e0376bf8cd00e\lwjgl-3.3.2-natives-windows-arm64.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.2\ed495259b2c8f068794da0ffedfa7ae7c130b3c5\lwjgl-3.3.2-natives-windows-x86.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.machinezoo.noexception\noexception\1.7.1\b65330c98e38a1f915fa54a6e5eca496505e3f0a\noexception-1.7.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\2.0.7\41eb7184ea9d556f23e18b5cb99cad1f8581fc00\slf4j-api-2.0.7.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.codehaus.plexus\plexus-utils\3.3.1\9b41b2b76b1bfe3774411fe22f5868058a9fc822\plexus-utils-3.3.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-native\3.25.1\7f58e474f7d94db5bf87b1fddf4fa646475779f2\jline-native-3.25.1.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.33.0\de2b60b62da487644fc11f734e73c8b0b431238f\checker-qual-3.33.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.18.0\89b684257096f548fa39a7df9fdaa409d4d4df91\error_prone_annotations-2.18.0.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\2.8\c85270e307e7b822f1086b93689124b89768e273\j2objc-annotations-2.8.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.antlr\ST4\4.3\92f2c1ad8d84abcbeead6cf7f2c53a04166293c2\ST4-4.3.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.antlr\antlr-runtime\3.5.2\cd9cd41361c155f3af0f653009dcecb08d8b4afd\antlr-runtime-3.5.2.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.abego.treelayout\org.abego.treelayout.core\1.0.3\457216e8e6578099ae63667bb1e4439235892028\org.abego.treelayout.core-1.0.3.jar, --lib, C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.glassfish\javax.json\1.0.4\3178f73569fd7a1e5ffc464e680f7a8cc784b85a\javax.json-1.0.4.jar, --disable-abstract-param'
Classpath:
 - C:\Users\<USER>\.gradle\caches\forge_gradle\maven_downloader\net\minecraftforge\ForgeAutoRenamingTool\1.1.0\ForgeAutoRenamingTool-1.1.0-all.jar
Working directory: C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\reobfJar
Main class: net.minecraftforge.fart.Main
====================================
Forge Auto Renaming Tool v1.1.0
lib: C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_user_repo\net\minecraftforge\forge\1.20.4-49.2.0_mapped_official_1.20.4\forge-1.20.4-49.2.0_mapped_official_1.20.4.jar
lib: C:\Users\<USER>\.gradle\caches\forge_gradle\minecraft_repo\versions\1.20.4\client-extra.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\fmlloader\1.20.4-49.2.0\67d8962f2233c0952d97f77209933ea5a4d405d4\fmlloader-1.20.4-49.2.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\JarJarSelector\0.3.26\1e032096a4d07e5bbb7b2f3b2beee8d5e02879e9\JarJarSelector-0.3.26.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\JarJarMetadata\0.3.26\6db967d6cce100e4abdeed0abfd594b9f42e0d10\JarJarMetadata-0.3.26.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\guava\32.1.2-jre\5e64ec7e056456bef3a4bc4c6fdaef71e8ab6318\guava-32.1.2-jre.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.findbugs\jsr305\3.0.2\25ea2e8b0c338a877313bd4672d3fe056ea78f0d\jsr305-3.0.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jetbrains\annotations\24.0.1\13c5c75c4206580aa4d683bffee658caae6c9f43\annotations-24.0.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\ca.weblite\java-objc-bridge\1.1\1227f9e0666314f9de41477e3ec277e542ed7f7b\java-objc-bridge-1.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\mergetool\1.1.7\aa7c6d9d187d585a8a215c288d65b0038be80b2a\mergetool-1.1.7-api.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\bootstrap\2.1.8\7ce55508387cf35eeb0eb4895bd84bec854ead93\bootstrap-2.1.8.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\bootstrap-api\2.1.8\f77d6527282dfc12e2460a3155467f40f9a2673b\bootstrap-api-2.1.8.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\accesstransformers\8.1.1\55b3a0604fec2b22c183a3e49b6f3da4e7aaaf27\accesstransformers-8.1.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.antlr\antlr4\4.9.1\e92af8ab33e428461927b484e90bb155a4f3a052\antlr4-4.9.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.antlr\antlr4-runtime\4.9.1\428664f05d2b7f7b7610204b5aa7c1763f62011a\antlr4-runtime-4.9.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\fmlcore\1.20.4-49.2.0\a3f0b582ea3603564b73e0c83c98a877eb562936\fmlcore-1.20.4-49.2.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\eventbus\6.2.27\1a7914b85a47ef9e8f333b694a6f8c0dc6a57db7\eventbus-6.2.27.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.jodah\typetools\0.6.3\a01aaa6ddaea9ec07ec4f209487b7a46a526283a\typetools-0.6.3.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\forgespi\7.1.3\a70210a2f27f3a8fa83b6c1d1abf7acfd4ec05da\forgespi-7.1.3.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\coremods\5.2.6\9528a9f2133ec5284122ac3d62cfff8bc8c6bba0\coremods-5.2.6.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.openjdk.nashorn\nashorn-core\15.4\f67f5ffaa5f5130cf6fb9b133da00c7df3b532a5\nashorn-core-15.4.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\modlauncher\10.1.2\58d8bf1f2b5f892bdcbc852098eccb687dcb55fb\modlauncher-10.1.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\mergetool-api\1.0\589fac8080d2eb657bb37f464151abcd25524d64\mergetool-api-1.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.electronwill.night-config\toml\3.6.7\ae2870aef9c31e5e392ffd662ea2d8a4e98a31bd\toml-3.6.7.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.electronwill.night-config\core\3.6.7\39bbc8694e2f1dc5eab92f01bc3248606ecd0ebd\core-3.6.7.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.maven\maven-artifact\3.8.8\c4324db6f73f14e1327da2a1acec293d75212ae7\maven-artifact-3.8.8.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecrell\terminalconsoleappender\1.2.0\96d02cd3b384ff015a8fef4223bcb4ccf1717c95\terminalconsoleappender-1.2.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-reader\3.25.1\46d87459a98d74be8c77645f2bc3c65bb2fc4f3a\jline-reader-3.25.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-terminal-jna\3.25.1\7e39efe62b2710b9f2bbefd2911e176b2efe1105\jline-terminal-jna-3.25.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-terminal\3.25.1\44e0e53397c39d01c525bad3735097596a7d889a\jline-terminal-3.25.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.spongepowered\mixin\0.8.5\9d1c0c3a304ae6697ecd477218fa61b850bf57fc\mixin-0.8.5.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\JarJarFileSystems\0.3.26\4b48cd32749b87a18afee96f33b825305feea289\JarJarFileSystems-0.3.26.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\fmlearlydisplay\1.20.4-49.2.0\d236d0544ae8053d3ac3be07f3bf0d65c7018891\fmlearlydisplay-1.20.4-49.2.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\javafmllanguage\1.20.4-49.2.0\4cb601d0a8894de9c15eab632f5fe3e3b9cb4523\javafmllanguage-1.20.4-49.2.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\lowcodelanguage\1.20.4-49.2.0\adabdb6a285ba61bcacb3b52d50a85863aa33917\lowcodelanguage-1.20.4-49.2.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\mclanguage\1.20.4-49.2.0\2fae82893317b483d9a54b3f5a4e31479afd8e8c\mclanguage-1.20.4-49.2.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\failureaccess\1.0.1\1dcf1de382a0bf95a3d8b0849546c88bac1292c9\failureaccess-1.0.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\securemodules\2.2.21\a036e07b6064da635df3a3480591884fca3714d4\securemodules-2.2.21.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.minecraftforge\unsafe\0.9.2\33bad02861cabd77c54a8c9d1205e8638bbbbb4f\unsafe-0.9.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-util\9.7.1\9e23359b598ec6b74b23e53110dd5c577adf2243\asm-util-9.7.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-commons\9.7.1\406c6a2225cfe1819f102a161e54cc16a5c24f75\asm-commons-9.7.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-analysis\9.7.1\f97a3b319f0ed6a8cd944dc79060d3912a28985f\asm-analysis-9.7.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm-tree\9.7.1\3a53139787663b139de76b627fca0084ab60d32c\asm-tree-9.7.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.ow2.asm\asm\9.7.1\f0ed132a49244b042cd0e15702ab9f2ce3cc8436\asm-9.7.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.github.oshi\oshi-core\6.4.5\943ba26de047eb6b28fff47f5ee939a34eb5fc8e\oshi-core-6.4.5.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.code.gson\gson\2.10.1\b3add478d4382b78ea20b1671390a858002feb6c\gson-2.10.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.ibm.icu\icu4j\73.2\61ad4ef7f9131fcf6d25c34b817f90d6da06c9e9\icu4j-73.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\authlib\6.0.52\65085e2eb921c4a3ebdde3d248637f1776e6d80d\authlib-6.0.52.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\blocklist\1.0.10\5c685c5ffa94c4cd39496c7184c1d122e515ecef\blocklist-1.0.10.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\brigadier\1.2.9\73e324f2ee541493a5179abf367237faa782ed21\brigadier-1.2.9.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\datafixerupper\6.0.8\3ba4a30557a9b057760af4011f909ba619fc5125\datafixerupper-6.0.8.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\logging\1.1.1\832b8e6674a9b325a5175a3a6267dfaf34c85139\logging-1.1.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\patchy\2.2.10\da05971b07cbb379d002cf7eaec6a2048211fefc\patchy-2.2.10.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.mojang\text2speech\1.17.9\3cad216e3a7f0c19b4b394388bc9ffc446f13b14\text2speech-1.17.9.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.httpcomponents\httpclient\4.5.13\e5f6cae5ca7ecaac1ec2827a9e2d65ae2869cada\httpclient-4.5.13.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-codec\commons-codec\1.16.0\4e3eb3d79888d76b54e28b350915b5dc3919c9de\commons-codec-1.16.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-io\commons-io\2.13.0\8bb2bc9b4df17e2411533a0708a69f983bf5e83b\commons-io-2.13.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\commons-logging\commons-logging\1.2\4bfc12adfe4842bf07b657f0369c4cb522955686\commons-logging-1.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-handler\4.1.97.Final\abb86c6906bf512bf2b797a41cd7d2e8d3cd7c36\netty-handler-4.1.97.Final.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-codec\4.1.97.Final\384ba4d75670befbedb45c4d3b497a93639c206d\netty-codec-4.1.97.Final.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-classes-epoll\4.1.97.Final\795da37ded759e862457a82d9d92c4d39ce8ecee\netty-transport-classes-epoll-4.1.97.Final.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport-native-unix-common\4.1.97.Final\d469d84265ab70095b01b40886cabdd433b6e664\netty-transport-native-unix-common-4.1.97.Final.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-transport\4.1.97.Final\f37380d23c9bb079bc702910833b2fd532c9abd0\netty-transport-4.1.97.Final.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-buffer\4.1.97.Final\f8f3d8644afa5e6e1a40a3a6aeb9d9aa970ecb4f\netty-buffer-4.1.97.Final.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-resolver\4.1.97.Final\cec8348108dc76c47cf87c669d514be52c922144\netty-resolver-4.1.97.Final.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\io.netty\netty-common\4.1.97.Final\7cceacaf11df8dc63f23d0fb58e9d4640fc88404\netty-common-4.1.97.Final.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\it.unimi.dsi\fastutil\8.5.12\c24946d46824bd528054bface3231d2ecb7e95e8\fastutil-8.5.12.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.java.dev.jna\jna-platform\5.13.0\88e9a306715e9379f3122415ef4ae759a352640d\jna-platform-5.13.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.java.dev.jna\jna\5.14.0\67bf3eaea4f0718cb376a181a629e5f88fa1c9dd\jna-5.14.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\net.sf.jopt-simple\jopt-simple\5.0.4\4fdac2fbe92dfad86aa6e9301736f6b4342a3f5c\jopt-simple-5.0.4.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-compress\1.22\691a8b4e6cf4248c3bc72c8b719337d5cb7359fa\commons-compress-1.22.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.commons\commons-lang3\3.13.0\b7263237aa89c1f99b327197c41d0669707a462e\commons-lang3-3.13.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.httpcomponents\httpcore\4.4.16\51cf043c87253c9f58b539c9f7e44c8894223850\httpcore-4.4.16.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-core\2.19.0\3b6eeb4de4c49c0fe38a4ee27188ff5fee44d0bb\log4j-core-2.19.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-slf4j2-impl\2.19.0\5c04bfdd63ce9dceb2e284b81e96b6a70010ee10\log4j-slf4j2-impl-2.19.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.apache.logging.log4j\log4j-api\2.19.0\ea1b37f38c327596b216542bc636cfdc0b8036fa\log4j-api-2.19.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.joml\joml\1.10.5\22566d58af70ad3d72308bab63b8339906deb649\joml-1.10.5.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.2\757920418805fb90bfebb3d46b1d9e7669fca2eb\lwjgl-glfw-3.3.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.2\1251e3cb7e5d6159334cfb9244f789ce992f03b\lwjgl-glfw-3.3.2-natives-windows.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.2\e79c4857a887bd79ba78bdf2d422a7d333028a2d\lwjgl-glfw-3.3.2-natives-windows-arm64.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-glfw\3.3.2\17e1f9ec031ef72c2f7825c38eeb3a79c4d8bb17\lwjgl-glfw-3.3.2-natives-windows-x86.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.2\877e17e39ebcd58a9c956dc3b5b777813de0873a\lwjgl-jemalloc-3.3.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.2\db886c1f9e313c3fa2a25543b99ccd250d3f9fb5\lwjgl-jemalloc-3.3.2-natives-windows.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.2\598790de603c286dbc4068b27829eacc37592786\lwjgl-jemalloc-3.3.2-natives-windows-arm64.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-jemalloc\3.3.2\9b07558f81a5d54dfaeb861bab3ccc86bb4477c9\lwjgl-jemalloc-3.3.2-natives-windows-x86.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.2\ae5357ed6d934546d3533993ea84c0cfb75eed95\lwjgl-openal-3.3.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.2\e74f299a602192faaf14b917632e4cbbb493c940\lwjgl-openal-3.3.2-natives-windows.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.2\545ddec7959007a78b6662d616e00dacf00e1c29\lwjgl-openal-3.3.2-natives-windows-arm64.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-openal\3.3.2\21fcb44d32ccf101017ec939fc740197677557d5\lwjgl-openal-3.3.2-natives-windows-x86.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.2\ee8e95be0b438602038bc1f02dc5e3d011b1b216\lwjgl-opengl-3.3.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.2\83cd34469d4e0bc335bf74c7f62206530a9480bf\lwjgl-opengl-3.3.2-natives-windows.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.2\21df035bf03dbf5001f92291b24dc951da513481\lwjgl-opengl-3.3.2-natives-windows-arm64.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-opengl\3.3.2\22fa4149159154b24f6c1bd46a342d4958a9fba1\lwjgl-opengl-3.3.2-natives-windows-x86.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.2\a2550795014d622b686e9caac50b14baa87d2c70\lwjgl-stb-3.3.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.2\1c4f4b8353bdb78c5264ab921436f03fc9aa1ba5\lwjgl-stb-3.3.2-natives-windows.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.2\c29df97c3cca97dc00d34e171936153764c9f78b\lwjgl-stb-3.3.2-natives-windows-arm64.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-stb\3.3.2\a0de7bde6722fa68d25ba6afbd7395508c53c730\lwjgl-stb-3.3.2-natives-windows-x86.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.2\9f65c248dd77934105274fcf8351abb75b34327c\lwjgl-tinyfd-3.3.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.2\54a93ed247d20007a6f579355263fdc2c030753a\lwjgl-tinyfd-3.3.2-natives-windows.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.2\500f5daa3b731ca282d4b90aeafda94c528d3e27\lwjgl-tinyfd-3.3.2-natives-windows-arm64.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl-tinyfd\3.3.2\c1dfa1c438e0262453e7bf625289540e5cbffb2\lwjgl-tinyfd-3.3.2-natives-windows-x86.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.2\4421d94af68e35dcaa31737a6fc59136a1e61b94\lwjgl-3.3.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.2\a55169ced70ffcd15f2162daf4a9c968578f6cd5\lwjgl-3.3.2-natives-windows.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.2\d900e4678449ba97ff46fa64b22e0376bf8cd00e\lwjgl-3.3.2-natives-windows-arm64.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.lwjgl\lwjgl\3.3.2\ed495259b2c8f068794da0ffedfa7ae7c130b3c5\lwjgl-3.3.2-natives-windows-x86.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.machinezoo.noexception\noexception\1.7.1\b65330c98e38a1f915fa54a6e5eca496505e3f0a\noexception-1.7.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.slf4j\slf4j-api\2.0.7\41eb7184ea9d556f23e18b5cb99cad1f8581fc00\slf4j-api-2.0.7.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.codehaus.plexus\plexus-utils\3.3.1\9b41b2b76b1bfe3774411fe22f5868058a9fc822\plexus-utils-3.3.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.jline\jline-native\3.25.1\7f58e474f7d94db5bf87b1fddf4fa646475779f2\jline-native-3.25.1.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\b421526c5f297295adef1c886e5246c39d4ac629\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.checkerframework\checker-qual\3.33.0\de2b60b62da487644fc11f734e73c8b0b431238f\checker-qual-3.33.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.errorprone\error_prone_annotations\2.18.0\89b684257096f548fa39a7df9fdaa409d4d4df91\error_prone_annotations-2.18.0.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\com.google.j2objc\j2objc-annotations\2.8\c85270e307e7b822f1086b93689124b89768e273\j2objc-annotations-2.8.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.antlr\ST4\4.3\92f2c1ad8d84abcbeead6cf7f2c53a04166293c2\ST4-4.3.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.antlr\antlr-runtime\3.5.2\cd9cd41361c155f3af0f653009dcecb08d8b4afd\antlr-runtime-3.5.2.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.abego.treelayout\org.abego.treelayout.core\1.0.3\457216e8e6578099ae63667bb1e4439235892028\org.abego.treelayout.core-1.0.3.jar
lib: C:\Users\<USER>\.gradle\caches\modules-2\files-2.1\org.glassfish\javax.json\1.0.4\3178f73569fd7a1e5ffc464e680f7a8cc784b85a\javax.json-1.0.4.jar
log: null
input: C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\libs\waypoint-mod-1.20.4-3.5.0.jar
output: C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\reobfJar\output.jar
threads: 32
Names: C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\reobfJar\mappings.tsrg(reversed: false)
Fix Annotations: false
Fix Records: false
Fix Identifiers: false
Fix SourceFile: false
Fix Line Numbers: false
Strip codesigning signatures: false
Adding Libraries to Inheritance
Reading Input: C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\libs\waypoint-mod-1.20.4-3.5.0.jar
Adding input to inheritance map
Processing entries
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/WaypointManagerScreen/keyPressed(III)Z from net/minecraft/client/gui/screens/Screen/keyPressed(III)Z: keyPressed -> m_7933_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/PanelBasedGUI/keyPressed(III)Z from net/minecraft/client/gui/screens/Screen/keyPressed(III)Z: keyPressed -> m_7933_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/PanelBasedGUI/isPauseScreen()Z from net/minecraft/client/gui/screens/Screen/isPauseScreen()Z: isPauseScreen -> m_7043_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/ScriptRecorderScreen/keyPressed(III)Z from net/minecraft/client/gui/screens/Screen/keyPressed(III)Z: keyPressed -> m_7933_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/ScriptRecorderScreen/isPauseScreen()Z from net/minecraft/client/gui/screens/Screen/isPauseScreen()Z: isPauseScreen -> m_7043_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/ScriptRecorderScreen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V from net/minecraft/client/gui/screens/Screen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V: render -> m_88315_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/ScriptRecorderScreen/init()V from net/minecraft/client/gui/screens/Screen/init()V: init -> m_7856_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/ScriptRecorderScreen/tick()V from net/minecraft/client/gui/screens/Screen/tick()V: tick -> m_86600_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/SimpleScriptRecorderScreen/keyPressed(III)Z from net/minecraft/client/gui/screens/Screen/keyPressed(III)Z: keyPressed -> m_7933_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/ToggleSprintScreen/isPauseScreen()Z from net/minecraft/client/gui/screens/Screen/isPauseScreen()Z: isPauseScreen -> m_7043_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/WaypointManagerScreen/isPauseScreen()Z from net/minecraft/client/gui/screens/Screen/isPauseScreen()Z: isPauseScreen -> m_7043_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/SimpleScriptRecorderScreen/isPauseScreen()Z from net/minecraft/client/gui/screens/Screen/isPauseScreen()Z: isPauseScreen -> m_7043_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/ToggleSprintScreen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V from net/minecraft/client/gui/screens/Screen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V: render -> m_88315_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/WaypointManagerScreen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V from net/minecraft/client/gui/screens/Screen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V: render -> m_88315_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/TabbedScriptRecorderScreen/keyPressed(III)Z from net/minecraft/client/gui/screens/Screen/keyPressed(III)Z: keyPressed -> m_7933_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/SimpleScriptRecorderScreen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V from net/minecraft/client/gui/screens/Screen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V: render -> m_88315_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/ToggleSprintScreen/init()V from net/minecraft/client/gui/screens/Screen/init()V: init -> m_7856_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/PanelBasedGUI/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V from net/minecraft/client/gui/screens/Screen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V: render -> m_88315_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/WaypointManagerScreen/init()V from net/minecraft/client/gui/screens/Screen/init()V: init -> m_7856_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/TabbedScriptRecorderScreen/isPauseScreen()Z from net/minecraft/client/gui/screens/Screen/isPauseScreen()Z: isPauseScreen -> m_7043_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/PanelBasedGUI/onClose()V from net/minecraft/client/gui/screens/Screen/onClose()V: onClose -> m_7379_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/SimpleScriptRecorderScreen/init()V from net/minecraft/client/gui/screens/Screen/init()V: init -> m_7856_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/WaypointManagerScreen/mouseClicked(DDI)Z from net/minecraft/client/gui/components/events/ContainerEventHandler/mouseClicked(DDI)Z: mouseClicked -> m_6375_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/SimpleScriptRecorderScreen/mouseClicked(DDI)Z from net/minecraft/client/gui/components/events/ContainerEventHandler/mouseClicked(DDI)Z: mouseClicked -> m_6375_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/TabbedScriptRecorderScreen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V from net/minecraft/client/gui/screens/Screen/render(Lnet/minecraft/client/gui/GuiGraphics;IIF)V: render -> m_88315_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/PanelBasedGUI/init()V from net/minecraft/client/gui/screens/Screen/init()V: init -> m_7856_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/WaypointManagerScreen/charTyped(CI)Z from net/minecraft/client/gui/components/events/ContainerEventHandler/charTyped(CI)Z: charTyped -> m_5534_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/TabbedScriptRecorderScreen/onClose()V from net/minecraft/client/gui/screens/Screen/onClose()V: onClose -> m_7379_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/SimpleScriptRecorderScreen/charTyped(CI)Z from net/minecraft/client/gui/components/events/ContainerEventHandler/charTyped(CI)Z: charTyped -> m_5534_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/PanelBasedGUI/mouseClicked(DDI)Z from net/minecraft/client/gui/components/events/ContainerEventHandler/mouseClicked(DDI)Z: mouseClicked -> m_6375_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/TabbedScriptRecorderScreen/init()V from net/minecraft/client/gui/screens/Screen/init()V: init -> m_7856_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/PanelBasedGUI/tick()V from net/minecraft/client/gui/screens/Screen/tick()V: tick -> m_86600_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/PanelBasedGUI/mouseDragged(DDIDD)Z from net/minecraft/client/gui/components/events/ContainerEventHandler/mouseDragged(DDIDD)Z: mouseDragged -> m_7979_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/TabbedScriptRecorderScreen/mouseClicked(DDI)Z from net/minecraft/client/gui/components/events/ContainerEventHandler/mouseClicked(DDI)Z: mouseClicked -> m_6375_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/PanelBasedGUI/charTyped(CI)Z from net/minecraft/client/gui/components/events/ContainerEventHandler/charTyped(CI)Z: charTyped -> m_5534_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/TabbedScriptRecorderScreen/tick()V from net/minecraft/client/gui/screens/Screen/tick()V: tick -> m_86600_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/TabbedScriptRecorderScreen/charTyped(CI)Z from net/minecraft/client/gui/components/events/ContainerEventHandler/charTyped(CI)Z: charTyped -> m_5534_
Conflicting propagated mapping for com/scriptrecorder/mod/client/gui/PanelBasedGUI/mouseReleased(DDI)Z from net/minecraft/client/gui/components/events/ContainerEventHandler/mouseReleased(DDI)Z: mouseReleased -> m_6348_
Conflicting propagated mapping for net/minecraft/world/entity/LivingEntity/lambda$addAdditionalSaveData$3(Lnet/minecraft/nbt/CompoundTag;Lnet/minecraft/nbt/Tag;)V from net/minecraft/world/entity/player/Player/lambda$addAdditionalSaveData$3(Lnet/minecraft/nbt/CompoundTag;Lnet/minecraft/nbt/Tag;)V: lambda$addAdditionalSaveData$3 -> m_219754_
Adding extras
Sorting
Writing Output: C:\Users\<USER>\Documents\augment-projects\minecraft scripts\build\reobfJar\output.jar
  META-INF/
  META-INF/MANIFEST.MF
  META-INF/mods.toml
  assets/
  assets/togglesprintmod/
  assets/togglesprintmod/lang/
  assets/togglesprintmod/lang/en_us.json
  assets/waypointmod/
  assets/waypointmod/lang/
  assets/waypointmod/lang/en_us.json
  com/
  com/scriptrecorder/
  com/scriptrecorder/mod/
  com/scriptrecorder/mod/ScriptRecorderMod.class
  com/scriptrecorder/mod/client/
  com/scriptrecorder/mod/client/KeyBindings.class
  com/scriptrecorder/mod/client/ScriptRecorderClient.class
  com/scriptrecorder/mod/client/autoclicker/
  com/scriptrecorder/mod/client/autoclicker/AutoClicker$ClickMethod.class
  com/scriptrecorder/mod/client/autoclicker/AutoClicker.class
  com/scriptrecorder/mod/client/autofisher/
  com/scriptrecorder/mod/client/autofisher/AutoFisher$FishingMode.class
  com/scriptrecorder/mod/client/autofisher/AutoFisher.class
  com/scriptrecorder/mod/client/cheststealer/
  com/scriptrecorder/mod/client/cheststealer/ChestStealer$TitleCheckType.class
  com/scriptrecorder/mod/client/cheststealer/ChestStealer.class
  com/scriptrecorder/mod/client/gui/
  com/scriptrecorder/mod/client/gui/PanelBasedGUI.class
  com/scriptrecorder/mod/client/gui/ScriptRecorderScreen.class
  com/scriptrecorder/mod/client/gui/SimpleScriptRecorderScreen.class
  com/scriptrecorder/mod/client/gui/TabbedScriptRecorderScreen$Tab.class
  com/scriptrecorder/mod/client/gui/TabbedScriptRecorderScreen.class
  com/scriptrecorder/mod/client/gui/ToggleSprintScreen.class
  com/scriptrecorder/mod/client/gui/WaypointManagerScreen.class
  com/scriptrecorder/mod/client/gui/panel/
  com/scriptrecorder/mod/client/gui/panel/AutoClickerPanel.class
  com/scriptrecorder/mod/client/gui/panel/AutoFisherPanel.class
  com/scriptrecorder/mod/client/gui/panel/ChestStealerPanel.class
  com/scriptrecorder/mod/client/gui/panel/DragInfo.class
  com/scriptrecorder/mod/client/gui/panel/ESPTracersPanel.class
  com/scriptrecorder/mod/client/gui/panel/Panel$ResizeHandle.class
  com/scriptrecorder/mod/client/gui/panel/Panel.class
  com/scriptrecorder/mod/client/gui/panel/PanelManager.class
  com/scriptrecorder/mod/client/gui/panel/ReachPanel.class
  com/scriptrecorder/mod/client/gui/panel/ScriptManagerPanel.class
  com/scriptrecorder/mod/client/gui/panel/ScriptRecorderPanel.class
  com/scriptrecorder/mod/client/gui/panel/ServerPingerPanel.class
  com/scriptrecorder/mod/client/gui/panel/SettingsPanel.class
  com/scriptrecorder/mod/client/overlay/
  com/scriptrecorder/mod/client/overlay/CPSOverlay.class
  com/scriptrecorder/mod/client/overlay/KeystrokeOverlay$KeyPress.class
  com/scriptrecorder/mod/client/overlay/KeystrokeOverlay.class
  com/scriptrecorder/mod/client/overlay/TracerESPOverlay$ESPProjection.class
  com/scriptrecorder/mod/client/overlay/TracerESPOverlay$ProjectionResult.class
  com/scriptrecorder/mod/client/overlay/TracerESPOverlay.class
  com/scriptrecorder/mod/client/overlay/WaypointRenderer.class
  com/scriptrecorder/mod/client/reach/
  com/scriptrecorder/mod/client/reach/Reach$ServerType.class
  com/scriptrecorder/mod/client/reach/Reach.class
  com/scriptrecorder/mod/client/serverpinger/
  com/scriptrecorder/mod/client/serverpinger/ServerInfo.class
  com/scriptrecorder/mod/client/serverpinger/ServerPinger.class
  com/scriptrecorder/mod/client/settings/
  com/scriptrecorder/mod/client/settings/ModSettings.class
  com/scriptrecorder/mod/commands/
  com/scriptrecorder/mod/commands/WaypointCommand.class
  com/scriptrecorder/mod/commands/WaypointCommands.class
  com/scriptrecorder/mod/recording/
  com/scriptrecorder/mod/recording/RecordedScript.class
  com/scriptrecorder/mod/recording/ScriptAction$ActionType.class
  com/scriptrecorder/mod/recording/ScriptAction.class
  com/scriptrecorder/mod/recording/ScriptManager$1.class
  com/scriptrecorder/mod/recording/ScriptManager.class
  com/scriptrecorder/mod/waypoints/
  com/scriptrecorder/mod/waypoints/Waypoint.class
  com/scriptrecorder/mod/waypoints/WaypointManager$1.class
  com/scriptrecorder/mod/waypoints/WaypointManager.class
  pack.mcmeta
