package com.scriptrecorder.mod.client.gui.panel;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.settings.ModSettings;
import com.scriptrecorder.mod.recording.RecordedScript;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.network.chat.Component;
import java.awt.Color;

/**
 * Panel for script recording controls
 */
public class ScriptRecorderPanel extends Panel {
    
    // UI Components
    private Button recordButton;
    private Button playButton;
    private Button stopButton;
    private EditBox loopCountBox;
    private Button infiniteLoopButton;
    
    // State
    private int loopCount = 1;
    private boolean infiniteLoop = false;
    
    // Colors
    private static final Color RECORD_COLOR = new Color(220, 50, 50);
    private static final Color PLAY_COLOR = new Color(50, 220, 50);
    private static final Color STOP_COLOR = new Color(200, 200, 50);
    
    public ScriptRecorderPanel() {
        super("Script Recorder", 10, 10, 280, 140);
        // Load settings from ModSettings
        loadSettings();
        initializeComponents();
    }

    /**
     * Load settings from ModSettings
     */
    private void loadSettings() {
        loopCount = ModSettings.getInstance().loopCount;
        infiniteLoop = ModSettings.getInstance().infiniteLoop;
    }
    
    private void initializeComponents() {
        clearWidgets();
        
        // Calculate positions relative to panel
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Record button
        recordButton = Button.builder(
            getRecordButtonText(),
            button -> onRecordPressed())
            .bounds(contentX, contentY, 80, 24)
            .build();
        addWidget(recordButton);
        
        // Play button
        playButton = Button.builder(
            getPlayButtonText(),
            button -> onPlayPressed())
            .bounds(contentX + 90, contentY, 80, 24)
            .build();
        addWidget(playButton);
        
        // Stop button
        stopButton = Button.builder(
            Component.literal("Stop"),
            button -> onStopPressed())
            .bounds(contentX + 180, contentY, 80, 24)
            .build();
        addWidget(stopButton);
        
        // Loop count input
        loopCountBox = new EditBox(Minecraft.getInstance().font, 
                                  contentX, contentY + 35, 60, 20, 
                                  Component.literal("Loop Count"));
        loopCountBox.setValue(infiniteLoop ? "∞" : String.valueOf(loopCount));
        loopCountBox.setMaxLength(3);
        addWidget(loopCountBox);
        
        // Infinite loop toggle
        infiniteLoopButton = Button.builder(
            Component.literal(infiniteLoop ? "∞ ON" : "∞ OFF"),
            button -> toggleInfiniteLoop())
            .bounds(contentX + 70, contentY + 35, 50, 20)
            .build();
        addWidget(infiniteLoopButton);
    }
    
    @Override
    protected void renderContent(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Update component positions when panel moves
        updateComponentPositions();
        
        // Draw status information
        int textX = (int)(x + 10);
        int textY = (int)(y + titleBarHeight + 70);
        
        // Draw current script info
        RecordedScript script = ScriptRecorderMod.scriptManager.getCurrentScript();
        if (script != null) {
            String info = String.format("Route: %d actions | %s",
                script.getTotalActions(), script.getFormattedDuration());
            guiGraphics.drawString(Minecraft.getInstance().font, info, 
                                 textX, textY, Color.WHITE.getRGB());
        }
        
        // Draw status
        String status;
        Color statusColor;
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            status = "● RECORDING";
            statusColor = RECORD_COLOR;
        } else if (ScriptRecorderMod.scriptManager.isPlaying()) {
            status = "▶ PLAYING";
            statusColor = PLAY_COLOR;
        } else {
            status = "⏸ READY";
            statusColor = Color.WHITE;
        }
        
        guiGraphics.drawString(Minecraft.getInstance().font, status, 
                             textX, textY + 12, statusColor.getRGB());
        
        // Draw loop info
        String loopInfo = infiniteLoop ? "Loops: ∞" : "Loops: " + loopCount;
        guiGraphics.drawString(Minecraft.getInstance().font, loopInfo, 
                             textX + 150, textY + 12, Color.LIGHT_GRAY.getRGB());
    }
    
    @Override
    protected void updateComponentPositions() {
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Update button positions
        if (recordButton != null) {
            recordButton.setX(contentX);
            recordButton.setY(contentY);
        }
        if (playButton != null) {
            playButton.setX(contentX + 90);
            playButton.setY(contentY);
        }
        if (stopButton != null) {
            stopButton.setX(contentX + 180);
            stopButton.setY(contentY);
        }
        if (loopCountBox != null) {
            loopCountBox.setX(contentX);
            loopCountBox.setY(contentY + 35);
        }
        if (infiniteLoopButton != null) {
            infiniteLoopButton.setX(contentX + 70);
            infiniteLoopButton.setY(contentY + 35);
        }
    }
    
    private void onRecordPressed() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            RecordedScript script = ScriptRecorderMod.scriptManager.stopRecording();
            sendChatMessage("Recording stopped. " +
                (script != null ? script.getTotalActions() + " actions captured." : ""));
        } else {
            if (ScriptRecorderMod.scriptManager.startRecording("recorded_script")) {
                sendChatMessage("Recording started. Press F7 to stop.");
            } else {
                sendChatMessage("Failed to start recording!");
            }
        }
        updateButtonStates();
    }
    
    private void onPlayPressed() {
        if (ScriptRecorderMod.scriptManager.isPlaying()) {
            ScriptRecorderMod.scriptManager.stopPlayback();
            sendChatMessage("Playback stopped.");
        } else {
            RecordedScript currentScript = ScriptRecorderMod.scriptManager.getCurrentScript();
            if (currentScript != null && currentScript.isValid()) {
                // Update loop count from input
                updateLoopCountFromInput();
                currentScript.setLoopCount(infiniteLoop ? 0 : loopCount);
                
                ScriptRecorderMod.scriptManager.startPlayback(currentScript);
                sendChatMessage("Playback started.");
            } else {
                sendChatMessage("No valid script to play!");
            }
        }
        updateButtonStates();
    }
    
    private void onStopPressed() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            onRecordPressed(); // Stop recording
        } else if (ScriptRecorderMod.scriptManager.isPlaying()) {
            onPlayPressed(); // Stop playback
        }
    }
    
    private void toggleInfiniteLoop() {
        infiniteLoop = !infiniteLoop;
        // Save to ModSettings
        ModSettings.getInstance().infiniteLoop = infiniteLoop;
        ModSettings.getInstance().save();

        if (infiniteLoopButton != null) {
            infiniteLoopButton.setMessage(Component.literal(infiniteLoop ? "∞ ON" : "∞ OFF"));
        }
        if (loopCountBox != null) {
            loopCountBox.setValue(infiniteLoop ? "∞" : String.valueOf(loopCount));
        }
    }
    
    private void updateLoopCountFromInput() {
        if (loopCountBox != null && !infiniteLoop) {
            try {
                String value = loopCountBox.getValue().trim();
                if (!value.isEmpty() && !value.equals("∞")) {
                    int newCount = Integer.parseInt(value);
                    if (newCount > 0 && newCount <= 999) {
                        loopCount = newCount;
                        // Save to ModSettings
                        ModSettings.getInstance().loopCount = loopCount;
                        ModSettings.getInstance().save();
                    }
                }
            } catch (NumberFormatException e) {
                // Invalid input, keep current value
                loopCountBox.setValue(String.valueOf(loopCount));
            }
        }
    }
    
    private void updateButtonStates() {
        if (recordButton != null) {
            recordButton.setMessage(getRecordButtonText());
        }
        if (playButton != null) {
            playButton.setMessage(getPlayButtonText());
        }
    }
    
    private Component getRecordButtonText() {
        return Component.literal(ScriptRecorderMod.scriptManager.isRecording() ? "Stop Rec" : "Record");
    }
    
    private Component getPlayButtonText() {
        return Component.literal(ScriptRecorderMod.scriptManager.isPlaying() ? "Stop Play" : "Play");
    }
    
    private void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null) {
            mc.player.displayClientMessage(Component.literal("[Script Recorder] " + message), false);
        }
    }
    
    /**
     * Update the panel (called every frame)
     */
    public void update() {
        // Always update component positions first
        updateComponentPositions();
        updateLoopCountFromInput();
        updateButtonStates();
    }
}
