package com.scriptrecorder.mod.client;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.settings.ModSettings;
import net.minecraft.client.settings.KeyBinding;
import net.minecraftforge.fml.client.registry.ClientRegistry;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import org.lwjgl.input.Keyboard;

/**
 * Key binding system for Toggle Sprint Mod
 * Toggle Sprint and Toggle Crouch are registered with Minecraft's keybind settings
 * Other keybinds are managed internally through the mod's GUI
 */
@SideOnly(Side.CLIENT)
public class KeyBindings {

    // Minecraft-registered keybinds (appear in settings)
    public static KeyBinding TOGGLE_SPRINT;
    public static KeyBinding TOGGLE_CROUCH;

    // Default key codes (can be changed via GUI only)
    public static final int DEFAULT_OPEN_GUI = Keyboard.KEY_BACKSLASH; // Shift + \
    public static final int DEFAULT_RECORD = Keyboard.KEY_F6;
    public static final int DEFAULT_STOP_RECORD = Keyboard.KEY_F7;
    public static final int DEFAULT_PLAY = Keyboard.KEY_F8;

    /**
     * Check if the GUI open key is pressed (Shift + \)
     */
    public static boolean isOpenGuiPressed() {
        return Keyboard.isKeyDown(ModSettings.getInstance().keyOpenGui) &&
               (Keyboard.isKeyDown(Keyboard.KEY_LSHIFT) || Keyboard.isKeyDown(Keyboard.KEY_RSHIFT));
    }

    /**
     * Check if the record key is pressed
     */
    public static boolean isRecordPressed() {
        return Keyboard.isKeyDown(ModSettings.getInstance().keyRecord);
    }

    /**
     * Check if the stop record key is pressed
     */
    public static boolean isStopRecordPressed() {
        return Keyboard.isKeyDown(ModSettings.getInstance().keyStopRecord);
    }

    /**
     * Check if the play key is pressed
     */
    public static boolean isPlayPressed() {
        return Keyboard.isKeyDown(ModSettings.getInstance().keyPlay);
    }

    /**
     * Check if the toggle keystrokes key is pressed
     */
    public static boolean isToggleKeystrokesPressed() {
        int key = ModSettings.getInstance().keyToggleKeystrokes;
        return key != 0 && Keyboard.isKeyDown(key);
    }

    /**
     * Initialize keybinds (called during mod setup)
     */
    public static void register() {
        // Initialize Minecraft keybinds
        TOGGLE_SPRINT = new KeyBinding(
            "key.waypointmod.toggle_sprint",
            Keyboard.KEY_NONE, // No default key - user must set it
            "key.categories.waypointmod"
        );

        TOGGLE_CROUCH = new KeyBinding(
            "key.waypointmod.toggle_crouch",
            Keyboard.KEY_NONE, // No default key - user must set it
            "key.categories.waypointmod"
        );

        // Register with ClientRegistry
        ClientRegistry.registerKeyBinding(TOGGLE_SPRINT);
        ClientRegistry.registerKeyBinding(TOGGLE_CROUCH);

        ScriptRecorderMod.LOGGER.info("Initialized and registered Waypoint Mod key bindings");
    }

    /**
     * Check if toggle sprint keybind was pressed
     */
    public static boolean isToggleSprintPressed() {
        return TOGGLE_SPRINT != null && TOGGLE_SPRINT.isPressed();
    }

    /**
     * Check if toggle crouch keybind was pressed
     */
    public static boolean isToggleCrouchPressed() {
        return TOGGLE_CROUCH != null && TOGGLE_CROUCH.isPressed();
    }
}
