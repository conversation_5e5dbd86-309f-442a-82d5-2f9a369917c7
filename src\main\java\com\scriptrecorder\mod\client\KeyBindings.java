package com.scriptrecorder.mod.client;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.settings.ModSettings;
import net.minecraft.client.KeyMapping;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import net.minecraftforge.client.event.RegisterKeyMappingsEvent;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import org.lwjgl.glfw.GLFW;

/**
 * Key binding system for Toggle Sprint Mod
 * Toggle Sprint and Toggle Crouch are registered with Minecraft's keybind settings
 * Other keybinds are managed internally through the mod's GUI
 */
@OnlyIn(Dist.CLIENT)
public class KeyBindings {

    // Minecraft-registered keybinds (appear in settings)
    public static KeyMapping TOGGLE_SPRINT;
    public static KeyMapping TOGGLE_CROUCH;

    // Default key codes (can be changed via GUI only)
    public static final int DEFAULT_OPEN_GUI = GLFW.GLFW_KEY_BACKSLASH; // Ctrl + \ + ]
    public static final int DEFAULT_RECORD = GLFW.GLFW_KEY_F6;
    public static final int DEFAULT_STOP_RECORD = GLFW.GLFW_KEY_F7;
    public static final int DEFAULT_PLAY = GLFW.GLFW_KEY_F8;

    /**
     * Check if the GUI open key is pressed (Ctrl + \ + ])
     */
    public static boolean isOpenGuiPressed() {
        return GLFW.glfwGetKey(getWindowHandle(), ModSettings.getInstance().keyOpenGui) == GLFW.GLFW_PRESS &&
               (GLFW.glfwGetKey(getWindowHandle(), GLFW.GLFW_KEY_LEFT_SHIFT) == GLFW.GLFW_PRESS ||
                GLFW.glfwGetKey(getWindowHandle(), GLFW.GLFW_KEY_RIGHT_SHIFT) == GLFW.GLFW_PRESS);
    }

    /**
     * Check if the record key is pressed
     */
    public static boolean isRecordPressed() {
        return GLFW.glfwGetKey(getWindowHandle(), ModSettings.getInstance().keyRecord) == GLFW.GLFW_PRESS;
    }

    /**
     * Check if the stop record key is pressed
     */
    public static boolean isStopRecordPressed() {
        return GLFW.glfwGetKey(getWindowHandle(), ModSettings.getInstance().keyStopRecord) == GLFW.GLFW_PRESS;
    }

    /**
     * Check if the play key is pressed
     */
    public static boolean isPlayPressed() {
        return GLFW.glfwGetKey(getWindowHandle(), ModSettings.getInstance().keyPlay) == GLFW.GLFW_PRESS;
    }



    /**
     * Check if the toggle keystrokes key is pressed
     */
    public static boolean isToggleKeystrokesPressed() {
        int key = ModSettings.getInstance().keyToggleKeystrokes;
        return key != 0 && GLFW.glfwGetKey(getWindowHandle(), key) == GLFW.GLFW_PRESS;
    }

    /**
     * Get the current window handle for GLFW key checking
     */
    private static long getWindowHandle() {
        return net.minecraft.client.Minecraft.getInstance().getWindow().getWindow();
    }

    /**
     * Initialize keybinds (called during mod setup)
     */
    public static void register() {
        // Initialize Minecraft keybinds
        TOGGLE_SPRINT = new KeyMapping(
            "key.waypointmod.toggle_sprint",
            GLFW.GLFW_KEY_UNKNOWN, // No default key - user must set it
            "key.categories.waypointmod"
        );

        TOGGLE_CROUCH = new KeyMapping(
            "key.waypointmod.toggle_crouch",
            GLFW.GLFW_KEY_UNKNOWN, // No default key - user must set it
            "key.categories.waypointmod"
        );

        ScriptRecorderMod.LOGGER.info("Initialized Waypoint Mod key bindings");
    }

    /**
     * Register keybinds with Minecraft (called by Forge event)
     */
    @SubscribeEvent
    public static void registerKeyMappings(RegisterKeyMappingsEvent event) {
        // Initialize keybinds if not already done
        if (TOGGLE_SPRINT == null || TOGGLE_CROUCH == null) {
            register();
        }

        ScriptRecorderMod.LOGGER.info("Registering Toggle Sprint Mod keybinds...");
        event.register(TOGGLE_SPRINT);
        event.register(TOGGLE_CROUCH);
        ScriptRecorderMod.LOGGER.info("Successfully registered Toggle Sprint Mod keybinds with Minecraft");
    }

    /**
     * Check if toggle sprint keybind was pressed
     */
    public static boolean isToggleSprintPressed() {
        return TOGGLE_SPRINT != null && TOGGLE_SPRINT.consumeClick();
    }

    /**
     * Check if toggle crouch keybind was pressed
     */
    public static boolean isToggleCrouchPressed() {
        return TOGGLE_CROUCH != null && TOGGLE_CROUCH.consumeClick();
    }
}
