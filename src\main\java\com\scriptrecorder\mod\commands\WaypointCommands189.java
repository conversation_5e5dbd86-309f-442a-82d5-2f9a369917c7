package com.scriptrecorder.mod.commands;

import com.scriptrecorder.mod.waypoints.WaypointManager;
import net.minecraft.client.Minecraft;
import net.minecraft.command.CommandBase;
import net.minecraft.command.CommandException;
import net.minecraft.command.ICommandSender;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.BlockPos;
import net.minecraft.util.ChatComponentText;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.util.List;

/**
 * Simple waypoint commands for 1.8.9
 */
@SideOnly(Side.CLIENT)
public class WaypointCommands189 extends CommandBase {

    @Override
    public String getCommandName() {
        return "waypoint";
    }

    @Override
    public String getCommandUsage(ICommandSender sender) {
        return "/waypoint <add|remove|list> [name]";
    }

    @Override
    public void processCommand(ICommandSender sender, String[] args) throws CommandException {
        if (!(sender instanceof EntityPlayer)) {
            return;
        }

        EntityPlayer player = (EntityPlayer) sender;
        WaypointManager manager = WaypointManager.getInstance();

        if (args.length == 0) {
            player.addChatMessage(new ChatComponentText("Usage: " + getCommandUsage(sender)));
            return;
        }

        String action = args[0].toLowerCase();

        switch (action) {
            case "add":
                if (args.length < 2) {
                    player.addChatMessage(new ChatComponentText("Usage: /waypoint add <name>"));
                    return;
                }
                String name = args[1];
                if (manager.addWaypointAtCurrentLocation(name)) {
                    player.addChatMessage(new ChatComponentText("Waypoint '" + name + "' added!"));
                } else {
                    player.addChatMessage(new ChatComponentText("Failed to add waypoint '" + name + "'"));
                }
                break;

            case "remove":
                if (args.length < 2) {
                    player.addChatMessage(new ChatComponentText("Usage: /waypoint remove <name>"));
                    return;
                }
                String removeName = args[1];
                if (manager.removeWaypoint(removeName)) {
                    player.addChatMessage(new ChatComponentText("Waypoint '" + removeName + "' removed!"));
                } else {
                    player.addChatMessage(new ChatComponentText("Waypoint '" + removeName + "' not found"));
                }
                break;

            case "list":
                int count = manager.getWaypointCount();
                player.addChatMessage(new ChatComponentText("You have " + count + " waypoints"));
                break;

            default:
                player.addChatMessage(new ChatComponentText("Usage: " + getCommandUsage(sender)));
                break;
        }
    }

    @Override
    public boolean canCommandSenderUseCommand(ICommandSender sender) {
        return true;
    }

    @Override
    public List<String> addTabCompletionOptions(ICommandSender sender, String[] args, BlockPos pos) {
        if (args.length == 1) {
            return getListOfStringsMatchingLastWord(args, "add", "remove", "list");
        }
        return null;
    }
}
