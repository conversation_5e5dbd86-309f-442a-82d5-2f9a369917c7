package com.scriptrecorder.mod.client.gui.panel;

import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.AbstractWidget;
import net.minecraft.network.chat.Component;
import java.awt.Color;
import java.util.ArrayList;
import java.util.List;

/**
 * Base panel class for the modern panel-based GUI system
 */
public abstract class Panel {
    
    // Panel properties
    protected String title;
    protected String panelName; // Used for saving position to settings
    protected double x, y;
    protected double width, height;
    protected double minWidth, minHeight;
    protected double maxWidth, maxHeight;
    protected boolean opened = true;
    protected boolean minimized = false;
    protected boolean dragging = false;
    protected boolean resizing = false;
    protected boolean resizable = true;
    protected boolean hasBackground = true;
    protected boolean minimizable = true;
    
    // Visual properties
    protected Color accentColor = new Color(0, 162, 232); // Blue accent
    protected Color backgroundColor = new Color(0, 0, 0, 120); // Semi-transparent black
    protected Color titleBarColor = new Color(0, 162, 232, 200); // Accent color for title bar
    protected double titleBarHeight = 20.0;
    
    // Drag and resize state
    protected DragInfo dragInfo = new DragInfo();
    protected ResizeHandle resizeHandle = ResizeHandle.NONE;
    protected double resizeStartX = 0;
    protected double resizeStartY = 0;
    protected double resizeStartWidth = 0;
    protected double resizeStartHeight = 0;

    // Resize handle size
    protected static final int RESIZE_HANDLE_SIZE = 8;
    
    // Child widgets
    protected List<AbstractWidget> widgets = new ArrayList<>();

    // Resize handle enumeration
    public enum ResizeHandle {
        NONE, BOTTOM_RIGHT, BOTTOM, RIGHT, TOP_RIGHT, TOP, LEFT, BOTTOM_LEFT, TOP_LEFT
    }
    
    public Panel(String title, double x, double y, double width, double height) {
        this.title = title;
        this.panelName = title.toLowerCase().replaceAll("\\s+", ""); // Convert title to panel name

        // Load position from settings if available
        double[] savedPosition = com.scriptrecorder.mod.client.settings.ModSettings.getPanelPosition(panelName);
        this.x = savedPosition[0];
        this.y = savedPosition[1];

        this.width = width;
        this.height = height;
        this.minWidth = width;
        this.minHeight = height;
        this.maxWidth = width * 2;
        this.maxHeight = height * 2;
    }
    
    public Panel(String title, double x, double y, double width, double height, 
                double minWidth, double minHeight, double maxWidth, double maxHeight) {
        this(title, x, y, width, height);
        this.minWidth = minWidth;
        this.minHeight = minHeight;
        this.maxWidth = maxWidth;
        this.maxHeight = maxHeight;
    }
    
    /**
     * Render the panel
     */
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        if (!opened) return;

        // Render background
        if (hasBackground) {
            renderBackground(guiGraphics);
        }

        // Render title bar
        renderTitleBar(guiGraphics, mouseX, mouseY, delta);

        // Only render content and widgets if not minimized
        if (!minimized) {
            // Render content
            renderContent(guiGraphics, mouseX, mouseY, delta);

            // Render resize handles if resizable
            if (resizable) {
                renderResizeHandles(guiGraphics, mouseX, mouseY);
            }
        }

        // Always render widgets (they handle their own visibility)
        for (AbstractWidget widget : widgets) {
            // Only render if panel is not minimized
            if (!minimized) {
                widget.render(guiGraphics, mouseX, mouseY, delta);
            }
        }
    }
    
    /**
     * Render the panel background
     */
    protected void renderBackground(GuiGraphics guiGraphics) {
        // Only render background if not minimized
        if (minimized) return;

        // Main panel background (semi-transparent)
        guiGraphics.fill((int)x, (int)(y + titleBarHeight),
                        (int)(x + width), (int)(y + height),
                        backgroundColor.getRGB());

        // Panel border
        int borderColor = new Color(255, 255, 255, 60).getRGB();

        // Top border (under title bar)
        guiGraphics.fill((int)x, (int)(y + titleBarHeight),
                        (int)(x + width), (int)(y + titleBarHeight + 1),
                        borderColor);

        // Left border
        guiGraphics.fill((int)x, (int)(y + titleBarHeight),
                        (int)(x + 1), (int)(y + height),
                        borderColor);

        // Right border
        guiGraphics.fill((int)(x + width - 1), (int)(y + titleBarHeight),
                        (int)(x + width), (int)(y + height),
                        borderColor);

        // Bottom border
        guiGraphics.fill((int)x, (int)(y + height - 1),
                        (int)(x + width), (int)(y + height),
                        borderColor);
    }
    
    /**
     * Render the title bar
     */
    protected void renderTitleBar(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Title bar background with hover effect
        boolean hoveringTitleBar = isInTitleBar(mouseX, mouseY);
        Color titleColor = hoveringTitleBar ?
            new Color(titleBarColor.getRed(), titleBarColor.getGreen(), titleBarColor.getBlue(), 255) :
            titleBarColor;

        guiGraphics.fill((int)x, (int)y,
                        (int)(x + width), (int)(y + titleBarHeight),
                        titleColor.getRGB());

        // Title text (left-aligned to make room for minimize button)
        int textColor = Color.WHITE.getRGB();
        int textX = (int)(x + 5);
        int textY = (int)(y + titleBarHeight / 2 - 4);

        guiGraphics.drawString(net.minecraft.client.Minecraft.getInstance().font,
                             title, textX, textY, textColor);

        // Minimize button (if minimizable)
        if (minimizable) {
            renderMinimizeButton(guiGraphics, mouseX, mouseY);
        }

        // Title bar border
        int borderColor = new Color(255, 255, 255, 100).getRGB();
        guiGraphics.fill((int)x, (int)(y + titleBarHeight - 1),
                        (int)(x + width), (int)(y + titleBarHeight),
                        borderColor);
    }
    
    /**
     * Render minimize button
     */
    protected void renderMinimizeButton(GuiGraphics guiGraphics, int mouseX, int mouseY) {
        int buttonSize = 12;
        int buttonX = (int)(x + width - buttonSize - 4);
        int buttonY = (int)(y + (titleBarHeight - buttonSize) / 2);

        // Check if hovering over minimize button
        boolean hoveringButton = mouseX >= buttonX && mouseX <= buttonX + buttonSize &&
                               mouseY >= buttonY && mouseY <= buttonY + buttonSize;

        // Button background
        Color buttonColor = hoveringButton ? new Color(255, 255, 255, 100) : new Color(255, 255, 255, 50);
        guiGraphics.fill(buttonX, buttonY, buttonX + buttonSize, buttonY + buttonSize,
                        buttonColor.getRGB());

        // Button symbol (- for minimize, + for restore)
        String symbol = minimized ? "+" : "−";
        int symbolX = buttonX + buttonSize / 2;
        int symbolY = buttonY + buttonSize / 2 - 4;

        guiGraphics.drawCenteredString(net.minecraft.client.Minecraft.getInstance().font,
                                     symbol, symbolX, symbolY, Color.WHITE.getRGB());
    }

    /**
     * Render resize handles
     */
    protected void renderResizeHandles(GuiGraphics guiGraphics, int mouseX, int mouseY) {
        if (minimized) return;

        ResizeHandle hoveredHandle = getResizeHandle(mouseX, mouseY);

        // Bottom-right corner handle (most important)
        int cornerX = (int)(x + width - RESIZE_HANDLE_SIZE);
        int cornerY = (int)(y + height - RESIZE_HANDLE_SIZE);

        Color handleColor = (hoveredHandle == ResizeHandle.BOTTOM_RIGHT) ?
            new Color(255, 255, 255, 150) : new Color(255, 255, 255, 80);

        guiGraphics.fill(cornerX, cornerY,
                        cornerX + RESIZE_HANDLE_SIZE, cornerY + RESIZE_HANDLE_SIZE,
                        handleColor.getRGB());

        // Draw resize indicator lines
        if (hoveredHandle == ResizeHandle.BOTTOM_RIGHT) {
            int lineColor = new Color(255, 255, 255, 200).getRGB();
            // Diagonal lines to indicate resize
            for (int i = 0; i < 3; i++) {
                int offset = i * 2 + 2;
                guiGraphics.fill(cornerX + offset, cornerY + RESIZE_HANDLE_SIZE - 2,
                               cornerX + offset + 1, cornerY + RESIZE_HANDLE_SIZE - 1, lineColor);
                guiGraphics.fill(cornerX + RESIZE_HANDLE_SIZE - 2, cornerY + offset,
                               cornerX + RESIZE_HANDLE_SIZE - 1, cornerY + offset + 1, lineColor);
            }
        }
    }

    /**
     * Abstract method for rendering panel-specific content
     */
    protected abstract void renderContent(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta);
    
    /**
     * Handle mouse click
     */
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        if (!opened) return false;

        // Check if clicking on title bar FIRST (for dragging) - but not if clicking minimize button
        if (isInTitleBar(mouseX, mouseY) && !isInMinimizeButton(mouseX, mouseY)) {
            if (button == 0) { // Left click
                dragInfo.setDragInfo(true, mouseX - x, mouseY - y);
                System.out.println("Panel " + title + " drag started");
                return true;
            }
        }

        // Check if clicking on minimize button
        if (minimizable && isInMinimizeButton(mouseX, mouseY)) {
            if (button == 0) { // Left click
                toggleMinimized();
                return true;
            }
        }

        // Check if clicking on resize handle
        if (!minimized && resizable) {
            ResizeHandle handle = getResizeHandle(mouseX, mouseY);
            if (handle != ResizeHandle.NONE && button == 0) {
                resizing = true;
                resizeHandle = handle;
                resizeStartX = mouseX;
                resizeStartY = mouseY;
                resizeStartWidth = width;
                resizeStartHeight = height;
                return true;
            }
        }

        // Check widgets (only if not minimized and not in title bar)
        if (!minimized && !isInTitleBar(mouseX, mouseY)) {
            // Clear focus from all widgets first
            for (AbstractWidget widget : widgets) {
                if (widget instanceof net.minecraft.client.gui.components.EditBox) {
                    ((net.minecraft.client.gui.components.EditBox) widget).setFocused(false);
                }
            }

            // Check if any widget was clicked and set focus appropriately
            for (AbstractWidget widget : widgets) {
                if (widget.mouseClicked(mouseX, mouseY, button)) {
                    // Set focus for EditBox widgets and save values when focus changes
                    if (widget instanceof net.minecraft.client.gui.components.EditBox) {
                        ((net.minecraft.client.gui.components.EditBox) widget).setFocused(true);
                        // Force an update to save any previously changed values
                        this.update();
                    }
                    return true;
                }
            }
        }

        // If clicking inside panel but not on any widget, clear all focus and save values
        if (isInPanel(mouseX, mouseY)) {
            for (AbstractWidget widget : widgets) {
                if (widget instanceof net.minecraft.client.gui.components.EditBox) {
                    ((net.minecraft.client.gui.components.EditBox) widget).setFocused(false);
                }
            }
            // Force an update to save any changed values
            this.update();
            return true;
        }

        return false;
    }
    
    /**
     * Handle mouse drag
     */
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        if (!opened) return false;

        // Handle panel dragging FIRST (highest priority)
        if (dragInfo.isDragging() && button == 0) {
            double newX = mouseX - dragInfo.getXOffset();
            double newY = mouseY - dragInfo.getYOffset();

            // Keep panel on screen (use actual screen bounds)
            net.minecraft.client.Minecraft mc = net.minecraft.client.Minecraft.getInstance();
            int screenWidth = mc.getWindow().getGuiScaledWidth();
            int screenHeight = mc.getWindow().getGuiScaledHeight();
            x = Math.max(0, Math.min(newX, screenWidth - width));
            y = Math.max(0, Math.min(newY, screenHeight - height));

            // Force update widget positions immediately
            updateComponentPositions();

            return true;
        }

        // Forward to widgets only if not dragging panel and not minimized
        if (!minimized && !dragInfo.isDragging()) {
            for (AbstractWidget widget : widgets) {
                if (widget.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                    return true;
                }
            }
        }

        // Handle resizing
        if (resizing && button == 0) {
            double deltaMouseX = mouseX - resizeStartX;
            double deltaMouseY = mouseY - resizeStartY;

            switch (resizeHandle) {
                case BOTTOM_RIGHT:
                    setWidth(resizeStartWidth + deltaMouseX);
                    setHeight(resizeStartHeight + deltaMouseY);
                    break;
                case BOTTOM:
                    setHeight(resizeStartHeight + deltaMouseY);
                    break;
                case RIGHT:
                    setWidth(resizeStartWidth + deltaMouseX);
                    break;
                // Add more resize directions as needed
            }

            return true;
        }

        return false;
    }
    
    /**
     * Handle mouse release
     */
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        if (dragInfo.isDragging() && button == 0) {
            dragInfo.reset();
            // Save position when dragging ends
            savePosition();
            return true;
        }
        if (resizing && button == 0) {
            resizing = false;
            resizeHandle = ResizeHandle.NONE;
            return true;
        }
        return false;
    }

    /**
     * Save the current panel position to settings
     */
    private void savePosition() {
        if (panelName != null) {
            com.scriptrecorder.mod.client.settings.ModSettings.setPanelPosition(panelName, x, y);
        }
    }
    
    /**
     * Check if mouse is in title bar
     */
    protected boolean isInTitleBar(double mouseX, double mouseY) {
        return mouseX >= x && mouseX <= x + width &&
               mouseY >= y && mouseY <= y + titleBarHeight;
    }
    
    /**
     * Check if mouse is in panel
     */
    protected boolean isInPanel(double mouseX, double mouseY) {
        double panelHeight = minimized ? titleBarHeight : height;
        return mouseX >= x && mouseX <= x + width &&
               mouseY >= y && mouseY <= y + panelHeight;
    }

    /**
     * Check if mouse is in minimize button
     */
    protected boolean isInMinimizeButton(double mouseX, double mouseY) {
        if (!minimizable) return false;

        int buttonSize = 12;
        int buttonX = (int)(x + width - buttonSize - 4);
        int buttonY = (int)(y + (titleBarHeight - buttonSize) / 2);

        return mouseX >= buttonX && mouseX <= buttonX + buttonSize &&
               mouseY >= buttonY && mouseY <= buttonY + buttonSize;
    }

    /**
     * Get resize handle at mouse position
     */
    protected ResizeHandle getResizeHandle(double mouseX, double mouseY) {
        if (!resizable || minimized) return ResizeHandle.NONE;

        // Check bottom-right corner
        if (mouseX >= x + width - RESIZE_HANDLE_SIZE && mouseX <= x + width &&
            mouseY >= y + height - RESIZE_HANDLE_SIZE && mouseY <= y + height) {
            return ResizeHandle.BOTTOM_RIGHT;
        }

        // Check bottom edge
        if (mouseX >= x + RESIZE_HANDLE_SIZE && mouseX <= x + width - RESIZE_HANDLE_SIZE &&
            mouseY >= y + height - RESIZE_HANDLE_SIZE && mouseY <= y + height) {
            return ResizeHandle.BOTTOM;
        }

        // Check right edge
        if (mouseX >= x + width - RESIZE_HANDLE_SIZE && mouseX <= x + width &&
            mouseY >= y + titleBarHeight + RESIZE_HANDLE_SIZE && mouseY <= y + height - RESIZE_HANDLE_SIZE) {
            return ResizeHandle.RIGHT;
        }

        return ResizeHandle.NONE;
    }

    /**
     * Toggle minimized state
     */
    public void toggleMinimized() {
        minimized = !minimized;
    }

    /**
     * Handle key press events (forward to widgets)
     */
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (!opened || minimized) return false;

        // First check if any EditBox has focus
        for (AbstractWidget widget : widgets) {
            if (widget instanceof net.minecraft.client.gui.components.EditBox) {
                net.minecraft.client.gui.components.EditBox editBox = (net.minecraft.client.gui.components.EditBox) widget;
                if (editBox.isFocused() && editBox.keyPressed(keyCode, scanCode, modifiers)) {
                    return true;
                }
            }
        }

        // Then check other widgets
        for (AbstractWidget widget : widgets) {
            if (!(widget instanceof net.minecraft.client.gui.components.EditBox) &&
                widget.keyPressed(keyCode, scanCode, modifiers)) {
                return true;
            }
        }
        return false;
    }

    /**
     * Handle character typed events (forward to widgets)
     */
    public boolean charTyped(char codePoint, int modifiers) {
        if (!opened || minimized) return false;

        // First check if any EditBox has focus
        for (AbstractWidget widget : widgets) {
            if (widget instanceof net.minecraft.client.gui.components.EditBox) {
                net.minecraft.client.gui.components.EditBox editBox = (net.minecraft.client.gui.components.EditBox) widget;
                if (editBox.isFocused() && editBox.charTyped(codePoint, modifiers)) {
                    return true;
                }
            }
        }

        // Then check other widgets
        for (AbstractWidget widget : widgets) {
            if (!(widget instanceof net.minecraft.client.gui.components.EditBox) &&
                widget.charTyped(codePoint, modifiers)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Add a widget to this panel
     */
    public void addWidget(AbstractWidget widget) {
        widgets.add(widget);
    }
    
    /**
     * Remove a widget from this panel
     */
    public void removeWidget(AbstractWidget widget) {
        widgets.remove(widget);
    }
    
    /**
     * Clear all widgets
     */
    public void clearWidgets() {
        widgets.clear();
    }

    /**
     * Get all widgets in this panel
     */
    public List<AbstractWidget> getWidgets() {
        return widgets;
    }

    /**
     * Update component positions when panel is moved - override in subclasses
     */
    protected void updateComponentPositions() {
        // Default implementation does nothing
        // Subclasses should override this to update their widget positions
    }

    /**
     * Update the panel (called every frame) - default implementation
     */
    public void update() {
        // Always update component positions first
        updateComponentPositions();
    }
    
    // Getters and setters
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public double getX() { return x; }
    public void setX(double x) { this.x = x; }
    
    public double getY() { return y; }
    public void setY(double y) { this.y = y; }
    
    public double getWidth() { return width; }
    public void setWidth(double width) { 
        this.width = Math.max(minWidth, Math.min(maxWidth, width)); 
    }
    
    public double getHeight() { return height; }
    public void setHeight(double height) { 
        this.height = Math.max(minHeight, Math.min(maxHeight, height)); 
    }
    
    public boolean isOpened() { return opened; }
    public void setOpened(boolean opened) { this.opened = opened; }

    public boolean isMinimized() { return minimized; }
    public void setMinimized(boolean minimized) { this.minimized = minimized; }

    public boolean isResizable() { return resizable; }
    public void setResizable(boolean resizable) { this.resizable = resizable; }

    public boolean isMinimizable() { return minimizable; }
    public void setMinimizable(boolean minimizable) { this.minimizable = minimizable; }

    public void toggle() { this.opened = !this.opened; }
    
    public Color getAccentColor() { return accentColor; }
    public void setAccentColor(Color accentColor) { 
        this.accentColor = accentColor;
        this.titleBarColor = new Color(accentColor.getRed(), accentColor.getGreen(), 
                                     accentColor.getBlue(), 200);
    }
}
