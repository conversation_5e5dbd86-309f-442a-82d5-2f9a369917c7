package com.scriptrecorder.mod.client.serverpinger;

import java.util.List;
import java.util.ArrayList;

/**
 * Contains comprehensive server information from ping
 */
public class ServerInfo {
    
    private final String address;
    private final int port;
    private final long ping;
    private final String description;
    private final List<String> playerList;
    private final int onlinePlayers;
    private final int maxPlayers;
    private final boolean online;
    
    // Additional tracking
    private final long timestamp;
    private List<String> detectedPlayers; // Players detected through other means
    private List<String> vanishedPlayers; // Players that might be vanished
    
    public ServerInfo(String address, int port, long ping, String description, 
                     List<String> playerList, int onlinePlayers, int maxPlayers, boolean online) {
        this.address = address;
        this.port = port;
        this.ping = ping;
        this.description = description;
        this.playerList = new ArrayList<>(playerList);
        this.onlinePlayers = onlinePlayers;
        this.maxPlayers = maxPlayers;
        this.online = online;
        this.timestamp = System.currentTimeMillis();
        this.detectedPlayers = new ArrayList<>();
        this.vanishedPlayers = new ArrayList<>();
    }
    
    // Getters
    public String getAddress() { return address; }
    public int getPort() { return port; }
    public long getPing() { return ping; }
    public String getDescription() { return description; }
    public List<String> getPlayerList() { return new ArrayList<>(playerList); }
    public int getOnlinePlayers() { return onlinePlayers; }
    public int getMaxPlayers() { return maxPlayers; }
    public boolean isOnline() { return online; }
    public long getTimestamp() { return timestamp; }
    
    /**
     * Get formatted server address
     */
    public String getFormattedAddress() {
        if (port == 25565) {
            return address;
        } else {
            return address + ":" + port;
        }
    }
    
    /**
     * Get formatted ping string
     */
    public String getFormattedPing() {
        if (!online) return "Offline";
        if (ping < 0) return "Error";
        if (ping == 0) return "?";
        return ping + "ms";
    }
    
    /**
     * Get formatted player count
     */
    public String getFormattedPlayerCount() {
        if (!online) return "0/0";
        return onlinePlayers + "/" + maxPlayers;
    }
    
    /**
     * Get clean description (remove formatting codes)
     */
    public String getCleanDescription() {
        if (description == null) return "No description";
        // Remove Minecraft color codes
        return description.replaceAll("§[0-9a-fk-or]", "").trim();
    }
    
    /**
     * Get ping color based on latency
     */
    public int getPingColor() {
        if (!online || ping < 0) return 0xFFFF0000; // Red for offline/error
        if (ping == 0) return 0xFF888888; // Gray for unknown
        if (ping < 50) return 0xFF00FF00; // Green for excellent
        if (ping < 100) return 0xFFFFFF00; // Yellow for good
        if (ping < 200) return 0xFFFF8000; // Orange for fair
        return 0xFFFF0000; // Red for poor
    }
    
    /**
     * Get player count color based on server load
     */
    public int getPlayerCountColor() {
        if (!online || maxPlayers == 0) return 0xFF888888;
        
        double ratio = (double) onlinePlayers / maxPlayers;
        if (ratio < 0.5) return 0xFF00FF00; // Green for low load
        if (ratio < 0.8) return 0xFFFFFF00; // Yellow for medium load
        if (ratio < 0.95) return 0xFFFF8000; // Orange for high load
        return 0xFFFF0000; // Red for full/overloaded
    }
    
    /**
     * Check if server is likely full
     */
    public boolean isFull() {
        return online && onlinePlayers >= maxPlayers;
    }
    
    /**
     * Check if server has player list available
     */
    public boolean hasPlayerList() {
        return !playerList.isEmpty();
    }
    
    /**
     * Add detected player (from other sources like ESP)
     */
    public void addDetectedPlayer(String playerName) {
        if (!detectedPlayers.contains(playerName)) {
            detectedPlayers.add(playerName);
            
            // Check if this player is not in the official list but detected
            if (!playerList.contains(playerName)) {
                if (!vanishedPlayers.contains(playerName)) {
                    vanishedPlayers.add(playerName);
                }
            }
        }
    }
    
    /**
     * Get all detected players (including potentially vanished ones)
     */
    public List<String> getDetectedPlayers() {
        return new ArrayList<>(detectedPlayers);
    }
    
    /**
     * Get players that might be vanished (detected but not in server list)
     */
    public List<String> getVanishedPlayers() {
        return new ArrayList<>(vanishedPlayers);
    }
    
    /**
     * Get combined player list (official + detected)
     */
    public List<String> getAllPlayers() {
        List<String> allPlayers = new ArrayList<>(playerList);
        for (String player : detectedPlayers) {
            if (!allPlayers.contains(player)) {
                allPlayers.add(player);
            }
        }
        return allPlayers;
    }
    
    /**
     * Get total player count including detected players
     */
    public int getTotalDetectedPlayers() {
        return getAllPlayers().size();
    }
    
    /**
     * Check if there are potentially vanished players
     */
    public boolean hasVanishedPlayers() {
        return !vanishedPlayers.isEmpty();
    }
    
    /**
     * Clear detected players (useful when changing servers)
     */
    public void clearDetectedPlayers() {
        detectedPlayers.clear();
        vanishedPlayers.clear();
    }
    
    /**
     * Get age of this server info in milliseconds
     */
    public long getAge() {
        return System.currentTimeMillis() - timestamp;
    }
    
    /**
     * Check if this server info is stale (older than 30 seconds)
     */
    public boolean isStale() {
        return getAge() > 30000;
    }
    
    @Override
    public String toString() {
        return String.format("ServerInfo{%s, ping=%s, players=%s, online=%s}", 
                           getFormattedAddress(), getFormattedPing(), 
                           getFormattedPlayerCount(), online);
    }
    
    /**
     * Create a copy with updated detected players
     */
    public ServerInfo withDetectedPlayers(List<String> detected) {
        ServerInfo copy = new ServerInfo(address, port, ping, description, 
                                       playerList, onlinePlayers, maxPlayers, online);
        copy.detectedPlayers = new ArrayList<>(detected);
        
        // Update vanished players
        for (String player : detected) {
            if (!playerList.contains(player)) {
                copy.vanishedPlayers.add(player);
            }
        }
        
        return copy;
    }
}
