package com.scriptrecorder.mod.client.autoclicker;

import net.minecraft.client.Minecraft;
import net.minecraft.client.KeyMapping;
import net.minecraft.world.InteractionHand;
import java.util.Random;

/**
 * AutoClicker implementation with multiple click methods and smart detection avoidance
 */
public class AutoClicker {
    
    private static AutoClicker instance;
    
    // Settings
    private boolean enabled = false;
    private boolean attackEnabled = true;
    private boolean useEnabled = false;
    private ClickMethod attackMethod = ClickMethod.DYNAMIC;
    private ClickMethod useMethod = ClickMethod.CONSTANT;
    private double attackCPSMin = 8.0;
    private double attackCPSMax = 12.0;
    private double useCPSMin = 12.0;
    private double useCPSMax = 16.0;
    
    // State tracking
    private long lastAttackClick = 0;
    private long lastUseClick = 0;
    private double currentAttackCPS = 10.0;
    private double currentUseCPS = 14.0;
    private long attackCPSChangeTime = 0;
    private long useCPSChangeTime = 0;
    private Random random = new Random();
    
    // Dynamic method state
    private double attackRemainder = 0;
    private double useRemainder = 0;
    private long attackTime = System.currentTimeMillis();
    private long useTime = System.currentTimeMillis();
    
    // Click method enumeration
    public enum ClickMethod {
        CONSTANT("Constant"),
        DYNAMIC("Dynamic"),
        COOLDOWN("Cooldown");
        
        private final String name;
        
        ClickMethod(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
    }
    
    private AutoClicker() {}
    
    public static AutoClicker getInstance() {
        if (instance == null) {
            instance = new AutoClicker();
        }
        return instance;
    }
    
    /**
     * Update the autoclicker (called every tick)
     */
    public void update() {
        if (!enabled) return;
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) return;
        
        // Update CPS values periodically for variation
        updateCPSValues();
        
        // Handle attack clicking
        if (attackEnabled && mc.options.keyAttack.isDown()) {
            if (shouldClick(true)) {
                performAttackClick();
            }
        }
        
        // Handle use clicking
        if (useEnabled && mc.options.keyUse.isDown()) {
            if (shouldClick(false)) {
                performUseClick();
            }
        }
    }
    
    /**
     * Update CPS values for variation (every 1-3 seconds)
     */
    private void updateCPSValues() {
        long currentTime = System.currentTimeMillis();
        
        // Update attack CPS
        if (currentTime - attackCPSChangeTime > (1000 + random.nextInt(2000))) {
            currentAttackCPS = attackCPSMin + (attackCPSMax - attackCPSMin) * random.nextDouble();
            attackCPSChangeTime = currentTime;
        }
        
        // Update use CPS
        if (currentTime - useCPSChangeTime > (1000 + random.nextInt(2000))) {
            currentUseCPS = useCPSMin + (useCPSMax - useCPSMin) * random.nextDouble();
            useCPSChangeTime = currentTime;
        }
    }
    
    /**
     * Determine if we should click based on the selected method
     */
    private boolean shouldClick(boolean isAttack) {
        long currentTime = System.currentTimeMillis();
        long lastClick = isAttack ? lastAttackClick : lastUseClick;
        double targetCPS = isAttack ? currentAttackCPS : currentUseCPS;
        ClickMethod method = isAttack ? attackMethod : useMethod;
        
        switch (method) {
            case CONSTANT:
                return shouldClickConstant(currentTime, lastClick, targetCPS);
            case DYNAMIC:
                return shouldClickDynamic(currentTime, targetCPS, isAttack);
            case COOLDOWN:
                return shouldClickCooldown(currentTime, lastClick, targetCPS);
            default:
                return false;
        }
    }
    
    /**
     * Constant click method - steady clicking
     */
    private boolean shouldClickConstant(long currentTime, long lastClick, double targetCPS) {
        double interval = 1000.0 / targetCPS;
        return (currentTime - lastClick) >= interval;
    }
    
    /**
     * Dynamic click method - humanized with variations and chokes
     */
    private boolean shouldClickDynamic(long currentTime, double targetCPS, boolean isAttack) {
        // Simulate human inconsistency with "chokes"
        if (random.nextDouble() > Math.sqrt((isAttack ? attackRemainder : useRemainder) + 4.0) / 3.0) {
            if (isAttack) {
                attackRemainder++;
            } else {
                useRemainder++;
            }
            return false;
        }
        
        // Calculate clicks with remainder system
        double adjustedCPS = targetCPS + (isAttack ? attackRemainder : useRemainder);
        long time = isAttack ? attackTime : useTime;
        int clicks = (int) Math.round((currentTime - time) / (1000.0 / adjustedCPS));
        
        if (clicks > 0) {
            if (isAttack) {
                attackTime += (long) (clicks * (1000.0 / adjustedCPS));
                attackRemainder = 0;
            } else {
                useTime += (long) (clicks * (1000.0 / adjustedCPS));
                useRemainder = 0;
            }
            return true;
        }
        
        return false;
    }
    
    /**
     * Cooldown click method - respects Minecraft's attack cooldown
     */
    private boolean shouldClickCooldown(long currentTime, long lastClick, double targetCPS) {
        Minecraft mc = Minecraft.getInstance();
        
        // Check attack cooldown for attack clicks
        if (mc.player != null && mc.player.getAttackStrengthScale(0.0f) < 1.0f) {
            return false;
        }
        
        double interval = 1000.0 / targetCPS;
        return (currentTime - lastClick) >= interval;
    }
    
    /**
     * Perform attack click with smart detection avoidance
     */
    private void performAttackClick() {
        Minecraft mc = Minecraft.getInstance();
        
        // Don't click while breaking blocks (natural behavior)
        if (mc.gameMode != null && mc.gameMode.isDestroying()) {
            return;
        }
        
        // Don't click if player is using an item
        if (mc.player != null && mc.player.isUsingItem()) {
            return;
        }
        
        // Simulate click
        KeyMapping.click(mc.options.keyAttack.getKey());
        lastAttackClick = System.currentTimeMillis();

        // Record click for CPS tracking
        com.scriptrecorder.mod.client.overlay.CPSOverlay.recordLeftClick();
    }
    
    /**
     * Perform use click with smart detection avoidance
     */
    private void performUseClick() {
        Minecraft mc = Minecraft.getInstance();
        
        // Don't click if player is already using an item
        if (mc.player != null && mc.player.isUsingItem()) {
            return;
        }
        
        // Don't click if player has an active hand
        if (mc.player != null && mc.player.getUsedItemHand() != null) {
            return;
        }
        
        // Simulate click
        KeyMapping.click(mc.options.keyUse.getKey());
        lastUseClick = System.currentTimeMillis();

        // Record click for CPS tracking
        com.scriptrecorder.mod.client.overlay.CPSOverlay.recordRightClick();
    }
    
    // Getters and setters
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public boolean isAttackEnabled() { return attackEnabled; }
    public void setAttackEnabled(boolean attackEnabled) { this.attackEnabled = attackEnabled; }
    
    public boolean isUseEnabled() { return useEnabled; }
    public void setUseEnabled(boolean useEnabled) { this.useEnabled = useEnabled; }
    
    public ClickMethod getAttackMethod() { return attackMethod; }
    public void setAttackMethod(ClickMethod attackMethod) { this.attackMethod = attackMethod; }
    
    public ClickMethod getUseMethod() { return useMethod; }
    public void setUseMethod(ClickMethod useMethod) { this.useMethod = useMethod; }
    
    public double getAttackCPSMin() { return attackCPSMin; }
    public void setAttackCPSMin(double attackCPSMin) { this.attackCPSMin = Math.max(1.0, Math.min(20.0, attackCPSMin)); }
    
    public double getAttackCPSMax() { return attackCPSMax; }
    public void setAttackCPSMax(double attackCPSMax) { this.attackCPSMax = Math.max(1.0, Math.min(20.0, attackCPSMax)); }
    
    public double getUseCPSMin() { return useCPSMin; }
    public void setUseCPSMin(double useCPSMin) { this.useCPSMin = Math.max(1.0, Math.min(20.0, useCPSMin)); }
    
    public double getUseCPSMax() { return useCPSMax; }
    public void setUseCPSMax(double useCPSMax) { this.useCPSMax = Math.max(1.0, Math.min(20.0, useCPSMax)); }
    
    public double getCurrentAttackCPS() { return currentAttackCPS; }
    public double getCurrentUseCPS() { return currentUseCPS; }
    
    /**
     * Get formatted CPS range string
     */
    public String getAttackCPSRange() {
        return String.format("%.1f-%.1f", attackCPSMin, attackCPSMax);
    }
    
    public String getUseCPSRange() {
        return String.format("%.1f-%.1f", useCPSMin, useCPSMax);
    }
    
    /**
     * Reset state when enabling/disabling
     */
    public void reset() {
        lastAttackClick = 0;
        lastUseClick = 0;
        attackRemainder = 0;
        useRemainder = 0;
        attackTime = System.currentTimeMillis();
        useTime = System.currentTimeMillis();
        attackCPSChangeTime = 0;
        useCPSChangeTime = 0;
    }
}
