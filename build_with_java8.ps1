# PowerShell script to build with Java 8
Write-Host "Setting up Java 8 environment for Minecraft 1.8.9 mod build..."

# Set Java 8 path
$java8Path = Join-Path $PSScriptRoot "java8\jdk8u392-b08"
$env:JAVA_HOME = $java8Path
$env:PATH = "$java8Path\bin;$env:PATH"

Write-Host "Java Home: $env:JAVA_HOME"
Write-Host "Java Version:"
& "$java8Path\bin\java.exe" -version

Write-Host "`nStarting Gradle build..."
Write-Host "Setting up decompilation workspace..."
& ".\gradlew.bat" setupDecompWorkspace --no-daemon

if ($LASTEXITCODE -eq 0) {
    Write-Host "`nBuilding mod..."
    & ".\gradlew.bat" build --no-daemon
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "`n✅ Build successful!"
        Write-Host "JAR file location: build\libs\script-recorder-mod-1.8.9-3.6.1.jar"

        if (Test-Path "build\libs\script-recorder-mod-1.8.9-3.6.1.jar") {
            Write-Host "✅ JAR file created successfully!"
            $jarSize = (Get-Item "build\libs\script-recorder-mod-1.8.9-3.6.1.jar").Length
            Write-Host "JAR size: $([math]::Round($jarSize/1KB, 2)) KB"
        } else {
            Write-Host "❌ JAR file not found in expected location"
        }
    } else {
        Write-Host "❌ Build failed"
    }
} else {
    Write-Host "❌ Setup failed"
}

Write-Host "`nPress any key to continue..."
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
