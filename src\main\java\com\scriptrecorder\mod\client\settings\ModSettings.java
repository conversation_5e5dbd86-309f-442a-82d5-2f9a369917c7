package com.scriptrecorder.mod.client.settings;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.scriptrecorder.mod.ScriptRecorderMod;
import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import org.lwjgl.input.Keyboard;

import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;

/**
 * Manages persistent settings for the Script Recorder mod
 */
@SideOnly(Side.CLIENT)
public class ModSettings {
    
    private static final String SETTINGS_FILE = "waypoint_settings.json";
    private static ModSettings instance;
    private static File settingsFile;
    
    // Settings with default values
    public boolean sprintToggled = false;
    public boolean crouchToggled = false;
    public boolean keystrokeDisplayEnabled = false;

    // Display settings
    public int keystrokeDisplayX = 10;
    public int keystrokeDisplayY = 10;
    public boolean cpsOverlayEnabled = false;
    public int cpsOverlayX = 10;
    public int cpsOverlayY = 50;

    // Script settings
    public int loopCount = 1;
    public boolean infiniteLoop = false;

    // Tracers/ESP settings
    public boolean tracersEnabled = false;
    public boolean espEnabled = false;
    public double tracerDistance = 50.0;
    public double espDistance = 30.0;
    public String tracerFilter = "both"; // "players", "mobs", "both"
    public String espFilter = "both"; // "players", "mobs", "both"

    // Reach settings
    public boolean reachEnabled = false;
    public boolean modifyBlockReach = false;
    public double attackReach = 3.0;
    public double blockReach = 4.5;
    public String reachServerType = "VANILLA"; // Default server type

    // Script manager settings
    public String lastScriptName = "farmscript";

    // Panel positions (x, y coordinates for each panel)
    public double scriptRecorderPanelX = 10.0;
    public double scriptRecorderPanelY = 10.0;
    public double scriptManagerPanelX = 300.0;
    public double scriptManagerPanelY = 10.0;
    public double espTracersPanelX = 10.0;
    public double espTracersPanelY = 160.0;
    public double reachPanelX = 350.0;
    public double reachPanelY = 10.0;
    public double chestStealerPanelX = 10.0;
    public double chestStealerPanelY = 350.0;
    public double autoClickerPanelX = 350.0;
    public double autoClickerPanelY = 200.0;
    public double autoFisherPanelX = 20.0;
    public double autoFisherPanelY = 300.0;
    public double serverPingerPanelX = 10.0;
    public double serverPingerPanelY = 500.0;
    public double settingsPanelX = 500.0;
    public double settingsPanelY = 10.0;

    // Entity Detection settings
    public boolean entityDetectionEnabled = false;
    public double entityDetectionRange = 100.0;

    // AutoFisher settings
    public boolean autoFisherEnabled = false;
    public String autoFisherMode = "DATA_TRACKER";
    public double autoFisherBubbleDistance = 1.5;
    public long autoFisherTimeToWait = 200;

    // ESP Optimization settings
    public boolean espOptimizationEnabled = true;
    public boolean espFrustumCulling = true;
    public boolean espDistanceLimiting = true;
    public double espMaxRenderDistance = 100.0;
    public boolean espShowNPCs = true; // Option to show/hide NPCs
    public boolean espCachedProjections = true;

    // Keybind settings (store key codes for 1.8.9)
    public int keyOpenGui = Keyboard.KEY_BACKSLASH; // GUI opens with Shift+\
    public int keyRecord = Keyboard.KEY_F6;
    public int keyStopRecord = Keyboard.KEY_F7;
    public int keyPlay = Keyboard.KEY_F8;
    public int keyToggleSprint = 0; // No default key
    public int keyToggleCrouch = 0; // No default key
    public int keyToggleKeystrokes = 0; // No default key
    
    private transient Gson gson;
    
    private ModSettings() {
        this.gson = new GsonBuilder()
            .setPrettyPrinting()
            .create();
    }
    
    public static ModSettings getInstance() {
        if (instance == null) {
            instance = new ModSettings();
            instance.load();
        }
        return instance;
    }
    
    public static void initialize() {
        // Create settings file path
        File minecraftDir = Minecraft.getMinecraft().mcDataDir;
        settingsFile = new File(minecraftDir, SETTINGS_FILE);

        // Load settings
        getInstance();

        ScriptRecorderMod.LOGGER.info("Settings initialized from: " + settingsFile.getAbsolutePath());
    }
    
    public void load() {
        if (settingsFile == null || !settingsFile.exists()) {
            ScriptRecorderMod.LOGGER.info("Settings file not found, using defaults");
            save(); // Create default settings file
            return;
        }

        try (FileReader reader = new FileReader(settingsFile)) {
            ModSettings loaded = gson.fromJson(reader, ModSettings.class);
            if (loaded != null) {
                copyFrom(loaded);
                validateAndFixKeybinds(); // Fix any problematic keybinds
                ScriptRecorderMod.LOGGER.info("Settings loaded successfully");
            }
        } catch (IOException e) {
            ScriptRecorderMod.LOGGER.error("Failed to load settings", e);
        }
    }

    /**
     * Validate and fix problematic keybinds that might cause conflicts
     */
    private void validateAndFixKeybinds() {
        boolean needsSave = false;

        // Reset C key (67) if it's assigned to crouch toggle
        if (keyToggleCrouch == 67) {
            ScriptRecorderMod.LOGGER.warn("Resetting problematic crouch toggle key (C key)");
            keyToggleCrouch = 0;
            needsSave = true;
        }

        // Reset any other problematic keys
        if (keyToggleSprint == 67) {
            ScriptRecorderMod.LOGGER.warn("Resetting problematic sprint toggle key (C key)");
            keyToggleSprint = 0;
            needsSave = true;
        }

        if (keyToggleKeystrokes == 67) {
            ScriptRecorderMod.LOGGER.warn("Resetting problematic keystroke toggle key (C key)");
            keyToggleKeystrokes = 0;
            needsSave = true;
        }

        if (needsSave) {
            save();
        }
    }
    
    public void save() {
        if (settingsFile == null) {
            ScriptRecorderMod.LOGGER.error("Settings file path not initialized");
            return;
        }
        
        try (FileWriter writer = new FileWriter(settingsFile)) {
            gson.toJson(this, writer);
            ScriptRecorderMod.LOGGER.debug("Settings saved successfully");
        } catch (IOException e) {
            ScriptRecorderMod.LOGGER.error("Failed to save settings", e);
        }
    }
    
    private void copyFrom(ModSettings other) {
        this.sprintToggled = other.sprintToggled;
        this.crouchToggled = other.crouchToggled;
        this.keystrokeDisplayEnabled = other.keystrokeDisplayEnabled;
        this.keystrokeDisplayX = other.keystrokeDisplayX;
        this.keystrokeDisplayY = other.keystrokeDisplayY;
        this.cpsOverlayEnabled = other.cpsOverlayEnabled;
        this.cpsOverlayX = other.cpsOverlayX;
        this.cpsOverlayY = other.cpsOverlayY;

        // Script settings
        this.loopCount = other.loopCount;
        this.infiniteLoop = other.infiniteLoop;

        // Tracers/ESP settings
        this.tracersEnabled = other.tracersEnabled;
        this.espEnabled = other.espEnabled;
        this.tracerDistance = other.tracerDistance;
        this.espDistance = other.espDistance;
        this.tracerFilter = other.tracerFilter != null ? other.tracerFilter : "both";
        this.espFilter = other.espFilter != null ? other.espFilter : "both";

        // Reach settings
        this.reachEnabled = other.reachEnabled;
        this.modifyBlockReach = other.modifyBlockReach;
        this.attackReach = other.attackReach;
        this.blockReach = other.blockReach;
        this.reachServerType = other.reachServerType != null ? other.reachServerType : "VANILLA";

        // Script manager settings
        this.lastScriptName = other.lastScriptName != null ? other.lastScriptName : "farmscript";

        // Panel positions
        this.scriptRecorderPanelX = other.scriptRecorderPanelX;
        this.scriptRecorderPanelY = other.scriptRecorderPanelY;
        this.scriptManagerPanelX = other.scriptManagerPanelX;
        this.scriptManagerPanelY = other.scriptManagerPanelY;
        this.espTracersPanelX = other.espTracersPanelX;
        this.espTracersPanelY = other.espTracersPanelY;
        this.reachPanelX = other.reachPanelX;
        this.reachPanelY = other.reachPanelY;
        this.chestStealerPanelX = other.chestStealerPanelX;
        this.chestStealerPanelY = other.chestStealerPanelY;
        this.autoClickerPanelX = other.autoClickerPanelX;
        this.autoClickerPanelY = other.autoClickerPanelY;
        this.autoFisherPanelX = other.autoFisherPanelX;
        this.autoFisherPanelY = other.autoFisherPanelY;
        this.serverPingerPanelX = other.serverPingerPanelX;
        this.serverPingerPanelY = other.serverPingerPanelY;
        this.settingsPanelX = other.settingsPanelX;
        this.settingsPanelY = other.settingsPanelY;

        // AutoFisher settings
        this.autoFisherEnabled = other.autoFisherEnabled;
        this.autoFisherMode = other.autoFisherMode != null ? other.autoFisherMode : "DATA_TRACKER";
        this.autoFisherBubbleDistance = other.autoFisherBubbleDistance;
        this.autoFisherTimeToWait = other.autoFisherTimeToWait;

        // Keybind settings
        this.keyOpenGui = other.keyOpenGui;
        this.keyRecord = other.keyRecord;
        this.keyStopRecord = other.keyStopRecord;
        this.keyPlay = other.keyPlay;
        this.keyToggleSprint = other.keyToggleSprint;
        this.keyToggleCrouch = other.keyToggleCrouch;
        this.keyToggleKeystrokes = other.keyToggleKeystrokes;
    }
    
    // Convenience methods for common operations
    public void toggleSprint() {
        sprintToggled = !sprintToggled;
        save();
    }

    public void toggleCrouch() {
        crouchToggled = !crouchToggled;
        save();
    }

    public void toggleKeystrokeDisplay() {
        keystrokeDisplayEnabled = !keystrokeDisplayEnabled;
        save();
    }

    public void toggleTracers() {
        tracersEnabled = !tracersEnabled;
        save();
    }

    public void toggleESP() {
        espEnabled = !espEnabled;
        save();
    }

    // === STATIC GETTER/SETTER METHODS FOR GUI ===

    public static boolean isKeystrokeDisplayEnabled() {
        return getInstance().keystrokeDisplayEnabled;
    }

    public static void setKeystrokeDisplayEnabled(boolean enabled) {
        getInstance().keystrokeDisplayEnabled = enabled;
        getInstance().save();
    }

    public static int getLoopCount() {
        return getInstance().loopCount;
    }

    public static void setLoopCount(int count) {
        getInstance().loopCount = Math.max(1, Math.min(999, count));
        getInstance().save();
    }

    public static boolean isInfiniteLoop() {
        return getInstance().infiniteLoop;
    }

    public static void setInfiniteLoop(boolean infinite) {
        getInstance().infiniteLoop = infinite;
        getInstance().save();
    }

    // Tracers/ESP static methods
    public static boolean isTracersEnabled() {
        return getInstance().tracersEnabled;
    }

    public static void setTracersEnabled(boolean enabled) {
        getInstance().tracersEnabled = enabled;
        getInstance().save();
    }

    public static boolean isESPEnabled() {
        return getInstance().espEnabled;
    }

    public static void setESPEnabled(boolean enabled) {
        getInstance().espEnabled = enabled;
        getInstance().save();
    }

    public static double getTracerDistance() {
        return getInstance().tracerDistance;
    }

    public static void setTracerDistance(double distance) {
        getInstance().tracerDistance = Math.max(1.0, Math.min(200.0, distance));
        getInstance().save();
    }

    public static double getESPDistance() {
        return getInstance().espDistance;
    }

    public static void setESPDistance(double distance) {
        getInstance().espDistance = Math.max(1.0, Math.min(200.0, distance));
        getInstance().save();
    }

    public static String getTracerFilter() {
        return getInstance().tracerFilter;
    }

    public static void setTracerFilter(String filter) {
        getInstance().tracerFilter = filter;
        getInstance().save();
    }

    public static String getESPFilter() {
        return getInstance().espFilter;
    }

    public static void setESPFilter(String filter) {
        getInstance().espFilter = filter;
        getInstance().save();
    }

    // Reach settings
    public static boolean isReachEnabled() {
        return getInstance().reachEnabled;
    }

    public static void setReachEnabled(boolean enabled) {
        getInstance().reachEnabled = enabled;
        getInstance().save();
    }

    public static boolean isModifyBlockReach() {
        return getInstance().modifyBlockReach;
    }

    public static void setModifyBlockReach(boolean enabled) {
        getInstance().modifyBlockReach = enabled;
        getInstance().save();
    }

    public static double getAttackReach() {
        return getInstance().attackReach;
    }

    public static void setAttackReach(double reach) {
        getInstance().attackReach = reach;
        getInstance().save();
    }

    public static double getBlockReach() {
        return getInstance().blockReach;
    }

    public static void setBlockReach(double reach) {
        getInstance().blockReach = reach;
        getInstance().save();
    }

    public static String getReachServerType() {
        return getInstance().reachServerType;
    }

    public static void setReachServerType(String serverType) {
        getInstance().reachServerType = serverType != null ? serverType : "VANILLA";
        getInstance().save();
    }

    // CPS Overlay settings
    public static boolean isCPSOverlayEnabled() {
        return getInstance().cpsOverlayEnabled;
    }

    public static void setCPSOverlayEnabled(boolean enabled) {
        getInstance().cpsOverlayEnabled = enabled;
        getInstance().save();
    }

    public static int[] getCPSOverlayPosition() {
        ModSettings settings = getInstance();
        return new int[]{settings.cpsOverlayX, settings.cpsOverlayY};
    }

    public static void setCPSOverlayPosition(int x, int y) {
        ModSettings settings = getInstance();
        settings.cpsOverlayX = x;
        settings.cpsOverlayY = y;
        settings.save();
    }

    // AutoFisher settings
    public static boolean isAutoFisherEnabled() {
        return getInstance().autoFisherEnabled;
    }

    public static void setAutoFisherEnabled(boolean enabled) {
        getInstance().autoFisherEnabled = enabled;
        getInstance().save();
    }

    public static String getAutoFisherMode() {
        return getInstance().autoFisherMode;
    }

    public static void setAutoFisherMode(String mode) {
        getInstance().autoFisherMode = mode != null ? mode : "DATA_TRACKER";
        getInstance().save();
    }

    public static double getAutoFisherBubbleDistance() {
        return getInstance().autoFisherBubbleDistance;
    }

    public static void setAutoFisherBubbleDistance(double distance) {
        getInstance().autoFisherBubbleDistance = Math.max(0.5, Math.min(5.0, distance));
        getInstance().save();
    }

    public static long getAutoFisherTimeToWait() {
        return getInstance().autoFisherTimeToWait;
    }

    public static void setAutoFisherTimeToWait(long time) {
        getInstance().autoFisherTimeToWait = Math.max(0, Math.min(2000, time));
        getInstance().save();
    }

    // Script manager settings
    public static String getLastScriptName() {
        return getInstance().lastScriptName;
    }

    public static void setLastScriptName(String name) {
        getInstance().lastScriptName = name != null ? name : "farmscript";
        getInstance().save();
    }

    // Panel position methods
    public static void setPanelPosition(String panelName, double x, double y) {
        ModSettings settings = getInstance();
        switch (panelName.toLowerCase()) {
            case "scriptrecorder":
                settings.scriptRecorderPanelX = x;
                settings.scriptRecorderPanelY = y;
                break;
            case "scriptmanager":
                settings.scriptManagerPanelX = x;
                settings.scriptManagerPanelY = y;
                break;
            case "esp&tracers":
                settings.espTracersPanelX = x;
                settings.espTracersPanelY = y;
                break;
            case "reach":
                settings.reachPanelX = x;
                settings.reachPanelY = y;
                break;
            case "cheststealer":
                settings.chestStealerPanelX = x;
                settings.chestStealerPanelY = y;
                break;
            case "autoclicker":
                settings.autoClickerPanelX = x;
                settings.autoClickerPanelY = y;
                break;
            case "autofisher":
                settings.autoFisherPanelX = x;
                settings.autoFisherPanelY = y;
                break;
            case "serverpinger":
                settings.serverPingerPanelX = x;
                settings.serverPingerPanelY = y;
                break;
            case "settings":
                settings.settingsPanelX = x;
                settings.settingsPanelY = y;
                break;
        }
        settings.save();
    }

    public static double[] getPanelPosition(String panelName) {
        ModSettings settings = getInstance();
        switch (panelName.toLowerCase()) {
            case "scriptrecorder":
                return new double[]{settings.scriptRecorderPanelX, settings.scriptRecorderPanelY};
            case "scriptmanager":
                return new double[]{settings.scriptManagerPanelX, settings.scriptManagerPanelY};
            case "esp&tracers":
                return new double[]{settings.espTracersPanelX, settings.espTracersPanelY};
            case "reach":
                return new double[]{settings.reachPanelX, settings.reachPanelY};
            case "cheststealer":
                return new double[]{settings.chestStealerPanelX, settings.chestStealerPanelY};
            case "autoclicker":
                return new double[]{settings.autoClickerPanelX, settings.autoClickerPanelY};
            case "autofisher":
                return new double[]{settings.autoFisherPanelX, settings.autoFisherPanelY};
            case "serverpinger":
                return new double[]{settings.serverPingerPanelX, settings.serverPingerPanelY};
            case "settings":
                return new double[]{settings.settingsPanelX, settings.settingsPanelY};
            default:
                return new double[]{10.0, 10.0}; // Default position
        }
    }

    public static void setKeybind(String keybindType, int keyCode) {
        ModSettings settings = getInstance();
        switch (keybindType) {
            case "record":
                settings.keyRecord = keyCode;
                break;
            case "stop_record":
                settings.keyStopRecord = keyCode;
                break;
            case "play":
                settings.keyPlay = keyCode;
                break;
        }
        settings.save();
    }

    public static int getKeybind(String keybindType) {
        ModSettings settings = getInstance();
        switch (keybindType) {
            case "record":
                return settings.keyRecord;
            case "stop_record":
                return settings.keyStopRecord;
            case "play":
                return settings.keyPlay;
            default:
                return 0;
        }
    }
}
