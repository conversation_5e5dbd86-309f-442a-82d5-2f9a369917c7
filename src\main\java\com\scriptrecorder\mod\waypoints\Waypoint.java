package com.scriptrecorder.mod.waypoints;

import com.google.gson.annotations.SerializedName;
import net.minecraft.world.phys.Vec3;

/**
 * Represents a waypoint with name, position, and dimension information
 */
public class Waypoint {
    
    @SerializedName("name")
    private String name;
    
    @SerializedName("x")
    private double x;
    
    @SerializedName("y")
    private double y;
    
    @SerializedName("z")
    private double z;
    
    @SerializedName("dimension")
    private String dimension;
    
    @SerializedName("color")
    private int color;
    
    @SerializedName("enabled")
    private boolean enabled;
    
    public Waypoint(String name, double x, double y, double z, String dimension) {
        this.name = name;
        this.x = x;
        this.y = y;
        this.z = z;
        this.dimension = dimension;
        this.color = 0xFF00FF00; // Default green
        this.enabled = true;
    }
    
    // Default constructor for JSON deserialization
    public Waypoint() {
        this.enabled = true;
        this.color = 0xFF00FF00;
    }
    
    // Getters
    public String getName() { return name; }
    public double getX() { return x; }
    public double getY() { return y; }
    public double getZ() { return z; }
    public String getDimension() { return dimension; }
    public int getColor() { return color; }
    public boolean isEnabled() { return enabled; }
    
    // Setters
    public void setName(String name) { this.name = name; }
    public void setX(double x) { this.x = x; }
    public void setY(double y) { this.y = y; }
    public void setZ(double z) { this.z = z; }
    public void setDimension(String dimension) { this.dimension = dimension; }
    public void setColor(int color) { this.color = color; }
    public void setEnabled(boolean enabled) { this.enabled = enabled; }
    
    public Vec3 getPosition() {
        return new Vec3(x, y, z);
    }
    
    public void setPosition(Vec3 pos) {
        this.x = pos.x;
        this.y = pos.y;
        this.z = pos.z;
    }
    
    public double getDistanceTo(Vec3 pos) {
        return Math.sqrt(Math.pow(x - pos.x, 2) + Math.pow(y - pos.y, 2) + Math.pow(z - pos.z, 2));
    }
    
    public String getFormattedCoordinates() {
        return String.format("%.1f, %.1f, %.1f", x, y, z);
    }
    
    public String getFormattedDistance(Vec3 playerPos) {
        double distance = getDistanceTo(playerPos);
        if (distance < 1000) {
            return String.format("%.1fm", distance);
        } else {
            return String.format("%.1fkm", distance / 1000);
        }
    }
    
    @Override
    public String toString() {
        return name + " (" + getFormattedCoordinates() + ")";
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        Waypoint waypoint = (Waypoint) obj;
        return name != null ? name.equals(waypoint.name) : waypoint.name == null;
    }
    
    @Override
    public int hashCode() {
        return name != null ? name.hashCode() : 0;
    }
}
