# Minecraft Script Recorder

A Python-based tool to record and replay mouse and keyboard actions specifically for Mine<PERSON>. This tool allows you to automate repetitive tasks in Minecraft by recording your actions and playing them back.

## Features

- **Record Actions**: Capture mouse clicks, keyboard presses, and scroll events
- **Minecraft Window Detection**: Only records/plays when Minecraft window is active
- **Flexible Playback**: Play scripts once, multiple times, or infinitely
- **Save/Load Scripts**: Store your recorded scripts as JSON files for later use
- **User-friendly GUI**: Easy-to-use interface built with tkinter
- **Action Viewer**: See all recorded actions with timestamps and details

## Requirements

- Python 3.7 or higher
- Windows OS (due to pygetwindow dependency)
- Minecraft (Java Edition or Bedrock Edition)

## Installation

1. Clone or download this repository
2. Install the required dependencies:

```bash
pip install -r requirements.txt
```

## Usage

1. **Start the Application**:
   ```bash
   python minecraft_script_recorder.py
   ```

2. **Recording Actions**:
   - Make sure Minecraft is running and visible
   - Click "Start Recording" in the application
   - Perform the actions you want to record in Minecraft
   - Click "Stop Recording" when done

3. **Playing Back Scripts**:
   - Set the number of repetitions (or check "Infinite" for continuous playback)
   - Make sure Minecraft window is active
   - Click "Play Script" to start playback
   - Click "Stop" to interrupt playback if needed

4. **Managing Scripts**:
   - **Save Script**: Save your recorded actions to a JSON file
   - **Load Script**: Load a previously saved script
   - **Clear Actions**: Remove all currently recorded actions

## Important Notes

- **Window Focus**: The tool only records and plays actions when the Minecraft window is active/focused
- **Safety**: Always test scripts in a safe environment before using them in important gameplay
- **Timing**: The tool preserves the original timing between actions for accurate playback
- **Compatibility**: Works with both Minecraft Java Edition and Bedrock Edition

## File Format

Scripts are saved as JSON files containing:
- Action type (mouse click, key press, etc.)
- Timestamps for accurate timing
- Coordinates for mouse actions
- Key names for keyboard actions
- Metadata (creation date, duration, etc.)

## Troubleshooting

- **"Minecraft window not detected"**: Make sure Minecraft is running and the window title contains "Minecraft"
- **Actions not recording**: Ensure the Minecraft window is active and focused
- **Playback not working**: Verify Minecraft window is active during playback
- **Permission errors**: Run as administrator if needed (some systems require elevated permissions for input simulation)

## Safety and Ethics

This tool is intended for legitimate automation of repetitive tasks in single-player or approved multiplayer environments. Always:
- Respect server rules and terms of service
- Don't use for unfair advantages in competitive gameplay
- Test scripts thoroughly before use
- Use responsibly and ethically

## License

This project is provided as-is for educational and personal use.
