package com.scriptrecorder.mod;

import com.scriptrecorder.mod.client.KeyBindings;
import com.scriptrecorder.mod.commands.WaypointCommands189;
import com.scriptrecorder.mod.recording.ScriptManager;
import com.scriptrecorder.mod.waypoints.WaypointManager;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.SidedProxy;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.relauncher.Side;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Mod(modid = ScriptRecorderMod.MODID, name = ScriptRecorderMod.NAME, version = ScriptRecorderMod.VERSION)
public class ScriptRecorderMod {
    public static final String MODID = "scriptrecordermod";
    public static final String NAME = "Script Recorder Mod";
    public static final String VERSION = "3.6.1";
    public static final Logger LOGGER = LogManager.getLogger(MODID);

    public static ScriptManager scriptManager;
    public static WaypointManager waypointManager;

    @SidedProxy(clientSide = "com.scriptrecorder.mod.client.ClientProxy", serverSide = "com.scriptrecorder.mod.CommonProxy")
    public static CommonProxy proxy;

    @Mod.EventHandler
    public void preInit(FMLPreInitializationEvent event) {
        LOGGER.info("Waypoint Mod pre-initialization");

        // Initialize script manager
        scriptManager = new ScriptManager();

        // Initialize waypoint manager
        waypointManager = WaypointManager.getInstance();

        proxy.preInit(event);
    }

    @Mod.EventHandler
    public void init(FMLInitializationEvent event) {
        LOGGER.info("Waypoint Mod initialization");

        proxy.init(event);
    }
}
