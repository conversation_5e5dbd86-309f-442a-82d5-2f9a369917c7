package com.scriptrecorder.mod;

import com.scriptrecorder.mod.client.KeyBindings;
import com.scriptrecorder.mod.client.ScriptRecorderClient;
import com.scriptrecorder.mod.commands.WaypointCommands;
import com.scriptrecorder.mod.recording.ScriptManager;
import com.scriptrecorder.mod.waypoints.WaypointManager;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.client.event.RegisterClientCommandsEvent;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.eventbus.api.IEventBus;
import net.minecraftforge.eventbus.api.SubscribeEvent;
import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.event.lifecycle.FMLClientSetupEvent;
import net.minecraftforge.fml.event.lifecycle.FMLCommonSetupEvent;
import net.minecraftforge.fml.javafmlmod.FMLJavaModLoadingContext;
import net.minecraftforge.fml.loading.FMLEnvironment;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Mod(ScriptRecorderMod.MODID)
public class ScriptRecorderMod {
    public static final String MODID = "waypointmod";
    public static final String NAME = "Waypoint Mod";
    public static final String VERSION = "3.5.0";
    public static final Logger LOGGER = LogManager.getLogger(MODID);

    public static ScriptManager scriptManager;
    public static WaypointManager waypointManager;

    public ScriptRecorderMod() {
        IEventBus modEventBus = FMLJavaModLoadingContext.get().getModEventBus();

        // Register setup events
        modEventBus.addListener(this::commonSetup);

        // Client-side only setup
        if (FMLEnvironment.dist == Dist.CLIENT) {
            modEventBus.addListener(this::clientSetup);
            // Register keybind registration event - this is crucial!
            modEventBus.addListener(KeyBindings::registerKeyMappings);
            // Register for client command events
            MinecraftForge.EVENT_BUS.register(this);
        }
    }

    private void commonSetup(final FMLCommonSetupEvent event) {
        LOGGER.info("Waypoint Mod common setup");

        // Initialize script manager
        scriptManager = new ScriptManager();

        // Initialize waypoint manager
        waypointManager = WaypointManager.getInstance();
    }

    private void clientSetup(final FMLClientSetupEvent event) {
        LOGGER.info("Waypoint Mod client setup");

        // Register key bindings
        KeyBindings.register();

        // Register client event handlers
        MinecraftForge.EVENT_BUS.register(new ScriptRecorderClient());

        // Initialize client
        ScriptRecorderClient.init();
    }

    @SubscribeEvent
    public void onRegisterClientCommands(RegisterClientCommandsEvent event) {
        if (FMLEnvironment.dist == Dist.CLIENT) {
            WaypointCommands.register(event.getDispatcher());
            LOGGER.info("Registered client commands");
        }
    }
}
