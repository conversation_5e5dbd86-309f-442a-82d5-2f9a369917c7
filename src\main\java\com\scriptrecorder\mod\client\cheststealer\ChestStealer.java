package com.scriptrecorder.mod.client.cheststealer;

import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.screens.inventory.AbstractContainerScreen;
import net.minecraft.client.gui.screens.inventory.ContainerScreen;
import net.minecraft.world.inventory.AbstractContainerMenu;
import net.minecraft.world.inventory.ChestMenu;
import net.minecraft.world.inventory.Slot;
import net.minecraft.world.item.ItemStack;
import net.minecraft.network.chat.Component;
import java.util.List;
import java.util.ArrayList;
import java.util.Random;

/**
 * ChestStealer implementation with intelligent item selection and anti-detection features
 */
public class ChestStealer {
    
    private static ChestStealer instance;
    
    // Settings
    private boolean enabled = false;
    private int delayMin = 100;
    private int delayMax = 200;
    private int openDelay = 100;
    private int closeDelay = 100;
    private int randomization = 10;
    private int failChance = 5;
    private boolean intelligent = true;
    private boolean checkTitle = false;
    private String titleSubstring = "Chest";
    private TitleCheckType titleCheckType = TitleCheckType.CONTAINS;
    private boolean ignoreCase = true;
    
    // State tracking
    private boolean wasContainerOpen = false;
    private long lastActionTime = 0;
    private long nextActionDelay = 0;
    private int currentSlotIndex = 0;
    private boolean isProcessing = false;
    private Random random = new Random();
    
    // Title check types
    public enum TitleCheckType {
        CONTAINS("Contains"),
        EQUALS("Equals");
        
        private final String name;
        
        TitleCheckType(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
    }
    
    private ChestStealer() {}
    
    public static ChestStealer getInstance() {
        if (instance == null) {
            instance = new ChestStealer();
        }
        return instance;
    }
    
    /**
     * Update the chest stealer (called every tick)
     */
    public void update() {
        if (!enabled) {
            reset();
            return;
        }
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) return;
        
        // Check if we have a container screen open
        if (!(mc.screen instanceof AbstractContainerScreen)) {
            if (wasContainerOpen) {
                reset();
            }
            return;
        }
        
        AbstractContainerScreen<?> containerScreen = (AbstractContainerScreen<?>) mc.screen;
        
        // Validate container type
        if (!isValidContainer(containerScreen)) {
            return;
        }
        
        // Check title if enabled
        if (checkTitle && !isValidTitle(containerScreen)) {
            return;
        }
        
        // Handle container opening delay
        if (!wasContainerOpen) {
            wasContainerOpen = true;
            lastActionTime = System.currentTimeMillis();
            nextActionDelay = openDelay + random.nextInt(randomization);
            return;
        }
        
        // Check if we need to wait
        if (System.currentTimeMillis() - lastActionTime < nextActionDelay) {
            return;
        }
        
        // Process chest stealing
        processChestStealing(containerScreen);
    }
    
    /**
     * Check if the container is a valid type for stealing
     */
    private boolean isValidContainer(AbstractContainerScreen<?> screen) {
        return screen instanceof ContainerScreen ||
               screen.getMenu() instanceof ChestMenu;
    }
    
    /**
     * Check if the container title matches our criteria
     */
    private boolean isValidTitle(AbstractContainerScreen<?> screen) {
        Component title = screen.getTitle();
        String titleText = title.getString();
        
        switch (titleCheckType) {
            case CONTAINS:
                return ignoreCase ? 
                    titleText.toLowerCase().contains(titleSubstring.toLowerCase()) :
                    titleText.contains(titleSubstring);
            case EQUALS:
                return ignoreCase ?
                    titleText.equalsIgnoreCase(titleSubstring) :
                    titleText.equals(titleSubstring);
            default:
                return true;
        }
    }
    
    /**
     * Process the chest stealing logic
     */
    private void processChestStealing(AbstractContainerScreen<?> containerScreen) {
        AbstractContainerMenu menu = containerScreen.getMenu();
        List<Slot> containerSlots = getContainerSlots(menu);
        
        if (containerSlots.isEmpty()) {
            // No more items to steal, close after delay
            if (!isProcessing) {
                isProcessing = true;
                lastActionTime = System.currentTimeMillis();
                nextActionDelay = closeDelay + random.nextInt(randomization);
            } else if (System.currentTimeMillis() - lastActionTime >= nextActionDelay) {
                closeContainer();
            }
            return;
        }
        
        // Find next slot to steal from
        Slot targetSlot = findNextSlot(containerSlots);
        if (targetSlot == null) {
            // No valid slots found
            return;
        }
        
        // Apply fail chance for humanization
        if (random.nextInt(100) < failChance) {
            // Intentionally "miss" by clicking an empty slot
            targetSlot = findEmptySlot(containerSlots);
            if (targetSlot == null) {
                targetSlot = findNextSlot(containerSlots); // Fallback to valid slot
            }
        }
        
        // Perform the click
        if (targetSlot != null) {
            clickSlot(targetSlot);
            
            // Calculate next delay based on distance and randomization
            int baseDelay = delayMin + random.nextInt(delayMax - delayMin + 1);
            int randomDelay = random.nextInt(randomization * 2) - randomization;
            nextActionDelay = Math.max(50, baseDelay + randomDelay);
            
            lastActionTime = System.currentTimeMillis();
        }
    }
    
    /**
     * Get all container slots (excluding player inventory)
     */
    private List<Slot> getContainerSlots(AbstractContainerMenu menu) {
        List<Slot> containerSlots = new ArrayList<>();
        
        // Get container size (excluding player inventory)
        int containerSize = menu.slots.size() - 36; // 36 = player inventory size
        
        for (int i = 0; i < containerSize; i++) {
            Slot slot = menu.getSlot(i);
            if (slot.hasItem()) {
                containerSlots.add(slot);
            }
        }
        
        return containerSlots;
    }
    
    /**
     * Find the next slot to steal from
     */
    private Slot findNextSlot(List<Slot> containerSlots) {
        for (Slot slot : containerSlots) {
            if (slot.hasItem() && canTakeItem(slot)) {
                if (!intelligent || !hasBetterEquivalent(slot)) {
                    return slot;
                }
            }
        }
        return null;
    }
    
    /**
     * Find an empty slot for "failing"
     */
    private Slot findEmptySlot(List<Slot> containerSlots) {
        Minecraft mc = Minecraft.getInstance();
        AbstractContainerMenu menu = mc.player.containerMenu;
        
        int containerSize = menu.slots.size() - 36;
        for (int i = 0; i < containerSize; i++) {
            Slot slot = menu.getSlot(i);
            if (!slot.hasItem()) {
                return slot;
            }
        }
        return null;
    }
    
    /**
     * Check if we can take an item (have inventory space)
     */
    private boolean canTakeItem(Slot slot) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null) return false;
        
        ItemStack itemStack = slot.getItem();
        
        // Check if player has space in inventory
        for (int i = 0; i < mc.player.getInventory().getContainerSize(); i++) {
            ItemStack playerStack = mc.player.getInventory().getItem(i);
            
            // Empty slot
            if (playerStack.isEmpty()) {
                return true;
            }
            
            // Can stack with existing item
            if (ItemStack.isSameItemSameTags(playerStack, itemStack) && 
                playerStack.getCount() < playerStack.getMaxStackSize()) {
                return true;
            }
        }
        
        return false;
    }
    
    /**
     * Check if player already has a better equivalent item (intelligent mode)
     */
    private boolean hasBetterEquivalent(Slot slot) {
        if (!intelligent) return false;
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null) return false;
        
        ItemStack chestItem = slot.getItem();
        
        // Check player inventory for better items
        for (int i = 0; i < mc.player.getInventory().getContainerSize(); i++) {
            ItemStack playerItem = mc.player.getInventory().getItem(i);
            
            if (playerItem.isEmpty()) continue;
            
            // Same item type
            if (chestItem.getItem() == playerItem.getItem()) {
                // For tools/armor, check enchantments and durability
                if (chestItem.isEnchanted() || playerItem.isEnchanted()) {
                    // Simple check: if player item is enchanted and chest item isn't, skip
                    if (playerItem.isEnchanted() && !chestItem.isEnchanted()) {
                        return true;
                    }
                }
                
                // For damaged items, prefer higher durability
                if (chestItem.isDamaged() && playerItem.isDamaged()) {
                    if (playerItem.getDamageValue() < chestItem.getDamageValue()) {
                        return true; // Player item has less damage (better)
                    }
                }
            }
        }
        
        return false;
    }
    
    /**
     * Click a slot to take the item
     */
    private void clickSlot(Slot slot) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.gameMode != null && mc.player != null) {
            // Shift-click to quickly move item to player inventory
            mc.gameMode.handleInventoryMouseClick(
                mc.player.containerMenu.containerId,
                slot.index,
                0, // Left click
                net.minecraft.world.inventory.ClickType.QUICK_MOVE,
                mc.player
            );
        }
    }
    
    /**
     * Close the container
     */
    private void closeContainer() {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null) {
            mc.player.closeContainer();
        }
        reset();
    }
    
    /**
     * Reset state
     */
    private void reset() {
        wasContainerOpen = false;
        isProcessing = false;
        currentSlotIndex = 0;
        lastActionTime = 0;
        nextActionDelay = 0;
    }
    
    /**
     * Get current status information
     */
    public String getStatus() {
        if (!enabled) return "Disabled";
        if (!wasContainerOpen) return "Waiting for container";
        if (isProcessing) return "Finishing...";
        return "Stealing items";
    }
    
    /**
     * Get items stolen count (simplified)
     */
    public int getItemsStolen() {
        // This would need more sophisticated tracking in a real implementation
        return 0;
    }
    
    // Getters and setters
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { 
        this.enabled = enabled; 
        if (!enabled) reset();
    }
    
    public int getDelayMin() { return delayMin; }
    public void setDelayMin(int delayMin) { this.delayMin = Math.max(50, Math.min(1000, delayMin)); }
    
    public int getDelayMax() { return delayMax; }
    public void setDelayMax(int delayMax) { this.delayMax = Math.max(50, Math.min(1000, delayMax)); }
    
    public int getOpenDelay() { return openDelay; }
    public void setOpenDelay(int openDelay) { this.openDelay = Math.max(0, Math.min(1000, openDelay)); }
    
    public int getCloseDelay() { return closeDelay; }
    public void setCloseDelay(int closeDelay) { this.closeDelay = Math.max(0, Math.min(1000, closeDelay)); }
    
    public int getRandomization() { return randomization; }
    public void setRandomization(int randomization) { this.randomization = Math.max(0, Math.min(100, randomization)); }
    
    public int getFailChance() { return failChance; }
    public void setFailChance(int failChance) { this.failChance = Math.max(0, Math.min(100, failChance)); }
    
    public boolean isIntelligent() { return intelligent; }
    public void setIntelligent(boolean intelligent) { this.intelligent = intelligent; }
    
    public boolean isCheckTitle() { return checkTitle; }
    public void setCheckTitle(boolean checkTitle) { this.checkTitle = checkTitle; }
    
    public String getTitleSubstring() { return titleSubstring; }
    public void setTitleSubstring(String titleSubstring) { this.titleSubstring = titleSubstring; }
    
    public TitleCheckType getTitleCheckType() { return titleCheckType; }
    public void setTitleCheckType(TitleCheckType titleCheckType) { this.titleCheckType = titleCheckType; }
    
    public boolean isIgnoreCase() { return ignoreCase; }
    public void setIgnoreCase(boolean ignoreCase) { this.ignoreCase = ignoreCase; }
    
    /**
     * Get formatted delay range
     */
    public String getDelayRange() {
        return delayMin + "-" + delayMax + "ms";
    }
    
    /**
     * Get formatted settings summary
     */
    public String getSettingsSummary() {
        return String.format("Delay: %s, Fail: %d%%, Intel: %s", 
            getDelayRange(), failChance, intelligent ? "ON" : "OFF");
    }
}
