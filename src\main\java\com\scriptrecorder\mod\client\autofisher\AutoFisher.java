package com.scriptrecorder.mod.client.autofisher;

import com.scriptrecorder.mod.client.settings.ModSettings;
import net.minecraft.client.Minecraft;
import net.minecraft.client.player.LocalPlayer;
import net.minecraft.world.entity.projectile.FishingHook;
import net.minecraft.world.item.FishingRodItem;
import net.minecraft.world.item.ItemStack;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

/**
 * Automated fishing system with multiple detection modes
 */
@OnlyIn(Dist.CLIENT)
public class AutoFisher {
    
    private static AutoFisher instance;
    
    // Settings
    private boolean enabled = false;
    private FishingMode mode = FishingMode.DATA_TRACKER;
    private double bubbleDistance = 1.5;
    private long timeToWait = 200; // milliseconds
    
    // State tracking
    private boolean hasCaught = false;
    private boolean blocked = false;
    private long lastCastTime = 0;
    private long lastReelTime = 0;
    private long bubbleDetectedTime = 0;
    
    // Timing constants
    private static final long MIN_CAST_DELAY = 500; // Minimum delay between casts
    private static final long MIN_REEL_DELAY = 300; // Minimum delay between reels
    private static final long EMERGENCY_REEL_TIME = 30000; // 30 seconds max fishing time
    
    public enum FishingMode {
        DATA_TRACKER("Data Tracker"),
        BUBBLES("Bubbles");
        
        private final String displayName;
        
        FishingMode(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
    
    private AutoFisher() {
        loadSettings();
    }
    
    public static AutoFisher getInstance() {
        if (instance == null) {
            instance = new AutoFisher();
        }
        return instance;
    }
    
    /**
     * Update the auto fisher (called every tick)
     */
    public void update() {
        if (!enabled) return;
        
        Minecraft mc = Minecraft.getInstance();
        LocalPlayer player = mc.player;
        
        if (player == null || mc.level == null) return;
        
        // Check if player has fishing rod
        if (!hasFishingRod(player)) return;
        
        // Handle fishing logic
        handleFishing(player);
    }
    
    /**
     * Check if player has a fishing rod in main or offhand
     */
    private boolean hasFishingRod(LocalPlayer player) {
        ItemStack mainHand = player.getMainHandItem();
        ItemStack offHand = player.getOffhandItem();
        
        return mainHand.getItem() instanceof FishingRodItem || 
               offHand.getItem() instanceof FishingRodItem;
    }
    
    /**
     * Handle the main fishing logic
     */
    private void handleFishing(LocalPlayer player) {
        FishingHook hook = player.fishing;
        long currentTime = System.currentTimeMillis();
        
        if (hook == null) {
            // No hook in water, try to cast
            if (canCast(currentTime)) {
                castLine(player);
            }
        } else {
            // Hook is in water, check if we should reel
            if (shouldReel(hook, currentTime)) {
                reelLine(player, currentTime);
            }
        }
    }
    
    /**
     * Check if we can cast the fishing line
     */
    private boolean canCast(long currentTime) {
        // Don't cast too frequently
        if (currentTime - lastCastTime < MIN_CAST_DELAY) {
            return false;
        }
        
        // Don't cast while moving (more natural)
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null && (mc.player.getDeltaMovement().horizontalDistanceSqr() > 0.01)) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Cast the fishing line
     */
    private void castLine(LocalPlayer player) {
        // Simulate right-click to cast
        Minecraft mc = Minecraft.getInstance();
        if (mc.options.keyUse.isDown()) return; // Don't interfere if player is manually using
        
        // Trigger use key
        mc.options.keyUse.setDown(true);
        mc.options.keyUse.setDown(false);
        
        lastCastTime = System.currentTimeMillis();
        blocked = false;
        hasCaught = false;
        
        System.out.println("AutoFisher: Cast line");
    }
    
    /**
     * Check if we should reel in the line
     */
    private boolean shouldReel(FishingHook hook, long currentTime) {
        if (blocked) return false;
        
        // Don't reel too frequently
        if (currentTime - lastReelTime < MIN_REEL_DELAY) {
            return false;
        }
        
        // Emergency reel if hook has been out too long
        if (currentTime - lastCastTime > EMERGENCY_REEL_TIME) {
            System.out.println("AutoFisher: Emergency reel (timeout)");
            return true;
        }
        
        // Check based on selected mode
        switch (mode) {
            case DATA_TRACKER:
                // Use vanilla fish detection
                return hook.getHookedIn() != null;
                
            case BUBBLES:
                // Use bubble detection with timing
                if (hasCaught && currentTime - bubbleDetectedTime >= timeToWait) {
                    return true;
                }
                break;
        }
        
        // Emergency conditions
        if (hook.onGround() && !hook.isInWater()) {
            System.out.println("AutoFisher: Emergency reel (hook on ground)");
            return true;
        }
        
        return false;
    }
    
    /**
     * Reel in the fishing line
     */
    private void reelLine(LocalPlayer player, long currentTime) {
        // Simulate right-click to reel
        Minecraft mc = Minecraft.getInstance();
        if (mc.options.keyUse.isDown()) return; // Don't interfere if player is manually using
        
        // Trigger use key
        mc.options.keyUse.setDown(true);
        mc.options.keyUse.setDown(false);
        
        lastReelTime = currentTime;
        blocked = true;
        hasCaught = false;
        
        System.out.println("AutoFisher: Reeled line");
    }
    
    /**
     * Called when bubble particles are detected near the hook
     */
    public void onBubbleDetected() {
        if (!enabled || mode != FishingMode.BUBBLES) return;
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.player.fishing == null) return;
        
        hasCaught = true;
        bubbleDetectedTime = System.currentTimeMillis();
        blocked = false;
        
        System.out.println("AutoFisher: Bubble detected");
    }
    
    /**
     * Load settings from ModSettings
     */
    public void loadSettings() {
        enabled = ModSettings.isAutoFisherEnabled();
        String modeStr = ModSettings.getAutoFisherMode();
        try {
            mode = FishingMode.valueOf(modeStr);
        } catch (IllegalArgumentException e) {
            mode = FishingMode.DATA_TRACKER;
        }
        bubbleDistance = ModSettings.getAutoFisherBubbleDistance();
        timeToWait = ModSettings.getAutoFisherTimeToWait();
    }
    
    // Getters and setters
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) { 
        this.enabled = enabled; 
        ModSettings.setAutoFisherEnabled(enabled);
    }
    
    public FishingMode getMode() { return mode; }
    public void setMode(FishingMode mode) { 
        this.mode = mode; 
        ModSettings.setAutoFisherMode(mode.name());
    }
    
    public double getBubbleDistance() { return bubbleDistance; }
    public void setBubbleDistance(double distance) { 
        this.bubbleDistance = Math.max(0.5, Math.min(5.0, distance)); 
        ModSettings.setAutoFisherBubbleDistance(this.bubbleDistance);
    }
    
    public long getTimeToWait() { return timeToWait; }
    public void setTimeToWait(long time) { 
        this.timeToWait = Math.max(0, Math.min(2000, time)); 
        ModSettings.setAutoFisherTimeToWait(this.timeToWait);
    }
    
    public void reset() {
        blocked = false;
        hasCaught = false;
        lastCastTime = 0;
        lastReelTime = 0;
        bubbleDetectedTime = 0;
    }
}
