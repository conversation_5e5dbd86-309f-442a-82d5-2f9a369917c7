package com.scriptrecorder.mod.recording;

import com.google.gson.annotations.SerializedName;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * Container for a complete recorded script with metadata
 */
public class RecordedScript {
    
    @SerializedName("version")
    private String version = "1.0";
    
    @SerializedName("created")
    private String created;
    
    @SerializedName("name")
    private String name;
    
    @SerializedName("description")
    private String description;
    
    @SerializedName("actions")
    private List<ScriptAction> actions;
    
    @SerializedName("totalActions")
    private int totalActions;
    
    @SerializedName("duration")
    private double duration; // Total duration in seconds
    
    @SerializedName("loopCount")
    private int loopCount = 1; // How many times to loop (0 = infinite)
    
    @SerializedName("playbackSpeed")
    private double playbackSpeed = 1.0; // Speed multiplier
    
    // Constructors
    public RecordedScript() {
        this.actions = new ArrayList<>();
        this.created = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
    }
    
    public RecordedScript(String name) {
        this();
        this.name = name;
    }
    
    public RecordedScript(String name, String description) {
        this(name);
        this.description = description;
    }
    
    // Methods to manage actions
    public void addAction(ScriptAction action) {
        actions.add(action);
        updateMetadata();
    }
    
    public void removeAction(int index) {
        if (index >= 0 && index < actions.size()) {
            actions.remove(index);
            updateMetadata();
        }
    }
    
    public void clearActions() {
        actions.clear();
        updateMetadata();
    }
    
    private void updateMetadata() {
        this.totalActions = actions.size();
        if (!actions.isEmpty()) {
            this.duration = actions.get(actions.size() - 1).getTime();
        } else {
            this.duration = 0.0;
        }
    }
    
    // Validation
    public boolean isValid() {
        return actions != null && !actions.isEmpty() && duration > 0;
    }
    
    public String getValidationError() {
        if (actions == null) return "Actions list is null";
        if (actions.isEmpty()) return "No actions recorded";
        if (duration <= 0) return "Invalid duration";
        return null;
    }
    
    // Getters
    public String getVersion() { return version; }
    public String getCreated() { return created; }
    public String getName() { return name; }
    public String getDescription() { return description; }
    public List<ScriptAction> getActions() { return new ArrayList<>(actions); }
    public int getTotalActions() { return totalActions; }
    public double getDuration() { return duration; }
    public int getLoopCount() { return loopCount; }
    public double getPlaybackSpeed() { return playbackSpeed; }
    
    // Setters
    public void setVersion(String version) { this.version = version; }
    public void setCreated(String created) { this.created = created; }
    public void setName(String name) { this.name = name; }
    public void setDescription(String description) { this.description = description; }
    public void setActions(List<ScriptAction> actions) { 
        this.actions = new ArrayList<>(actions);
        updateMetadata();
    }
    public void setLoopCount(int loopCount) { this.loopCount = Math.max(0, loopCount); }
    public void setPlaybackSpeed(double playbackSpeed) { 
        this.playbackSpeed = Math.max(0.1, Math.min(10.0, playbackSpeed)); 
    }
    
    // Utility methods
    public String getFormattedDuration() {
        if (duration < 60) {
            return String.format("%.1fs", duration);
        } else {
            int minutes = (int) (duration / 60);
            double seconds = duration % 60;
            return String.format("%dm %.1fs", minutes, seconds);
        }
    }
    
    public String getLoopDescription() {
        if (loopCount == 0) return "Infinite";
        if (loopCount == 1) return "Once";
        return loopCount + " times";
    }
    
    @Override
    public String toString() {
        return String.format("RecordedScript{name='%s', actions=%d, duration=%s, loops=%s}", 
            name != null ? name : "Unnamed", 
            totalActions, 
            getFormattedDuration(), 
            getLoopDescription());
    }
}
