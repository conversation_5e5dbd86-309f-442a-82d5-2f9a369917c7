package com.scriptrecorder.mod.commands;

import com.mojang.brigadier.CommandDispatcher;
import com.mojang.brigadier.arguments.StringArgumentType;
import com.mojang.brigadier.context.CommandContext;

import com.scriptrecorder.mod.waypoints.Waypoint;
import com.scriptrecorder.mod.waypoints.WaypointManager;
import net.minecraft.client.Minecraft;
import net.minecraft.commands.CommandSourceStack;
import net.minecraft.commands.Commands;
import net.minecraft.network.chat.Component;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;

import java.util.List;

/**
 * Handles waypoint-related commands
 */
@OnlyIn(Dist.CLIENT)
public class WaypointCommand {
    
    public static void register(CommandDispatcher<CommandSourceStack> dispatcher) {
        // Remove the duplicate waypointgui registration - it's handled in WaypointCommands.java

        dispatcher.register(Commands.literal("waypoint")
            .then(Commands.literal("add")
                .then(Commands.argument("name", StringArgumentType.greedyString())
                    .executes(WaypointCommand::addWaypoint)))
            .then(Commands.literal("remove")
                .then(Commands.argument("name", StringArgumentType.greedyString())
                    .executes(WaypointCommand::removeWaypoint)))
            .then(Commands.literal("list")
                .executes(WaypointCommand::listWaypoints))
            .then(Commands.literal("clear")
                .executes(WaypointCommand::clearWaypoints)));
    }
    

    
    private static int addWaypoint(CommandContext<CommandSourceStack> context) {
        String name = StringArgumentType.getString(context, "name");
        WaypointManager manager = WaypointManager.getInstance();
        
        if (manager.addWaypointAtCurrentLocation(name)) {
            manager.sendChatMessage("Added waypoint: " + name, false);
        } else {
            manager.sendChatMessage("Failed to add waypoint (name already exists or invalid)", true);
        }
        
        return 1;
    }
    
    private static int removeWaypoint(CommandContext<CommandSourceStack> context) {
        String name = StringArgumentType.getString(context, "name");
        WaypointManager manager = WaypointManager.getInstance();
        
        if (manager.removeWaypoint(name)) {
            manager.sendChatMessage("Removed waypoint: " + name, false);
        } else {
            manager.sendChatMessage("Waypoint not found: " + name, true);
        }
        
        return 1;
    }
    
    private static int listWaypoints(CommandContext<CommandSourceStack> context) {
        WaypointManager manager = WaypointManager.getInstance();
        List<Waypoint> waypoints = manager.getAllWaypoints();
        
        if (waypoints.isEmpty()) {
            manager.sendChatMessage("No waypoints found", false);
        } else {
            manager.sendChatMessage("Waypoints (" + waypoints.size() + "):", false);
            Vec3 playerPos = manager.getCurrentPlayerPosition();
            
            for (Waypoint waypoint : waypoints) {
                String message = String.format("- %s: %s [%s]", 
                    waypoint.getName(), 
                    waypoint.getFormattedCoordinates(),
                    waypoint.getFormattedDistance(playerPos));
                manager.sendChatMessage(message, false);
            }
        }
        
        return 1;
    }
    
    private static int clearWaypoints(CommandContext<CommandSourceStack> context) {
        WaypointManager manager = WaypointManager.getInstance();
        int count = manager.getWaypointCount();
        manager.clearAllWaypoints();
        manager.sendChatMessage("Cleared " + count + " waypoints", false);
        return 1;
    }
}
