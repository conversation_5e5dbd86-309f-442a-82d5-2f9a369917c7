package com.scriptrecorder.mod.client.gui;

import com.scriptrecorder.mod.client.ScriptRecorderClient;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;

/**
 * Simple, focused GUI for toggle sprint and crouch controls only
 */
public class ToggleSprintScreen extends Screen {

    // GUI Dimensions and Colors
    private static final int GUI_WIDTH = 280;
    private static final int GUI_HEIGHT = 140;
    private static final int PANEL_COLOR = 0xE0000000; // Semi-transparent black
    private static final int BORDER_COLOR = 0xFF404040; // Dark gray
    private static final int ACCENT_COLOR = 0xFF00AA00; // Green
    private static final int TEXT_COLOR = 0xFFFFFFFF; // White

    // GUI Components
    private Button toggleSprintButton;
    private Button toggleCrouchButton;

    public ToggleSprintScreen() {
        super(Component.literal("Toggle Controls"));
    }

    @Override
    protected void init() {
        super.init();
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int guiLeft = centerX - GUI_WIDTH / 2;
        int guiTop = centerY - GUI_HEIGHT / 2;
        
        // Toggle Sprint Button
        toggleSprintButton = Button.builder(
            getSprintButtonText(),
            button -> toggleSprint())
            .bounds(guiLeft + 20, guiTop + 50, 110, 24)
            .build();
        this.addRenderableWidget(toggleSprintButton);
        
        // Toggle Crouch Button
        toggleCrouchButton = Button.builder(
            getCrouchButtonText(),
            button -> toggleCrouch())
            .bounds(guiLeft + 150, guiTop + 50, 110, 24)
            .build();
        this.addRenderableWidget(toggleCrouchButton);
        
        // Close Button
        this.addRenderableWidget(Button.builder(
            Component.literal("Close"),
            button -> this.onClose())
            .bounds(guiLeft + GUI_WIDTH / 2 - 40, guiTop + GUI_HEIGHT - 35, 80, 20)
            .build());
    }

    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        this.renderBackground(guiGraphics, mouseX, mouseY, partialTick);
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int guiLeft = centerX - GUI_WIDTH / 2;
        int guiTop = centerY - GUI_HEIGHT / 2;
        
        // Draw main panel background
        guiGraphics.fill(guiLeft, guiTop, guiLeft + GUI_WIDTH, guiTop + GUI_HEIGHT, PANEL_COLOR);
        
        // Draw border
        drawBorder(guiGraphics, guiLeft, guiTop, GUI_WIDTH, GUI_HEIGHT, BORDER_COLOR);
        
        // Draw title
        String title = "Toggle Controls";
        guiGraphics.drawCenteredString(this.font, title, centerX, guiTop + 15, ACCENT_COLOR);
        
        // Draw status indicators
        drawStatusIndicators(guiGraphics, guiLeft, guiTop);
        
        super.render(guiGraphics, mouseX, mouseY, partialTick);
    }
    
    private void drawBorder(GuiGraphics guiGraphics, int x, int y, int width, int height, int color) {
        // Top
        guiGraphics.fill(x, y, x + width, y + 1, color);
        // Bottom  
        guiGraphics.fill(x, y + height - 1, x + width, y + height, color);
        // Left
        guiGraphics.fill(x, y, x + 1, y + height, color);
        // Right
        guiGraphics.fill(x + width - 1, y, x + width, y + height, color);
    }
    
    private void drawStatusIndicators(GuiGraphics guiGraphics, int guiLeft, int guiTop) {
        // Sprint status
        boolean sprintEnabled = ScriptRecorderClient.isSprintToggled();
        String sprintStatus = sprintEnabled ? "ACTIVE" : "INACTIVE";
        int sprintColor = sprintEnabled ? ACCENT_COLOR : 0xFF888888;
        guiGraphics.drawCenteredString(this.font, sprintStatus, guiLeft + 75, guiTop + 80, sprintColor);
        
        // Crouch status
        boolean crouchEnabled = ScriptRecorderClient.isCrouchToggled();
        String crouchStatus = crouchEnabled ? "ACTIVE" : "INACTIVE";
        int crouchColor = crouchEnabled ? ACCENT_COLOR : 0xFF888888;
        guiGraphics.drawCenteredString(this.font, crouchStatus, guiLeft + 205, guiTop + 80, crouchColor);
    }
    
    private Component getSprintButtonText() {
        boolean enabled = ScriptRecorderClient.isSprintToggled();
        return Component.literal("Sprint: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getCrouchButtonText() {
        boolean enabled = ScriptRecorderClient.isCrouchToggled();
        return Component.literal("Crouch: " + (enabled ? "ON" : "OFF"));
    }
    
    private void toggleSprint() {
        boolean newState = !ScriptRecorderClient.isSprintToggled();
        ScriptRecorderClient.setSprintToggled(newState);
        
        // Update button text
        toggleSprintButton.setMessage(getSprintButtonText());
        
        // Send chat message
        sendChatMessage("Sprint toggle: " + (newState ? "ON" : "OFF"), false);
    }
    
    private void toggleCrouch() {
        boolean newState = !ScriptRecorderClient.isCrouchToggled();
        ScriptRecorderClient.setCrouchToggled(newState);
        
        // Update button text
        toggleCrouchButton.setMessage(getCrouchButtonText());
        
        // Send chat message
        sendChatMessage("Crouch toggle: " + (newState ? "ON" : "OFF"), false);
    }
    
    private void sendChatMessage(String message, boolean isError) {
        if (minecraft != null && minecraft.player != null) {
            MutableComponent chatMessage = Component.literal("[Toggle Sprint Mod] " + message);
            if (isError) {
                chatMessage = chatMessage.withStyle(style -> style.withColor(0xFF4444));
            } else {
                chatMessage = chatMessage.withStyle(style -> style.withColor(0x44FF44));
            }
            minecraft.player.sendSystemMessage(chatMessage);
        }
    }

    @Override
    public boolean isPauseScreen() {
        return false;
    }
}
