package com.scriptrecorder.mod.client.gui;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.ScriptRecorderClient;
import com.scriptrecorder.mod.client.overlay.KeystrokeOverlay;
import com.scriptrecorder.mod.client.settings.ModSettings;
import com.scriptrecorder.mod.recording.RecordedScript;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import net.minecraft.network.chat.MutableComponent;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import org.lwjgl.glfw.GLFW;

@OnlyIn(Dist.CLIENT)
public class SimpleScriptRecorderScreen extends Screen {
    
    private static final int GUI_WIDTH = 300;
    private static final int GUI_HEIGHT = 200;
    private static final int TEXT_COLOR = 0xFFFFFF;
    private static final int BACKGROUND_COLOR = 0x80000000;
    
    // Main components
    private EditBox scriptNameBox;
    private EditBox loopCountBox;
    private Button recordButton;
    private Button playButton;
    private Button stopButton;
    private Button sprintToggleButton;
    private Button crouchToggleButton;
    private Button keystrokeToggleButton;
    private Button exportButton;
    private Button importButton;
    
    public SimpleScriptRecorderScreen() {
        super(Component.literal("Script Recorder"));
    }
    
    @Override
    protected void init() {
        super.init();
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int startX = centerX - GUI_WIDTH / 2;
        int startY = centerY - GUI_HEIGHT / 2;
        
        // Script name input
        this.scriptNameBox = new EditBox(this.font, startX + 10, startY + 30, 200, 20, Component.literal("Script Name"));
        this.scriptNameBox.setValue("QuickScript_" + System.currentTimeMillis());
        this.addWidget(this.scriptNameBox);
        
        // Loop count input
        this.loopCountBox = new EditBox(this.font, startX + 220, startY + 30, 60, 20, Component.literal("Loops"));
        this.loopCountBox.setValue("1");
        this.addWidget(this.loopCountBox);
        
        // Recording controls
        this.recordButton = Button.builder(Component.literal("Record"), button -> toggleRecording())
            .bounds(startX + 10, startY + 60, 80, 20).build();
        this.addRenderableWidget(this.recordButton);
        
        this.playButton = Button.builder(Component.literal("Play"), button -> togglePlayback())
            .bounds(startX + 100, startY + 60, 80, 20).build();
        this.addRenderableWidget(this.playButton);
        
        this.stopButton = Button.builder(Component.literal("Stop"), button -> stopAll())
            .bounds(startX + 190, startY + 60, 80, 20).build();
        this.addRenderableWidget(this.stopButton);
        
        // Toggle features
        ModSettings settings = ModSettings.getInstance();
        
        this.sprintToggleButton = Button.builder(
            Component.literal("Sprint: " + (settings.sprintToggled ? "ON" : "OFF")), 
            button -> toggleSprint())
            .bounds(startX + 10, startY + 90, 120, 20).build();
        this.addRenderableWidget(this.sprintToggleButton);
        
        this.crouchToggleButton = Button.builder(
            Component.literal("Crouch: " + (settings.crouchToggled ? "ON" : "OFF")), 
            button -> toggleCrouch())
            .bounds(startX + 140, startY + 90, 120, 20).build();
        this.addRenderableWidget(this.crouchToggleButton);
        
        this.keystrokeToggleButton = Button.builder(
            Component.literal("Keystrokes: " + (settings.keystrokeDisplayEnabled ? "ON" : "OFF")),
            button -> toggleKeystrokes())
            .bounds(startX + 10, startY + 120, 120, 20).build();
        this.addRenderableWidget(this.keystrokeToggleButton);

        // Export/Import buttons
        this.exportButton = Button.builder(
            Component.literal("Export"),
            button -> onExportPressed())
            .bounds(startX + 140, startY + 120, 60, 20).build();
        this.addRenderableWidget(this.exportButton);

        this.importButton = Button.builder(
            Component.literal("Import"),
            button -> onImportPressed())
            .bounds(startX + 210, startY + 120, 60, 20).build();
        this.addRenderableWidget(this.importButton);

        updateButtonStates();
    }
    
    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        this.renderBackground(guiGraphics, mouseX, mouseY, partialTick);
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int startX = centerX - GUI_WIDTH / 2;
        int startY = centerY - GUI_HEIGHT / 2;
        
        // Draw background
        guiGraphics.fill(startX, startY, startX + GUI_WIDTH, startY + GUI_HEIGHT, BACKGROUND_COLOR);
        
        // Draw title
        guiGraphics.drawCenteredString(this.font, "Script Recorder", centerX, startY + 10, TEXT_COLOR);
        
        // Draw labels
        guiGraphics.drawString(this.font, "Script Name:", startX + 10, startY + 20, TEXT_COLOR);
        guiGraphics.drawString(this.font, "Loops:", startX + 220, startY + 20, TEXT_COLOR);
        
        // Draw status
        String status = getStatusText();
        guiGraphics.drawString(this.font, "Status: " + status, startX + 10, startY + 150, TEXT_COLOR);
        
        super.render(guiGraphics, mouseX, mouseY, partialTick);
    }
    
    private String getStatusText() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            return "Recording...";
        } else if (ScriptRecorderMod.scriptManager.isPlaying()) {
            return "Playing...";
        } else {
            return "Ready";
        }
    }
    
    private void toggleRecording() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            ScriptRecorderMod.scriptManager.stopRecording();
        } else {
            String scriptName = this.scriptNameBox.getValue();
            if (scriptName.isEmpty()) {
                scriptName = "QuickScript_" + System.currentTimeMillis();
            }
            ScriptRecorderMod.scriptManager.startRecording(scriptName);
        }
        updateButtonStates();
    }
    
    private void togglePlayback() {
        if (ScriptRecorderMod.scriptManager.isPlaying()) {
            ScriptRecorderMod.scriptManager.stopPlayback();
        } else {
            RecordedScript currentScript = ScriptRecorderMod.scriptManager.getCurrentScript();
            if (currentScript != null && currentScript.isValid()) {
                ScriptRecorderMod.scriptManager.startPlayback(currentScript);
            }
        }
        updateButtonStates();
    }
    
    private void stopAll() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            ScriptRecorderMod.scriptManager.stopRecording();
        }
        if (ScriptRecorderMod.scriptManager.isPlaying()) {
            ScriptRecorderMod.scriptManager.stopPlayback();
        }
        updateButtonStates();
    }
    
    private void toggleSprint() {
        ModSettings settings = ModSettings.getInstance();
        settings.toggleSprint();
        ScriptRecorderClient.setSprintToggled(settings.sprintToggled);
        this.sprintToggleButton.setMessage(Component.literal("Sprint: " + (settings.sprintToggled ? "ON" : "OFF")));
    }
    
    private void toggleCrouch() {
        ModSettings settings = ModSettings.getInstance();
        settings.toggleCrouch();
        ScriptRecorderClient.setCrouchToggled(settings.crouchToggled);
        this.crouchToggleButton.setMessage(Component.literal("Crouch: " + (settings.crouchToggled ? "ON" : "OFF")));
    }
    
    private void toggleKeystrokes() {
        ModSettings settings = ModSettings.getInstance();
        settings.toggleKeystrokeDisplay();
        KeystrokeOverlay.setEnabled(settings.keystrokeDisplayEnabled);
        this.keystrokeToggleButton.setMessage(Component.literal("Keystrokes: " + (settings.keystrokeDisplayEnabled ? "ON" : "OFF")));
    }

    private void onExportPressed() {
        RecordedScript currentScript = ScriptRecorderMod.scriptManager.getCurrentScript();
        if (currentScript == null) {
            sendChatMessage("No script to export! Record or load a script first.", true);
            return;
        }

        String scriptName = scriptNameBox.getValue().trim();
        if (scriptName.isEmpty()) {
            sendChatMessage("Please enter a script name for export.", true);
            return;
        }

        // Check if script already exists
        if (ScriptRecorderMod.scriptManager.scriptExists(scriptName)) {
            sendChatMessage("Warning: Script '" + scriptName + "' already exists and will be overwritten.", false);
        }

        // Export using the enhanced method
        if (ScriptRecorderMod.scriptManager.exportScript(currentScript, scriptName)) {
            sendChatMessage("Script exported as '" + scriptName + ".json' (" +
                currentScript.getTotalActions() + " actions, " +
                currentScript.getFormattedDuration() + ")", false);
        } else {
            sendChatMessage("Failed to export script!", true);
        }
    }

    private void onImportPressed() {
        String scriptName = scriptNameBox.getValue().trim();
        if (scriptName.isEmpty()) {
            sendChatMessage("Please enter a script name to import.", true);
            return;
        }

        // Check if script exists before trying to import
        if (!ScriptRecorderMod.scriptManager.scriptExists(scriptName)) {
            sendChatMessage("Script '" + scriptName + "' not found. Available scripts: " +
                String.join(", ", ScriptRecorderMod.scriptManager.getAvailableScripts()), true);
            return;
        }

        RecordedScript loadedScript = ScriptRecorderMod.scriptManager.importScript(scriptName);
        if (loadedScript != null) {
            sendChatMessage("Script '" + scriptName + "' imported successfully! " +
                loadedScript.getTotalActions() + " actions, " +
                loadedScript.getFormattedDuration() + " loaded.", false);
        } else {
            sendChatMessage("Failed to import script '" + scriptName + "'. File may be corrupted.", true);
        }
    }

    private void sendChatMessage(String message, boolean isError) {
        if (minecraft != null && minecraft.player != null) {
            MutableComponent chatMessage = Component.literal("[Toggle Sprint Mod] " + message);
            if (isError) {
                chatMessage = chatMessage.withStyle(style -> style.withColor(0xFF4444));
            } else {
                chatMessage = chatMessage.withStyle(style -> style.withColor(0x44FF44));
            }
            minecraft.player.sendSystemMessage(chatMessage);
        }
    }

    private void updateButtonStates() {
        boolean isRecording = ScriptRecorderMod.scriptManager.isRecording();
        boolean isPlaying = ScriptRecorderMod.scriptManager.isPlaying();
        boolean hasScript = ScriptRecorderMod.scriptManager.getCurrentScript() != null;

        this.recordButton.setMessage(Component.literal(isRecording ? "Stop Recording" : "Record"));
        this.playButton.setMessage(Component.literal(isPlaying ? "Stop Playing" : "Play"));
        this.playButton.active = !isRecording && hasScript;

        // Export button is only active when there's a script to export
        if (exportButton != null) exportButton.active = hasScript && !isRecording && !isPlaying;

        // Import button is always active (unless recording/playing)
        if (importButton != null) importButton.active = !isRecording && !isPlaying;
    }
    
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (keyCode == GLFW.GLFW_KEY_ESCAPE) {
            this.onClose();
            return true;
        }
        
        if (this.scriptNameBox.isFocused()) {
            return this.scriptNameBox.keyPressed(keyCode, scanCode, modifiers);
        }
        
        if (this.loopCountBox.isFocused()) {
            return this.loopCountBox.keyPressed(keyCode, scanCode, modifiers);
        }
        
        return super.keyPressed(keyCode, scanCode, modifiers);
    }
    
    @Override
    public boolean charTyped(char codePoint, int modifiers) {
        if (this.scriptNameBox.isFocused()) {
            return this.scriptNameBox.charTyped(codePoint, modifiers);
        }
        
        if (this.loopCountBox.isFocused()) {
            return this.loopCountBox.charTyped(codePoint, modifiers);
        }
        
        return super.charTyped(codePoint, modifiers);
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        this.scriptNameBox.mouseClicked(mouseX, mouseY, button);
        this.loopCountBox.mouseClicked(mouseX, mouseY, button);
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    @Override
    public boolean isPauseScreen() {
        return false;
    }
}
