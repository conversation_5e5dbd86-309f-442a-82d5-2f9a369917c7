# Download and extract portable Java 17
$url = "https://download.java.net/java/GA/jdk17.0.2/dfd4a8d0985749f896bed50d7138ee7f/8/GPL/openjdk-17.0.2_windows-x64_bin.zip"
$output = "java17.zip"
$extractPath = "java17"

Write-Host "Downloading Java 17..."
try {
    [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.SecurityProtocolType]::Tls12
    Invoke-WebRequest -Uri $url -OutFile $output -UseBasicParsing
    Write-Host "Download completed"
    
    # Extract
    Expand-Archive -Path $output -DestinationPath $extractPath -Force
    Write-Host "Extraction completed"
    
    # Find Java directory
    $javaDir = Get-ChildItem -Path $extractPath -Directory | Select-Object -First 1
    $javaHome = $javaDir.FullName
    $javaExe = Join-Path $javaHome "bin\java.exe"
    
    if (Test-Path $javaExe) {
        Write-Host "Java found at: $javaHome"
        & $javaExe -version
        
        # Create environment script
        $envScript = @"
@echo off
set JAVA_HOME=$javaHome
set PATH=$javaHome\bin;%PATH%
echo JAVA_HOME set to: %JAVA_HOME%
"@
        $envScript | Out-File -FilePath "set_java_env.bat" -Encoding ASCII
        Write-Host "Environment script created: set_java_env.bat"
        
        # Clean up
        Remove-Item $output -ErrorAction SilentlyContinue
        
        Write-Host "Java 17 is ready! Run 'set_java_env.bat' then 'gradlew.bat build'"
    } else {
        Write-Host "Error: Java executable not found after extraction"
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}
