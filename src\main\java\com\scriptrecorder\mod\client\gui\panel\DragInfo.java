package com.scriptrecorder.mod.client.gui.panel;

/**
 * Helper class to manage drag state for panels
 */
public class DragInfo {
    private boolean dragging = false;
    private double xOffset = 0.0;
    private double yOffset = 0.0;
    
    /**
     * Set drag information
     */
    public void setDragInfo(boolean dragging, double xOffset, double yOffset) {
        this.dragging = dragging;
        this.xOffset = xOffset;
        this.yOffset = yOffset;
    }
    
    /**
     * Reset drag state
     */
    public void reset() {
        this.dragging = false;
        this.xOffset = 0.0;
        this.yOffset = 0.0;
    }
    
    /**
     * Check if currently dragging
     */
    public boolean isDragging() {
        return dragging;
    }
    
    /**
     * Get X offset for dragging
     */
    public double getXOffset() {
        return xOffset;
    }
    
    /**
     * Get Y offset for dragging
     */
    public double getYOffset() {
        return yOffset;
    }
}
