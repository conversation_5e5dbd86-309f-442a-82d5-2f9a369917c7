package com.scriptrecorder.mod.client.gui.panel;

import net.minecraft.client.gui.GuiGraphics;
import java.awt.Color;
import java.util.ArrayList;
import java.util.List;

/**
 * Manages all panels in the GUI system
 */
public class PanelManager {
    
    private static PanelManager instance;
    private List<Panel> panels = new ArrayList<>();
    private Panel draggedPanel = null;
    
    // Auto-layout settings
    private double nextPanelX = 10;
    private double nextPanelY = 10;
    private double panelSpacing = 10;
    
    // Global settings
    private Color globalAccentColor = new Color(0, 162, 232);
    private boolean blurBackground = true;
    private double backgroundOpacity = 0.3;
    
    private PanelManager() {}
    
    public static PanelManager getInstance() {
        if (instance == null) {
            instance = new PanelManager();
        }
        return instance;
    }
    
    /**
     * Add a panel to the manager
     */
    public void addPanel(Panel panel) {
        // Auto-position if panel is at default position
        if (panel.getX() == 0 && panel.getY() == 0) {
            panel.setX(nextPanelX);
            panel.setY(nextPanelY);
            
            // Update next position
            nextPanelY += panel.getHeight() + panelSpacing;
            
            // Wrap to next column if needed
            if (nextPanelY > 400) {
                nextPanelX += 250;
                nextPanelY = 10;
            }
        }
        
        // Apply global accent color
        panel.setAccentColor(globalAccentColor);
        
        panels.add(panel);
    }
    
    /**
     * Remove a panel from the manager
     */
    public void removePanel(Panel panel) {
        panels.remove(panel);
    }
    
    /**
     * Get panel by title
     */
    public Panel getPanel(String title) {
        for (Panel panel : panels) {
            if (panel.getTitle().equals(title)) {
                return panel;
            }
        }
        return null;
    }
    
    /**
     * Render all panels
     */
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Render background blur/overlay if enabled
        if (blurBackground) {
            renderBackgroundOverlay(guiGraphics);
        }
        
        // Render panels (dragged panel last so it's on top)
        for (Panel panel : panels) {
            if (panel != draggedPanel) {
                panel.render(guiGraphics, mouseX, mouseY, delta);
            }
        }
        
        // Render dragged panel on top
        if (draggedPanel != null) {
            draggedPanel.render(guiGraphics, mouseX, mouseY, delta);
        }
    }
    
    /**
     * Render background overlay
     */
    private void renderBackgroundOverlay(GuiGraphics guiGraphics) {
        // Semi-transparent dark overlay
        int overlayColor = new Color(0, 0, 0, (int)(backgroundOpacity * 255)).getRGB();
        guiGraphics.fill(0, 0, 2000, 1200, overlayColor); // Large enough to cover screen
    }
    
    /**
     * Handle mouse click for all panels
     */
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Check panels in reverse order (top to bottom)
        for (int i = panels.size() - 1; i >= 0; i--) {
            Panel panel = panels.get(i);
            if (panel.mouseClicked(mouseX, mouseY, button)) {
                // Move clicked panel to front
                moveToFront(panel);
                // Set as potentially dragged panel
                draggedPanel = panel;
                return true;
            }
        }
        return false;
    }
    
    /**
     * Handle mouse drag for all panels
     */
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // If we have a dragged panel, prioritize it
        if (draggedPanel != null) {
            if (draggedPanel.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                return true;
            }
        }

        // Otherwise check all panels
        for (Panel panel : panels) {
            if (panel.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
                draggedPanel = panel;
                return true;
            }
        }
        return false;
    }
    
    /**
     * Handle mouse release for all panels
     */
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        draggedPanel = null;
        for (Panel panel : panels) {
            if (panel.mouseReleased(mouseX, mouseY, button)) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Move panel to front (render last)
     */
    public void moveToFront(Panel panel) {
        if (panels.remove(panel)) {
            panels.add(panel);
        }
    }
    
    /**
     * Toggle panel visibility by title
     */
    public void togglePanel(String title) {
        Panel panel = getPanel(title);
        if (panel != null) {
            panel.toggle();
        }
    }
    
    /**
     * Show panel by title
     */
    public void showPanel(String title) {
        Panel panel = getPanel(title);
        if (panel != null) {
            panel.setOpened(true);
            moveToFront(panel);
        }
    }
    
    /**
     * Hide panel by title
     */
    public void hidePanel(String title) {
        Panel panel = getPanel(title);
        if (panel != null) {
            panel.setOpened(false);
        }
    }
    
    /**
     * Close all panels
     */
    public void closeAllPanels() {
        for (Panel panel : panels) {
            panel.setOpened(false);
        }
    }
    
    /**
     * Open all panels
     */
    public void openAllPanels() {
        for (Panel panel : panels) {
            panel.setOpened(true);
        }
    }
    
    /**
     * Reset panel positions to auto-layout
     */
    public void resetPositions() {
        nextPanelX = 10;
        nextPanelY = 10;
        
        for (Panel panel : panels) {
            panel.setX(nextPanelX);
            panel.setY(nextPanelY);
            
            nextPanelY += panel.getHeight() + panelSpacing;
            
            if (nextPanelY > 400) {
                nextPanelX += 250;
                nextPanelY = 10;
            }
        }
    }
    
    /**
     * Apply accent color to all panels
     */
    public void setGlobalAccentColor(Color color) {
        this.globalAccentColor = color;
        for (Panel panel : panels) {
            panel.setAccentColor(color);
        }
    }
    
    /**
     * Get all panels
     */
    public List<Panel> getPanels() {
        return new ArrayList<>(panels);
    }
    
    /**
     * Clear all panels
     */
    public void clear() {
        panels.clear();
        nextPanelX = 10;
        nextPanelY = 10;
    }
    
    // Getters and setters
    public Color getGlobalAccentColor() { return globalAccentColor; }
    
    public boolean isBlurBackground() { return blurBackground; }
    public void setBlurBackground(boolean blurBackground) { this.blurBackground = blurBackground; }
    
    public double getBackgroundOpacity() { return backgroundOpacity; }
    public void setBackgroundOpacity(double backgroundOpacity) { 
        this.backgroundOpacity = Math.max(0.0, Math.min(1.0, backgroundOpacity)); 
    }
    
    public int getPanelCount() { return panels.size(); }
    
    public boolean isEmpty() { return panels.isEmpty(); }
}
