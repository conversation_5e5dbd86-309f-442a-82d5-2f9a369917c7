package com.scriptrecorder.mod.client.serverpinger;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import net.minecraft.client.Minecraft;
import net.minecraft.client.multiplayer.ServerData;
import net.minecraft.client.multiplayer.resolver.ServerAddress;
import net.minecraft.network.chat.Component;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Server pinger that can detect server information and player lists
 */
public class ServerPinger {
    
    private static final ExecutorService EXECUTOR = Executors.newCachedThreadPool();
    private static final int TIMEOUT_MS = 5000;
    
    // Server info cache
    private static final Map<String, ServerInfo> serverCache = new HashMap<>();
    private static final Map<String, Long> lastPingTime = new HashMap<>();
    private static final long CACHE_DURATION = 30000; // 30 seconds
    
    /**
     * Ping a server and get comprehensive information
     */
    public static CompletableFuture<ServerInfo> pingServer(String address, int port) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                String serverKey = address + ":" + port;
                
                // Check cache first
                if (serverCache.containsKey(serverKey)) {
                    Long lastPing = lastPingTime.get(serverKey);
                    if (lastPing != null && (System.currentTimeMillis() - lastPing) < CACHE_DURATION) {
                        return serverCache.get(serverKey);
                    }
                }
                
                ServerInfo info = performPing(address, port);
                
                // Cache the result
                serverCache.put(serverKey, info);
                lastPingTime.put(serverKey, System.currentTimeMillis());
                
                return info;
                
            } catch (Exception e) {
                return new ServerInfo(address, port, -1, "Error: " + e.getMessage(), 
                                    new ArrayList<>(), 0, 0, false);
            }
        }, EXECUTOR);
    }
    
    /**
     * Ping the current server the player is connected to
     */
    public static CompletableFuture<ServerInfo> pingCurrentServer() {
        Minecraft mc = Minecraft.getInstance();
        if (mc.getCurrentServer() != null) {
            ServerData serverData = mc.getCurrentServer();
            ServerAddress serverAddress = ServerAddress.parseString(serverData.ip);
            return pingServer(serverAddress.getHost(), serverAddress.getPort());
        } else {
            // Single player or not connected
            return CompletableFuture.completedFuture(
                new ServerInfo("localhost", 0, 0, "Single Player", 
                             new ArrayList<>(), 1, 1, true));
        }
    }
    
    /**
     * Perform the actual server ping
     */
    private static ServerInfo performPing(String address, int port) throws IOException {
        long startTime = System.currentTimeMillis();
        
        try (Socket socket = new Socket()) {
            socket.setSoTimeout(TIMEOUT_MS);
            socket.connect(new InetSocketAddress(address, port), TIMEOUT_MS);
            
            DataOutputStream out = new DataOutputStream(socket.getOutputStream());
            DataInputStream in = new DataInputStream(socket.getInputStream());
            
            // Send handshake packet
            sendHandshake(out, address, port);
            
            // Send status request
            sendStatusRequest(out);
            
            // Read response
            String response = readStatusResponse(in);
            
            long ping = System.currentTimeMillis() - startTime;
            
            // Parse JSON response
            return parseServerResponse(response, address, port, ping);
            
        } catch (IOException e) {
            throw new IOException("Failed to ping server: " + e.getMessage());
        }
    }
    
    /**
     * Send handshake packet
     */
    private static void sendHandshake(DataOutputStream out, String address, int port) throws IOException {
        ByteArrayOutputStream handshake = new ByteArrayOutputStream();
        DataOutputStream handshakeOut = new DataOutputStream(handshake);
        
        // Packet ID (0x00 for handshake)
        writeVarInt(handshakeOut, 0x00);
        // Protocol version (for 1.20.4)
        writeVarInt(handshakeOut, 765);
        // Server address
        writeString(handshakeOut, address);
        // Server port
        handshakeOut.writeShort(port);
        // Next state (1 for status)
        writeVarInt(handshakeOut, 1);
        
        // Send packet
        writeVarInt(out, handshake.size());
        out.write(handshake.toByteArray());
    }
    
    /**
     * Send status request packet
     */
    private static void sendStatusRequest(DataOutputStream out) throws IOException {
        // Packet length (1 byte)
        writeVarInt(out, 1);
        // Packet ID (0x00 for status request)
        writeVarInt(out, 0x00);
    }
    
    /**
     * Read status response
     */
    private static String readStatusResponse(DataInputStream in) throws IOException {
        // Read packet length
        int length = readVarInt(in);
        
        // Read packet ID
        int packetId = readVarInt(in);
        if (packetId != 0x00) {
            throw new IOException("Invalid packet ID: " + packetId);
        }
        
        // Read JSON string
        return readString(in);
    }
    
    /**
     * Parse server response JSON
     */
    private static ServerInfo parseServerResponse(String json, String address, int port, long ping) {
        try {
            JsonObject root = JsonParser.parseString(json).getAsJsonObject();
            
            // Parse description (MOTD)
            String description = "Unknown";
            if (root.has("description")) {
                JsonElement desc = root.get("description");
                if (desc.isJsonPrimitive()) {
                    description = desc.getAsString();
                } else if (desc.isJsonObject()) {
                    description = desc.getAsJsonObject().get("text").getAsString();
                }
            }
            
            // Parse players
            List<String> playerList = new ArrayList<>();
            int onlinePlayers = 0;
            int maxPlayers = 0;
            
            if (root.has("players")) {
                JsonObject players = root.getAsJsonObject("players");
                onlinePlayers = players.get("online").getAsInt();
                maxPlayers = players.get("max").getAsInt();
                
                // Parse player sample (if available)
                if (players.has("sample")) {
                    JsonArray sample = players.getAsJsonArray("sample");
                    for (JsonElement player : sample) {
                        JsonObject playerObj = player.getAsJsonObject();
                        String playerName = playerObj.get("name").getAsString();
                        playerList.add(playerName);
                    }
                }
            }
            
            return new ServerInfo(address, port, ping, description, playerList, 
                                onlinePlayers, maxPlayers, true);
            
        } catch (Exception e) {
            return new ServerInfo(address, port, ping, "Parse Error: " + e.getMessage(), 
                                new ArrayList<>(), 0, 0, false);
        }
    }
    
    // Utility methods for Minecraft protocol
    
    private static void writeVarInt(DataOutputStream out, int value) throws IOException {
        while ((value & 0x80) != 0) {
            out.writeByte((value & 0x7F) | 0x80);
            value >>>= 7;
        }
        out.writeByte(value & 0x7F);
    }
    
    private static int readVarInt(DataInputStream in) throws IOException {
        int value = 0;
        int position = 0;
        byte currentByte;
        
        while (true) {
            currentByte = in.readByte();
            value |= (currentByte & 0x7F) << position;
            
            if ((currentByte & 0x80) == 0) break;
            
            position += 7;
            if (position >= 32) {
                throw new RuntimeException("VarInt is too big");
            }
        }
        
        return value;
    }
    
    private static void writeString(DataOutputStream out, String string) throws IOException {
        byte[] bytes = string.getBytes(StandardCharsets.UTF_8);
        writeVarInt(out, bytes.length);
        out.write(bytes);
    }
    
    private static String readString(DataInputStream in) throws IOException {
        int length = readVarInt(in);
        byte[] bytes = new byte[length];
        in.readFully(bytes);
        return new String(bytes, StandardCharsets.UTF_8);
    }
    
    /**
     * Clear the server cache
     */
    public static void clearCache() {
        serverCache.clear();
        lastPingTime.clear();
    }
    
    /**
     * Get cached server info if available
     */
    public static ServerInfo getCachedInfo(String address, int port) {
        String serverKey = address + ":" + port;
        return serverCache.get(serverKey);
    }
}
