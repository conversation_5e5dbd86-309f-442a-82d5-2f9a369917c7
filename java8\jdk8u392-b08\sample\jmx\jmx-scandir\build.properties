# IMPORTANT NOTE
#
# If you made a private copy of this project you may have to update the
# nbjdk.home variable at the end of this file.
#  
# To be able to run the test-suite, you will also have to set the
# variable:
#
# libs.junit.classpath=<junit.jar> 
#

main.dir=.

src.dir=${main.dir}/src
test.src.dir=${main.dir}/test

build.dir=build
classes.dir=${build.dir}/classes

dist.dir=dist
jar=${dist.dir}/jmx-scandir.jar
javadoc.dir=${dist.dir}/javadoc

build.test.classes.dir=${build.dir}/test/classes
build.test.results.dir=${build.dir}/test/results

build.sysclasspath=ignore
# E.g.: cp=lib/x.jar:lib/y.jar
cp=
extra.run.cp=

# To be able to run the test-suite, set the following property:
# libs.junit.classpath=...

javac.test.classpath=\
    ${classes.dir}:\
    ${libs.junit.classpath}

main.agent.class=com.sun.jmx.examples.scandir.ScanDirAgent
main.client.class=com.sun.jmx.examples.scandir.ScanDirClient
main.class=${main.client.class}

run.jvmargs=-Djava.util.logging.config.file=logging.properties
common.jvmargs=${run.jvmargs} -Djavax.net.ssl.keyStore=keystore -Djavax.net.ssl.keyStorePassword=password -Djavax.net.ssl.trustStore=truststore -Djavax.net.ssl.trustStorePassword=trustword
client.jvmargs=${common.jvmargs}
agent.jvmargs=${common.jvmargs} -Dcom.sun.management.config.file=src/etc/management.properties -Dscandir.config.file=src/etc/testconfig.xml

client.args=localhost 4545

run.cp=${cp}:${classes.dir}:${extra.run.cp}
run.test.classpath=${run.cp}:${build.test.classes.dir}

debug=true
deprecation=false

# Update this variable if need be to point to the JDK 6 location.
# 
nbjdk.home=${basedir}/../../..
