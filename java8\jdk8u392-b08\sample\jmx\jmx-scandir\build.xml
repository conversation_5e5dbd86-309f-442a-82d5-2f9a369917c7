<?xml version="1.0" encoding="UTF-8"?>

<!--
 Copyright (c) 2006, Oracle and/or its affiliates. All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:

   - Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.

   - Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.

   - Neither the name of Oracle nor the names of its
     contributors may be used to endorse or promote products derived
     from this software without specific prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
 IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->


<!-- 
  This is ant (http://ant.apache.org) build script to build the
  "jmx-scandir" sample. Either this build.xml can be used standalone 
  with "ant" tool or can be opened as a project with NetBeans IDE
  (http://www.netbeans.org).
-->

<project name="jmx-scandir" default="jar" basedir=".">

    <import file="nbproject/jdk.xml"/>
    

    <target name="-prop-init">
        <property file="user.build.properties"/>
        <property file="build.properties"/>
    </target>

    <target name="-init" depends="-prop-init,-jdk-init"/>

    <target name="compile" depends="-init" description="Compile main sources.">
        <mkdir dir="${classes.dir}"/>
        <javac srcdir="${src.dir}" destdir="${classes.dir}" debug="${debug}" deprecation="${deprecation}">
            <classpath path="${cp}"/>
        </javac>
        <copy todir="${classes.dir}">
            <fileset dir="${src.dir}"/>
        </copy>
    </target>

    <target name="jar" depends="compile" description="Build JAR file for main sources.">
        <mkdir dir="${dist.dir}" />
        <jar jarfile="${jar}" compress="true">
            <manifest>
                <attribute name="Main-Class" value="${main.agent.class}"/>
            </manifest>
            <fileset dir="${classes.dir}"/>
        </jar>
    </target>

    <target name="run-client" depends="compile" description="Run client.">
        <fail unless="main.client.class">Must set property 'main.client.class' (e.g. in build.properties)</fail>
        <java classname="${main.client.class}" fork="true" failonerror="true">
            <classpath path="${run.cp}"/>
            <jvmarg line="${client.jvmargs}" />
            <arg line="${client.args}" />
        </java>
    </target>

    <target name="run-agent" depends="compile" description="Run agent.">
        <fail unless="main.agent.class">Must set property 'main.agent.class' (e.g. in build.properties)</fail>
        <java classname="${main.agent.class}" fork="true" failonerror="true">
            <classpath path="${run.cp}"/>
            <jvmarg line="${agent.jvmargs}" />
        </java>
    </target>

    <target name="run" depends="run-agent" description="Run agent." />

    <target name="run-single" depends="-init,compile">
        <fail unless="run.class">Must select one file in the IDE or set run.class</fail>
        <java classname="${run.class}" fork="true" failonerror="true">
            <classpath path="${run.cp}"/>
        </java>
    </target>

    <target name="javadoc" depends="-init" description="Build Javadoc.">
        <mkdir dir="${javadoc.dir}"/>
        <javadoc destdir="${javadoc.dir}">
            <classpath path="${cp}"/>
            <sourcepath>
                <pathelement location="${src.dir}"/>
            </sourcepath>
            <fileset dir="${src.dir}"/>
        </javadoc>
    </target>

    <target name="clean" depends="-init" description="Clean build products.">
        <delete dir="${build.dir}"/>
        <delete file="${jar}"/>
        <delete dir="${dist.dir}"/>
    </target>

    <target name="profile">
        <ant antfile="nbproject/netbeans-targets.xml" target="profile"/>
    </target>

    <!-- JUnit targets -->

    <target name="compile-test" depends="-init,compile">
        <fail unless="libs.junit.classpath">Must set libs.junit.classpath variable to the JUnit classpath in the build.properties file.</fail>
        <mkdir dir="${build.test.classes.dir}"/>
        <javac srcdir="${test.src.dir}" destdir="${build.test.classes.dir}" debug="${debug}" classpath="${javac.test.classpath}"/>
        <copy todir="${build.test.classes.dir}">
            <fileset dir="${test.src.dir}" excludes="**/*.java"/>
        </copy>
    </target>

    <target name="-do-test-run" depends="-init,compile-test">
        <mkdir dir="${build.test.results.dir}"/>
        <junit showoutput="true" fork="true" dir="${basedir}" failureproperty="tests.failed" errorproperty="tests.failed">
            <batchtest todir="${build.test.results.dir}">
                <fileset dir="${test.src.dir}" includes="**/*Test.java"/>
            </batchtest>
            <classpath>
                <path path="${run.test.classpath}"/>
            </classpath>
            <syspropertyset>
                <propertyref prefix="test-sys-prop."/>
                <mapper type="glob" from="test-sys-prop.*" to="*"/>
            </syspropertyset>
            <formatter type="brief" usefile="false"/>
            <formatter type="xml"/>
            <jvmarg line="${run.jvmargs}"/>
        </junit>
        <fail if="tests.failed">Some tests failed; see details above.</fail>
    </target>
    
    <target name="test" depends="-init,compile-test,-do-test-run" description="Run unit tests."/>

    <target name="-do-test-run-single" depends="-init,compile-test">
        <fail unless="test.includes">Must select some files in the IDE or set test.includes</fail>
        <junit showoutput="true" fork="true" dir="${basedir}" failureproperty="tests.failed" errorproperty="tests.failed">
            <batchtest todir="${build.test.results.dir}">
                <fileset dir="${test.src.dir}" includes="${test.includes}"/>
            </batchtest>
            <classpath>
                <path path="${run.test.classpath}"/>
            </classpath>
            <syspropertyset>
                <propertyref prefix="test-sys-prop."/>
                <mapper type="glob" from="test-sys-prop.*" to="*"/>
            </syspropertyset>
            <formatter type="brief" usefile="false"/>
            <formatter type="xml"/>
            <jvmarg line="${run.jvmargs}"/>
        </junit>
        <fail if="tests.failed">Some tests failed; see details above.</fail>
    </target>

    <target name="test-single" depends="-init,compile-test,-do-test-run-single" description="Run single unit test."/>
</project>
