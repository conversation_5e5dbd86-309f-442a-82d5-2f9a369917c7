package com.scriptrecorder.mod.client;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.KeyBindings;
import com.scriptrecorder.mod.client.overlay.KeystrokeOverlay189;
import com.scriptrecorder.mod.client.overlay.TracerESPOverlay189;
import com.scriptrecorder.mod.client.overlay.WaypointRenderer189;
import com.scriptrecorder.mod.client.settings.ModSettings;
import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import net.minecraftforge.common.MinecraftForge;
import org.lwjgl.input.Keyboard;
import org.lwjgl.input.Mouse;

/**
 * Client-side initialization and event handling for 1.8.9
 */
@SideOnly(Side.CLIENT)
public class ScriptRecorderClient189 {

    private static boolean initialized = false;
    private static boolean sprintToggled = false;
    private static boolean crouchToggled = false;
    private final Minecraft mc = Minecraft.getMinecraft();

    // GUI debounce
    private static boolean guiOpenDebounce = false;

    public static void init() {
        if (initialized) return;

        ScriptRecorderMod.LOGGER.info("Initializing Script Recorder client for 1.8.9");

        // Initialize settings system
        ModSettings.initialize();

        // Load saved settings
        ModSettings settings = ModSettings.getInstance();
        sprintToggled = settings.sprintToggled;
        crouchToggled = settings.crouchToggled;

        // Apply saved overlay settings
        KeystrokeOverlay189.setEnabled(settings.keystrokeDisplayEnabled);
        TracerESPOverlay189.loadSettings();

        initialized = true;
    }
    
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) return;

        if (mc.thePlayer == null) return;

        // Update key states and check for custom keybind presses
        updateKeyStates();

        // Check for GUI opening with Shift+\ combination
        boolean backslashPressed = Keyboard.isKeyDown(Keyboard.KEY_BACKSLASH);
        boolean shiftHeld = Keyboard.isKeyDown(Keyboard.KEY_LSHIFT) || Keyboard.isKeyDown(Keyboard.KEY_RSHIFT);

        // Use Shift+\ combination to open GUI
        if (backslashPressed && shiftHeld && !guiOpenDebounce) {
            // TODO: Open GUI when implemented
            ScriptRecorderMod.LOGGER.info("GUI open requested (not implemented yet)");
            guiOpenDebounce = true;
        } else if (!backslashPressed) {
            guiOpenDebounce = false;
        }

        // Check Minecraft-registered keybinds for toggle sprint and crouch
        if (KeyBindings.isToggleSprintPressed()) {
            toggleSprint();
        }

        if (KeyBindings.isToggleCrouchPressed()) {
            toggleCrouch();
        }
    }

    private void updateKeyStates() {
        // Handle toggle sprint - only control when toggle is ON
        if (sprintToggled && mc.thePlayer != null) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindSprint.getKeyCode(), true);
        }

        // Handle toggle crouch - only control when toggle is ON
        if (crouchToggled && mc.thePlayer != null) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindSneak.getKeyCode(), true);
        }
    }

    private void toggleSprint() {
        sprintToggled = !sprintToggled;
        
        // Save the setting
        ModSettings settings = ModSettings.getInstance();
        settings.sprintToggled = sprintToggled;
        settings.save();

        // Send feedback message
        sendChatMessage("Sprint toggle: " + (sprintToggled ? "ON" : "OFF"));

        // If turning off, release the sprint key
        if (!sprintToggled) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindSprint.getKeyCode(), false);
        }
    }

    private void toggleCrouch() {
        crouchToggled = !crouchToggled;
        
        // Save the setting
        ModSettings settings = ModSettings.getInstance();
        settings.crouchToggled = crouchToggled;
        settings.save();

        // Send feedback message
        sendChatMessage("Crouch toggle: " + (crouchToggled ? "ON" : "OFF"));

        // If turning off, release the crouch key
        if (!crouchToggled) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindSneak.getKeyCode(), false);
        }
    }

    private static void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getMinecraft();
        if (mc.thePlayer != null) {
            mc.thePlayer.addChatMessage(new net.minecraft.util.ChatComponentText("[Toggle Sprint Mod] " + message));
        }
    }

    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        // Get key information from Keyboard
        int keyCode = Keyboard.getEventKey();
        boolean pressed = Keyboard.getEventKeyState();

        // Update keystroke overlay
        KeystrokeOverlay189.onKeyInput(keyCode, pressed);

        // TODO: Add script recording functionality
    }

    @SubscribeEvent
    public void onMouseInput(InputEvent.MouseInputEvent event) {
        // Get mouse button and state
        int button = Mouse.getEventButton();
        boolean pressed = Mouse.getEventButtonState();

        // Update keystroke overlay
        if (button >= 0) {
            KeystrokeOverlay189.onMouseInput(button, pressed);
        }

        // TODO: Add script recording functionality
    }
}
