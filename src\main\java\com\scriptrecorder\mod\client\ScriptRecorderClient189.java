package com.scriptrecorder.mod.client;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.KeyBindings;
import com.scriptrecorder.mod.client.gui.SimpleGUI189;
import com.scriptrecorder.mod.client.overlay.KeystrokeOverlay189;
import com.scriptrecorder.mod.client.overlay.TracerESPOverlay189;
import com.scriptrecorder.mod.client.overlay.WaypointRenderer189;
import com.scriptrecorder.mod.client.settings.ModSettings;
import com.scriptrecorder.mod.recording.ScriptAction;
import com.scriptrecorder.mod.recording.RecordedScript;
import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import net.minecraftforge.common.MinecraftForge;
import org.lwjgl.input.Keyboard;
import org.lwjgl.input.Mouse;

/**
 * Client-side initialization and event handling for 1.8.9
 */
@SideOnly(Side.CLIENT)
public class ScriptRecorderClient189 {

    private static boolean initialized = false;
    private static boolean sprintToggled = false;
    private static boolean crouchToggled = false;
    private final Minecraft mc = Minecraft.getMinecraft();

    // GUI debounce
    private static boolean guiOpenDebounce = false;

    public static void init() {
        if (initialized) return;

        ScriptRecorderMod.LOGGER.info("Initializing Script Recorder client for 1.8.9");

        // Initialize settings system
        ModSettings.initialize();

        // Load saved settings
        ModSettings settings = ModSettings.getInstance();
        sprintToggled = settings.sprintToggled;
        crouchToggled = settings.crouchToggled;

        // Apply saved overlay settings
        KeystrokeOverlay189.setEnabled(settings.keystrokeDisplayEnabled);
        TracerESPOverlay189.loadSettings();

        initialized = true;
    }
    
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) return;

        if (mc.thePlayer == null) return;

        // Update key states and check for custom keybind presses
        updateKeyStates();

        // Check for GUI opening with Shift+\ combination
        boolean backslashPressed = Keyboard.isKeyDown(Keyboard.KEY_BACKSLASH);
        boolean shiftHeld = Keyboard.isKeyDown(Keyboard.KEY_LSHIFT) || Keyboard.isKeyDown(Keyboard.KEY_RSHIFT);

        // Use Shift+\ combination to open GUI
        if (backslashPressed && shiftHeld && !guiOpenDebounce) {
            mc.displayGuiScreen(new SimpleGUI189());
            guiOpenDebounce = true;
        } else if (!backslashPressed) {
            guiOpenDebounce = false;
        }

        // Check for script recording hotkeys
        if (isKeyJustPressed(ModSettings.getInstance().keyRecord)) {
            handleRecordKey();
        }

        if (isKeyJustPressed(ModSettings.getInstance().keyStopRecord)) {
            handleStopRecordKey();
        }

        if (isKeyJustPressed(ModSettings.getInstance().keyPlay)) {
            handlePlayKey();
        }

        // Check Minecraft-registered keybinds for toggle sprint and crouch
        if (KeyBindings.isToggleSprintPressed()) {
            toggleSprint();
        }

        if (KeyBindings.isToggleCrouchPressed()) {
            toggleCrouch();
        }
    }

    private void updateKeyStates() {
        // Handle toggle sprint - only control when toggle is ON
        if (sprintToggled && mc.thePlayer != null) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindSprint.getKeyCode(), true);
        }

        // Handle toggle crouch - only control when toggle is ON
        if (crouchToggled && mc.thePlayer != null) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindSneak.getKeyCode(), true);
        }
    }

    private void toggleSprint() {
        sprintToggled = !sprintToggled;
        
        // Save the setting
        ModSettings settings = ModSettings.getInstance();
        settings.sprintToggled = sprintToggled;
        settings.save();

        // Send feedback message
        sendChatMessage("Sprint toggle: " + (sprintToggled ? "ON" : "OFF"));

        // If turning off, release the sprint key
        if (!sprintToggled) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindSprint.getKeyCode(), false);
        }
    }

    private void toggleCrouch() {
        crouchToggled = !crouchToggled;
        
        // Save the setting
        ModSettings settings = ModSettings.getInstance();
        settings.crouchToggled = crouchToggled;
        settings.save();

        // Send feedback message
        sendChatMessage("Crouch toggle: " + (crouchToggled ? "ON" : "OFF"));

        // If turning off, release the crouch key
        if (!crouchToggled) {
            KeyBinding.setKeyBindState(mc.gameSettings.keyBindSneak.getKeyCode(), false);
        }
    }

    private static void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getMinecraft();
        if (mc.thePlayer != null) {
            mc.thePlayer.addChatMessage(new net.minecraft.util.ChatComponentText("[Toggle Sprint Mod] " + message));
        }
    }

    // Key state tracking for debouncing
    private static boolean[] keyStates = new boolean[256];

    private boolean isKeyJustPressed(int keyCode) {
        // Bounds check for LWJGL 2.x keyboard
        if (keyCode < 0 || keyCode >= 256) {
            ScriptRecorderMod.LOGGER.warn("Invalid key code: " + keyCode + " (out of bounds 0-255)");
            return false;
        }

        boolean currentState = Keyboard.isKeyDown(keyCode);
        boolean wasPressed = keyStates[keyCode];
        keyStates[keyCode] = currentState;
        return currentState && !wasPressed;
    }

    private void handleRecordKey() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            sendChatMessage("Already recording a script!");
            return;
        }

        String scriptName = "script_" + System.currentTimeMillis();
        if (ScriptRecorderMod.scriptManager.startRecording(scriptName)) {
            sendChatMessage("Started recording script: " + scriptName);
        } else {
            sendChatMessage("Failed to start recording!");
        }
    }

    private void handleStopRecordKey() {
        if (!ScriptRecorderMod.scriptManager.isRecording()) {
            sendChatMessage("Not currently recording!");
            return;
        }

        RecordedScript script = ScriptRecorderMod.scriptManager.stopRecording();
        if (script != null) {
            sendChatMessage("Stopped recording. Script has " + script.getTotalActions() + " actions.");
        } else {
            sendChatMessage("Failed to stop recording!");
        }
    }

    private void handlePlayKey() {
        if (ScriptRecorderMod.scriptManager.isPlaying()) {
            sendChatMessage("Already playing a script!");
            return;
        }

        RecordedScript lastScript = ScriptRecorderMod.scriptManager.getLastRecordedScript();
        if (lastScript != null) {
            if (ScriptRecorderMod.scriptManager.startPlayback(lastScript)) {
                sendChatMessage("Started playing script with " + lastScript.getTotalActions() + " actions.");
            } else {
                sendChatMessage("Failed to start playback!");
            }
        } else {
            sendChatMessage("No script to play! Record a script first.");
        }
    }

    @SubscribeEvent
    public void onKeyInput(InputEvent.KeyInputEvent event) {
        // Get key information from Keyboard
        int keyCode = Keyboard.getEventKey();
        boolean pressed = Keyboard.getEventKeyState();

        // Update keystroke overlay
        KeystrokeOverlay189.onKeyInput(keyCode, pressed);

        // Record key events if recording
        if (ScriptRecorderMod.scriptManager.isRecording() && mc.currentScreen == null) {
            String keyName = getKeyName(keyCode);
            if (keyName != null) {
                double currentTime = ScriptRecorderMod.scriptManager.getRecordingTime();

                if (pressed) {
                    ScriptAction action = ScriptAction.keyPress(currentTime, keyName, keyCode);
                    ScriptRecorderMod.scriptManager.recordAction(action);
                } else {
                    ScriptAction action = ScriptAction.keyRelease(currentTime, keyName, keyCode);
                    ScriptRecorderMod.scriptManager.recordAction(action);
                }
            }
        }
    }

    @SubscribeEvent
    public void onMouseInput(InputEvent.MouseInputEvent event) {
        // Get mouse button and state
        int button = Mouse.getEventButton();
        boolean pressed = Mouse.getEventButtonState();

        // Update keystroke overlay
        if (button >= 0) {
            KeystrokeOverlay189.onMouseInput(button, pressed);
        }

        // Record mouse events if recording
        if (ScriptRecorderMod.scriptManager.isRecording() && mc.currentScreen == null && button >= 0) {
            int mouseX = Mouse.getEventX();
            int mouseY = Mouse.getEventY();

            String buttonName = getMouseButtonName(button);
            if (buttonName != null) {
                double currentTime = ScriptRecorderMod.scriptManager.getRecordingTime();
                ScriptAction action = ScriptAction.mouseClick(currentTime, mouseX, mouseY, buttonName, pressed);
                ScriptRecorderMod.scriptManager.recordAction(action);
            }
        }
    }

    private static String getKeyName(int keyCode) {
        // Convert LWJGL key codes to readable names (1.8.9)
        switch (keyCode) {
            case Keyboard.KEY_SPACE: return "space";
            case Keyboard.KEY_RETURN: return "enter";
            case Keyboard.KEY_TAB: return "tab";
            case Keyboard.KEY_BACK: return "backspace";
            case Keyboard.KEY_DELETE: return "delete";
            case Keyboard.KEY_LEFT: return "left";
            case Keyboard.KEY_RIGHT: return "right";
            case Keyboard.KEY_UP: return "up";
            case Keyboard.KEY_DOWN: return "down";
            case Keyboard.KEY_LSHIFT: return "left_shift";
            case Keyboard.KEY_RSHIFT: return "right_shift";
            case Keyboard.KEY_LCONTROL: return "left_ctrl";
            case Keyboard.KEY_RCONTROL: return "right_ctrl";
            case Keyboard.KEY_LMENU: return "left_alt";
            case Keyboard.KEY_RMENU: return "right_alt";
            case Keyboard.KEY_ESCAPE: return "escape";
            default:
                // For letter keys
                if (keyCode >= Keyboard.KEY_A && keyCode <= Keyboard.KEY_Z) {
                    return String.valueOf((char) ('a' + (keyCode - Keyboard.KEY_A)));
                }
                // For number keys
                if (keyCode >= Keyboard.KEY_0 && keyCode <= Keyboard.KEY_9) {
                    return String.valueOf((char) ('0' + (keyCode - Keyboard.KEY_0)));
                }
                // For function keys
                if (keyCode >= Keyboard.KEY_F1 && keyCode <= Keyboard.KEY_F12) {
                    return "f" + (keyCode - Keyboard.KEY_F1 + 1);
                }
                return "key_" + keyCode;
        }
    }

    private static String getMouseButtonName(int button) {
        switch (button) {
            case 0: return "left";
            case 1: return "right";
            case 2: return "middle";
            default: return "button_" + button;
        }
    }
}
