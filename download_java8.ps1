# Download and extract Java 8 for 1.8.9 mod development
$java8Dir = "java8"
$java8Url = "https://github.com/adoptium/temurin8-binaries/releases/download/jdk8u392-b08/OpenJDK8U-jdk_x64_windows_hotspot_8u392b08.zip"
$java8Zip = "java8.zip"

Write-Host "Downloading Java 8..."
if (!(Test-Path $java8Dir)) {
    New-Item -ItemType Directory -Path $java8Dir
}

# Download Java 8
Invoke-WebRequest -Uri $java8Url -OutFile $java8Zip

# Extract Java 8
Write-Host "Extracting Java 8..."
Expand-Archive -Path $java8Zip -DestinationPath $java8Dir -Force

# Clean up
Remove-Item $java8Zip

Write-Host "Java 8 downloaded and extracted to $java8Dir"
Write-Host "Set JAVA_HOME to: $((Get-Location).Path)\$java8Dir\jdk8u392-b08"
