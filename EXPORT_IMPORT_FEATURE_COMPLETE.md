# Export/Import Script Feature - COMPLETE

## Overview
Successfully implemented export/import functionality for the Minecraft Script Recorder mod, allowing users to save scripts with custom names and load them later for reuse.

## New Features Added

### 1. Export Functionality
- **Export Button**: Added to both main GUI and simple GUI
- **Custom Naming**: Users can specify custom names for their scripts (e.g., "farmscript")
- **Overwrite Warning**: Shows warning if script name already exists
- **Detailed Feedback**: Shows script info (actions count, duration) when exporting

### 2. Import Functionality
- **Import Button**: Added to both main GUI and simple GUI
- **Script Validation**: Checks if script exists before attempting import
- **Available Scripts List**: Shows available scripts if import fails
- **Detailed Feedback**: Shows script info when successfully imported

### 3. Enhanced Script Management
- **Script Name Input**: Text field for entering custom script names
- **Button States**: Export/import buttons are properly enabled/disabled based on context
- **Error Handling**: Comprehensive error messages and validation

## GUI Changes

### Main Script Recorder Screen (`ScriptRecorderScreen.java`)
- Added `scriptNameBox` for custom script naming
- Added `exportButton` and `importButton`
- Added "Script Name:" label
- Enhanced button state management
- Added export/import methods with chat feedback

### Simple Script Recorder Screen (`SimpleScriptRecorderScreen.java`)
- Added `exportButton` and `importButton` 
- Enhanced existing `scriptNameBox` functionality
- Added export/import methods with chat feedback
- Updated button state management

## Backend Enhancements

### ScriptManager (`ScriptManager.java`)
- Added `exportScript()` method for custom-named exports
- Added `importScript()` method for enhanced imports
- Added `scriptExists()` method for validation
- Enhanced error handling and logging

## Usage Instructions

### Exporting a Script
1. Record a script or load an existing one
2. Enter a custom name in the "Script Name" field (e.g., "farmscript")
3. Click the "Export" button
4. Script is saved as `farmscript.json` in the scripts directory
5. Chat message confirms export with script details

### Importing a Script
1. Enter the script name in the "Script Name" field (e.g., "farmscript")
2. Click the "Import" button
3. Script is loaded and ready for playback
4. Chat message confirms import with script details

### Error Handling
- **No Script to Export**: Shows error if trying to export without a recorded/loaded script
- **Empty Name**: Shows error if script name field is empty
- **Script Not Found**: Shows available scripts list if import fails
- **File Corruption**: Shows error if script file is corrupted

## File Structure
Scripts are stored in the `scriptrecorder` folder within the Minecraft directory:
```
.minecraft/scriptrecorder/
├── farmscript.json
├── miningscript.json
├── buildscript.json
└── ...
```

## Chat Notifications
All export/import operations provide clear chat feedback:
- **Success**: Green messages with script details
- **Warnings**: Yellow messages for overwrite warnings
- **Errors**: Red messages with helpful information

## Button States
- **Export Button**: Only active when a script is available and not recording/playing
- **Import Button**: Always active when not recording/playing
- **Proper Disabling**: Buttons disabled during recording/playback operations

## Version Information
- **Mod Version**: 3.2.0
- **Minecraft Version**: 1.20.4
- **Build Status**: ✅ Successfully compiled
- **JAR Location**: `build/libs/minecraft-script-recorder-1.20.4-3.2.0.jar`

## Testing Recommendations
1. Record a simple script
2. Export it with name "testscript"
3. Clear current script (record new empty one)
4. Import "testscript" 
5. Verify script plays correctly
6. Test error cases (empty names, non-existent scripts)

## Future Enhancements
- File browser for selecting export/import locations
- Script preview before import
- Batch export/import operations
- Script categories/folders
- Cloud sync capabilities

The export/import functionality is now fully implemented and ready for use!
