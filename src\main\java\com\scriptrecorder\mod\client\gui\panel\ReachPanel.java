package com.scriptrecorder.mod.client.gui.panel;

import com.scriptrecorder.mod.client.reach.Reach;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.network.chat.Component;
import java.awt.Color;

/**
 * Panel for Reach configuration and control
 */
public class ReachPanel extends Panel {
    
    // UI Components
    private Button enableButton;
    private Button modifyBlockReachButton;
    private Button serverTypeButton;
    private Button safeValuesButton;
    private EditBox attackReachBox;
    private EditBox blockReachBox;
    
    // Current server type
    private Reach.ServerType currentServerType = Reach.ServerType.VANILLA;
    
    // Colors
    private static final Color ENABLED_COLOR = new Color(50, 220, 50);
    private static final Color DISABLED_COLOR = new Color(220, 50, 50);
    private static final Color ATTACK_COLOR = new Color(255, 100, 100);
    private static final Color BLOCK_COLOR = new Color(100, 150, 255);
    private static final Color ADVANTAGE_COLOR = new Color(255, 215, 0);
    
    public ReachPanel() {
        super("Reach", 350, 10, 300, 180);
        // Load settings from ModSettings via Reach instance
        Reach.getInstance().loadSettings();
        // Load server type from settings
        loadServerType();
        initializeComponents();
    }

    private void loadServerType() {
        String savedServerType = com.scriptrecorder.mod.client.settings.ModSettings.getReachServerType();
        try {
            currentServerType = Reach.ServerType.valueOf(savedServerType);
        } catch (IllegalArgumentException e) {
            currentServerType = Reach.ServerType.VANILLA; // Default fallback
        }
    }
    
    private void initializeComponents() {
        clearWidgets();
        
        // Calculate positions relative to panel
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Main enable button
        enableButton = Button.builder(
            getEnableButtonText(),
            button -> toggleEnabled())
            .bounds(contentX, contentY, 100, 24)
            .build();
        addWidget(enableButton);
        
        // Server type button
        serverTypeButton = Button.builder(
            getServerTypeButtonText(),
            button -> cycleServerType())
            .bounds(contentX + 110, contentY, 80, 24)
            .build();
        addWidget(serverTypeButton);
        
        // Safe values button
        safeValuesButton = Button.builder(
            Component.literal("Safe Values"),
            button -> applySafeValues())
            .bounds(contentX + 200, contentY, 80, 24)
            .build();
        addWidget(safeValuesButton);
        
        // Attack reach section
        attackReachBox = new EditBox(Minecraft.getInstance().font,
                                    contentX + 100, contentY + 35, 60, 20,
                                    Component.literal("Attack Reach"));
        double currentAttackReach = Reach.getInstance().getAttackReach();
        attackReachBox.setValue(String.valueOf(currentAttackReach));
        lastAttackValue = String.valueOf(currentAttackReach);
        attackReachBox.setMaxLength(4);
        attackReachBox.setResponder(value -> {
            try {
                if (!value.trim().isEmpty()) {
                    double reachValue = Double.parseDouble(value.trim());
                    if (reachValue >= Reach.DEFAULT_ATTACK_REACH && reachValue <= Reach.MAX_SAFE_REACH) {
                        Reach.getInstance().setAttackReach(reachValue);
                        System.out.println("Attack reach updated to: " + reachValue);
                    }
                }
            } catch (NumberFormatException e) {
                // Invalid input, ignore
            }
        });
        addWidget(attackReachBox);
        
        // Block reach section
        modifyBlockReachButton = Button.builder(
            getModifyBlockReachButtonText(),
            button -> toggleModifyBlockReach())
            .bounds(contentX, contentY + 65, 90, 20)
            .build();
        addWidget(modifyBlockReachButton);
        
        blockReachBox = new EditBox(Minecraft.getInstance().font,
                                   contentX + 100, contentY + 65, 60, 20,
                                   Component.literal("Block Reach"));
        double currentBlockReach = Reach.getInstance().getBlockReach();
        blockReachBox.setValue(String.valueOf(currentBlockReach));
        lastBlockValue = String.valueOf(currentBlockReach);
        blockReachBox.setMaxLength(4);
        blockReachBox.setResponder(value -> {
            try {
                if (!value.trim().isEmpty()) {
                    double reachValue = Double.parseDouble(value.trim());
                    if (reachValue >= Reach.DEFAULT_BLOCK_REACH && reachValue <= Reach.MAX_SAFE_REACH) {
                        Reach.getInstance().setBlockReach(reachValue);
                        System.out.println("Block reach updated to: " + reachValue);
                    }
                }
            } catch (NumberFormatException e) {
                // Invalid input, ignore
            }
        });
        addWidget(blockReachBox);
    }
    
    @Override
    protected void renderContent(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Always update component positions
        updateComponentPositions();
        
        // Draw labels and status
        int textX = (int)(x + 10);
        int textY = (int)(y + titleBarHeight + 100);
        
        Minecraft mc = Minecraft.getInstance();
        Reach reach = Reach.getInstance();
        
        // Status information
        String status = reach.isEnabled() ? "ACTIVE" : "INACTIVE";
        Color statusColor = reach.isEnabled() ? ENABLED_COLOR : DISABLED_COLOR;
        guiGraphics.drawString(mc.font, "Status: " + status, 
                             textX, textY, statusColor.getRGB());
        
        // Current effective reach values
        if (reach.isEnabled()) {
            String attackInfo = String.format("Attack: %.1f", reach.getEffectiveAttackReach());
            guiGraphics.drawString(mc.font, attackInfo, 
                                 textX, textY + 12, ATTACK_COLOR.getRGB());
            
            if (reach.isModifyBlockReach()) {
                String blockInfo = String.format("Block: %.1f", reach.getEffectiveBlockReach());
                guiGraphics.drawString(mc.font, blockInfo, 
                                     textX + 100, textY + 12, BLOCK_COLOR.getRGB());
            }
            
            // Show advantage
            String advantage = "Advantage: " + reach.getFormattedAdvantage();
            guiGraphics.drawString(mc.font, advantage, 
                                 textX, textY + 24, ADVANTAGE_COLOR.getRGB());
        }
        
        // Labels for input boxes
        guiGraphics.drawString(mc.font, "Attack Reach:", textX, textY + 40, Color.WHITE.getRGB());
        guiGraphics.drawString(mc.font, "Block Reach:", textX, textY + 70, Color.WHITE.getRGB());
        
        // Help text
        guiGraphics.drawString(mc.font, "Default: Attack 3.0, Block 4.5", 
                             textX, textY + 85, Color.GRAY.getRGB());
        guiGraphics.drawString(mc.font, "Safe values vary by server type", 
                             textX, textY + 97, Color.GRAY.getRGB());
        
        // Server-specific recommendations
        String serverInfo = String.format("%s: A%.1f B%.1f", 
            currentServerType.getName(),
            Reach.getSafeAttackReach(currentServerType),
            Reach.getSafeBlockReach(currentServerType));
        guiGraphics.drawString(mc.font, serverInfo, 
                             textX, textY + 109, Color.LIGHT_GRAY.getRGB());
    }
    
    @Override
    protected void updateComponentPositions() {
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Update all component positions
        if (enableButton != null) {
            enableButton.setX(contentX);
            enableButton.setY(contentY);
        }
        if (serverTypeButton != null) {
            serverTypeButton.setX(contentX + 110);
            serverTypeButton.setY(contentY);
        }
        if (safeValuesButton != null) {
            safeValuesButton.setX(contentX + 200);
            safeValuesButton.setY(contentY);
        }
        if (attackReachBox != null) {
            attackReachBox.setX(contentX + 100);
            attackReachBox.setY(contentY + 35);
        }
        if (modifyBlockReachButton != null) {
            modifyBlockReachButton.setX(contentX);
            modifyBlockReachButton.setY(contentY + 65);
        }
        if (blockReachBox != null) {
            blockReachBox.setX(contentX + 100);
            blockReachBox.setY(contentY + 65);
        }
    }
    
    // Button action methods
    private void toggleEnabled() {
        Reach reach = Reach.getInstance();
        boolean newState = !reach.isEnabled();
        reach.setEnabled(newState);
        
        if (newState) {
            reach.reset();
            sendChatMessage("Reach enabled - Attack: " + reach.getFormattedAttackReach());
        } else {
            sendChatMessage("Reach disabled");
        }
        
        updateButtonStates();
    }
    
    private void toggleModifyBlockReach() {
        Reach reach = Reach.getInstance();
        boolean newState = !reach.isModifyBlockReach();
        reach.setModifyBlockReach(newState);
        sendChatMessage("Block reach modification: " + (newState ? "ON" : "OFF"));
        updateButtonStates();
    }
    
    private void cycleServerType() {
        Reach.ServerType[] types = Reach.ServerType.values();
        int currentIndex = currentServerType.ordinal();
        int nextIndex = (currentIndex + 1) % types.length;
        currentServerType = types[nextIndex];

        // Save server type to settings
        com.scriptrecorder.mod.client.settings.ModSettings.setReachServerType(currentServerType.name());

        sendChatMessage("Server type: " + currentServerType.getName());
        updateButtonStates();
    }
    
    private void applySafeValues() {
        Reach reach = Reach.getInstance();
        double safeAttack = Reach.getSafeAttackReach(currentServerType);
        double safeBlock = Reach.getSafeBlockReach(currentServerType);
        
        reach.setAttackReach(safeAttack);
        reach.setBlockReach(safeBlock);
        
        // Update input boxes
        if (attackReachBox != null) {
            attackReachBox.setValue(String.valueOf(safeAttack));
        }
        if (blockReachBox != null) {
            blockReachBox.setValue(String.valueOf(safeBlock));
        }
        
        sendChatMessage(String.format("Applied %s safe values: A%.1f B%.1f", 
            currentServerType.getName(), safeAttack, safeBlock));
        updateButtonStates();
    }
    
    private void updateButtonStates() {
        if (enableButton != null) enableButton.setMessage(getEnableButtonText());
        if (modifyBlockReachButton != null) modifyBlockReachButton.setMessage(getModifyBlockReachButtonText());
        if (serverTypeButton != null) serverTypeButton.setMessage(getServerTypeButtonText());
    }
    
    // Button text methods
    private Component getEnableButtonText() {
        boolean enabled = Reach.getInstance().isEnabled();
        return Component.literal("Reach: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getModifyBlockReachButtonText() {
        boolean enabled = Reach.getInstance().isModifyBlockReach();
        return Component.literal("Block: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getServerTypeButtonText() {
        return Component.literal(currentServerType.getName());
    }
    
    private void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null) {
            mc.player.displayClientMessage(Component.literal("[Reach] " + message), false);
        }
    }
    
    /**
     * Update the panel (called every frame)
     */
    public void update() {
        // Always update component positions first
        updateComponentPositions();
        // Check for reach value changes periodically
        checkAndUpdateReachValues();
        // Update button states
        updateButtonStates();
    }

    private long lastUpdateCheck = 0;
    private String lastAttackValue = "";
    private String lastBlockValue = "";

    /**
     * Check if EditBox values have changed and update reach accordingly
     */
    private void checkAndUpdateReachValues() {
        long currentTime = System.currentTimeMillis();
        // Only check every 500ms to avoid performance issues
        if (currentTime - lastUpdateCheck < 500) {
            return;
        }
        lastUpdateCheck = currentTime;

        // Check attack reach
        if (attackReachBox != null) {
            String currentValue = attackReachBox.getValue();
            if (!currentValue.equals(lastAttackValue)) {
                lastAttackValue = currentValue;
                updateAttackReach(currentValue);
            }
        }

        // Check block reach
        if (blockReachBox != null) {
            String currentValue = blockReachBox.getValue();
            if (!currentValue.equals(lastBlockValue)) {
                lastBlockValue = currentValue;
                updateBlockReach(currentValue);
            }
        }
    }

    private void updateAttackReach(String value) {
        try {
            if (!value.trim().isEmpty()) {
                double reachValue = Double.parseDouble(value.trim());
                if (reachValue >= Reach.DEFAULT_ATTACK_REACH && reachValue <= Reach.MAX_SAFE_REACH) {
                    Reach.getInstance().setAttackReach(reachValue);
                    System.out.println("Attack reach updated to: " + reachValue);
                }
            }
        } catch (NumberFormatException e) {
            // Invalid input, ignore
        }
    }

    private void updateBlockReach(String value) {
        try {
            if (!value.trim().isEmpty()) {
                double reachValue = Double.parseDouble(value.trim());
                if (reachValue >= Reach.DEFAULT_BLOCK_REACH && reachValue <= Reach.MAX_SAFE_REACH) {
                    Reach.getInstance().setBlockReach(reachValue);
                    System.out.println("Block reach updated to: " + reachValue);
                }
            }
        } catch (NumberFormatException e) {
            // Invalid input, ignore
        }
    }
    
    private void updateReachSettings() {
        Reach reach = Reach.getInstance();
        boolean settingsChanged = false;

        // Update attack reach setting
        if (attackReachBox != null) {
            try {
                String value = attackReachBox.getValue().trim();
                if (!value.isEmpty()) {
                    double reachValue = Double.parseDouble(value);
                    if (reachValue >= Reach.DEFAULT_ATTACK_REACH && reachValue <= Reach.MAX_SAFE_REACH) {
                        double currentValue = reach.getAttackReach();
                        if (Math.abs(currentValue - reachValue) > 0.01) { // Only update if changed
                            reach.setAttackReach(reachValue);
                            settingsChanged = true;
                        }
                    }
                }
            } catch (NumberFormatException e) {
                attackReachBox.setValue(String.valueOf(reach.getAttackReach()));
            }
        }

        // Update block reach setting
        if (blockReachBox != null) {
            try {
                String value = blockReachBox.getValue().trim();
                if (!value.isEmpty()) {
                    double reachValue = Double.parseDouble(value);
                    if (reachValue >= Reach.DEFAULT_BLOCK_REACH && reachValue <= Reach.MAX_SAFE_REACH) {
                        double currentValue = reach.getBlockReach();
                        if (Math.abs(currentValue - reachValue) > 0.01) { // Only update if changed
                            reach.setBlockReach(reachValue);
                            settingsChanged = true;
                        }
                    }
                }
            } catch (NumberFormatException e) {
                blockReachBox.setValue(String.valueOf(reach.getBlockReach()));
            }
        }

        // Force save if settings changed
        if (settingsChanged) {
            com.scriptrecorder.mod.client.settings.ModSettings.getInstance().save();
        }
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Save reach settings when clicking away from EditBoxes
        saveCurrentReachSettings();
        return super.mouseClicked(mouseX, mouseY, button);
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Handle Enter key to save values immediately
        if (keyCode == 257) { // Enter key
            if (attackReachBox != null && attackReachBox.isFocused()) {
                updateAttackReach(attackReachBox.getValue());
            }
            if (blockReachBox != null && blockReachBox.isFocused()) {
                updateBlockReach(blockReachBox.getValue());
            }
        }
        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    /**
     * Save current reach settings immediately
     */
    private void saveCurrentReachSettings() {
        updateReachSettings();
    }


}
