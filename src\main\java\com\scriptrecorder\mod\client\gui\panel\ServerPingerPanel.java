package com.scriptrecorder.mod.client.gui.panel;

import com.scriptrecorder.mod.client.serverpinger.ServerInfo;
import com.scriptrecorder.mod.client.serverpinger.ServerPinger;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.network.chat.Component;
import java.awt.Color;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Panel for server pinging and player list monitoring
 */
public class ServerPingerPanel extends Panel {
    
    // UI Components
    private EditBox serverAddressBox;
    private Button pingButton;
    private Button autoPingButton;
    private Button clearCacheButton;
    private Button pingCurrentButton;
    
    // State
    private ServerInfo currentServerInfo;
    private boolean autoPingEnabled = false;
    private long lastAutoPing = 0;
    private final long AUTO_PING_INTERVAL = 10000; // 10 seconds
    private boolean isPinging = false;
    
    // Player list scroll
    private int playerListScroll = 0;
    private final int maxVisiblePlayers = 8;
    
    // Colors
    private static final Color ONLINE_COLOR = new Color(50, 220, 50);
    private static final Color OFFLINE_COLOR = new Color(220, 50, 50);
    private static final Color VANISHED_COLOR = new Color(255, 165, 0); // Orange for vanished players
    
    public ServerPingerPanel() {
        super("Server Pinger", 650, 10, 350, 280);
        initializeComponents();
    }
    
    private void initializeComponents() {
        clearWidgets();
        
        // Calculate positions relative to panel
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Server address input
        serverAddressBox = new EditBox(Minecraft.getInstance().font, 
                                      contentX, contentY, 200, 20, 
                                      Component.literal("Server Address"));
        serverAddressBox.setValue("hypixel.net");
        serverAddressBox.setMaxLength(100);
        addWidget(serverAddressBox);
        
        // Ping button
        pingButton = Button.builder(
            Component.literal("Ping"),
            button -> pingServer())
            .bounds(contentX + 210, contentY, 50, 20)
            .build();
        addWidget(pingButton);
        
        // Auto ping toggle
        autoPingButton = Button.builder(
            getAutoPingButtonText(),
            button -> toggleAutoPing())
            .bounds(contentX + 270, contentY, 60, 20)
            .build();
        addWidget(autoPingButton);
        
        // Ping current server button
        pingCurrentButton = Button.builder(
            Component.literal("Current"),
            button -> pingCurrentServer())
            .bounds(contentX, contentY + 30, 80, 20)
            .build();
        addWidget(pingCurrentButton);
        
        // Clear cache button
        clearCacheButton = Button.builder(
            Component.literal("Clear Cache"),
            button -> clearCache())
            .bounds(contentX + 90, contentY + 30, 80, 20)
            .build();
        addWidget(clearCacheButton);
    }
    
    @Override
    protected void renderContent(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Update component positions when panel moves
        updateComponentPositions();
        
        // Handle auto ping
        if (autoPingEnabled && !isPinging) {
            long currentTime = System.currentTimeMillis();
            if (currentTime - lastAutoPing > AUTO_PING_INTERVAL) {
                pingServer();
                lastAutoPing = currentTime;
            }
        }
        
        // Draw server information
        int textX = (int)(x + 10);
        int textY = (int)(y + titleBarHeight + 70);
        
        if (currentServerInfo != null) {
            drawServerInfo(guiGraphics, textX, textY);
            drawPlayerList(guiGraphics, textX, textY + 80);
        } else {
            guiGraphics.drawString(Minecraft.getInstance().font, 
                                 "No server information available", 
                                 textX, textY, Color.GRAY.getRGB());
            guiGraphics.drawString(Minecraft.getInstance().font, 
                                 "Enter a server address and click 'Ping'", 
                                 textX, textY + 12, Color.GRAY.getRGB());
        }
        
        // Draw status
        if (isPinging) {
            guiGraphics.drawString(Minecraft.getInstance().font, 
                                 "Pinging...", 
                                 textX + 250, textY, Color.YELLOW.getRGB());
        }
        
        // Draw auto ping countdown
        if (autoPingEnabled && !isPinging) {
            long timeLeft = AUTO_PING_INTERVAL - (System.currentTimeMillis() - lastAutoPing);
            if (timeLeft > 0) {
                String countdown = "Next ping in: " + (timeLeft / 1000) + "s";
                guiGraphics.drawString(Minecraft.getInstance().font, countdown, 
                                     textX + 180, textY + 50, Color.LIGHT_GRAY.getRGB());
            }
        }
    }
    
    private void drawServerInfo(GuiGraphics guiGraphics, int x, int y) {
        Minecraft mc = Minecraft.getInstance();
        
        // Server address
        guiGraphics.drawString(mc.font, "Server: " + currentServerInfo.getFormattedAddress(), 
                             x, y, Color.WHITE.getRGB());
        
        // Ping with color coding
        String pingText = "Ping: " + currentServerInfo.getFormattedPing();
        guiGraphics.drawString(mc.font, pingText, 
                             x + 180, y, currentServerInfo.getPingColor());
        
        // Player count with color coding
        String playerText = "Players: " + currentServerInfo.getFormattedPlayerCount();
        guiGraphics.drawString(mc.font, playerText, 
                             x, y + 12, currentServerInfo.getPlayerCountColor());
        
        // Detected players (including vanished)
        int totalDetected = currentServerInfo.getTotalDetectedPlayers();
        if (totalDetected > currentServerInfo.getOnlinePlayers()) {
            String detectedText = "Detected: " + totalDetected;
            guiGraphics.drawString(mc.font, detectedText, 
                                 x + 120, y + 12, VANISHED_COLOR.getRGB());
        }
        
        // Server description (truncated)
        String description = currentServerInfo.getCleanDescription();
        if (description.length() > 40) {
            description = description.substring(0, 37) + "...";
        }
        guiGraphics.drawString(mc.font, "MOTD: " + description, 
                             x, y + 24, Color.LIGHT_GRAY.getRGB());
        
        // Status indicators
        if (currentServerInfo.hasVanishedPlayers()) {
            guiGraphics.drawString(mc.font, "⚠ Vanished players detected", 
                                 x, y + 36, VANISHED_COLOR.getRGB());
        }
        
        if (currentServerInfo.isFull()) {
            guiGraphics.drawString(mc.font, "⚠ Server is full", 
                                 x + 200, y + 36, OFFLINE_COLOR.getRGB());
        }
    }
    
    private void drawPlayerList(GuiGraphics guiGraphics, int x, int y) {
        Minecraft mc = Minecraft.getInstance();
        
        guiGraphics.drawString(mc.font, "Player List:", x, y, Color.WHITE.getRGB());
        
        List<String> allPlayers = currentServerInfo.getAllPlayers();
        List<String> officialPlayers = currentServerInfo.getPlayerList();
        List<String> vanishedPlayers = currentServerInfo.getVanishedPlayers();
        
        if (allPlayers.isEmpty()) {
            guiGraphics.drawString(mc.font, "No players visible", 
                                 x + 10, y + 12, Color.GRAY.getRGB());
            return;
        }
        
        // Draw scroll info if needed
        if (allPlayers.size() > maxVisiblePlayers) {
            String scrollInfo = String.format("(%d-%d of %d players)", 
                                            playerListScroll + 1, 
                                            Math.min(playerListScroll + maxVisiblePlayers, allPlayers.size()),
                                            allPlayers.size());
            guiGraphics.drawString(mc.font, scrollInfo, x + 100, y, Color.GRAY.getRGB());
        }
        
        // Draw player names
        int startIndex = Math.max(0, playerListScroll);
        int endIndex = Math.min(allPlayers.size(), startIndex + maxVisiblePlayers);
        
        for (int i = startIndex; i < endIndex; i++) {
            String playerName = allPlayers.get(i);
            int playerY = y + 12 + ((i - startIndex) * 12);
            
            // Determine player color and prefix
            String prefix = "• ";
            Color playerColor = Color.WHITE;
            
            if (vanishedPlayers.contains(playerName)) {
                prefix = "👻 "; // Ghost emoji for vanished players
                playerColor = VANISHED_COLOR;
            } else if (officialPlayers.contains(playerName)) {
                prefix = "✓ "; // Check mark for confirmed players
                playerColor = ONLINE_COLOR;
            } else {
                prefix = "? "; // Question mark for uncertain
                playerColor = Color.YELLOW;
            }
            
            guiGraphics.drawString(mc.font, prefix + playerName, 
                                 x + 10, playerY, playerColor.getRGB());
        }
        
        // Draw scroll indicators
        if (playerListScroll > 0) {
            guiGraphics.drawString(mc.font, "▲", x + 320, y + 12, Color.WHITE.getRGB());
        }
        if (startIndex + maxVisiblePlayers < allPlayers.size()) {
            guiGraphics.drawString(mc.font, "▼", x + 320, y + 12 + (maxVisiblePlayers - 1) * 12, Color.WHITE.getRGB());
        }
    }
    
    @Override
    protected void updateComponentPositions() {
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Update all component positions
        if (serverAddressBox != null) {
            serverAddressBox.setX(contentX);
            serverAddressBox.setY(contentY);
        }
        if (pingButton != null) {
            pingButton.setX(contentX + 210);
            pingButton.setY(contentY);
        }
        if (autoPingButton != null) {
            autoPingButton.setX(contentX + 270);
            autoPingButton.setY(contentY);
        }
        if (pingCurrentButton != null) {
            pingCurrentButton.setX(contentX);
            pingCurrentButton.setY(contentY + 30);
        }
        if (clearCacheButton != null) {
            clearCacheButton.setX(contentX + 90);
            clearCacheButton.setY(contentY + 30);
        }
    }
    
    // Button action methods
    private void pingServer() {
        if (isPinging) return;
        
        String address = serverAddressBox != null ? serverAddressBox.getValue().trim() : "";
        if (address.isEmpty()) {
            sendChatMessage("Enter a server address to ping!");
            return;
        }
        
        isPinging = true;
        
        // Parse address and port
        String host = address;
        int port = 25565;
        
        if (address.contains(":")) {
            String[] parts = address.split(":");
            host = parts[0];
            try {
                port = Integer.parseInt(parts[1]);
            } catch (NumberFormatException e) {
                port = 25565;
            }
        }
        
        CompletableFuture<ServerInfo> future = ServerPinger.pingServer(host, port);
        future.thenAccept(info -> {
            currentServerInfo = info;
            isPinging = false;
            
            // Integrate with ESP system to detect additional players
            integrateWithESP();
            
            sendChatMessage("Pinged " + info.getFormattedAddress() + " - " + 
                          info.getFormattedPing() + " - " + info.getFormattedPlayerCount());
        }).exceptionally(throwable -> {
            isPinging = false;
            sendChatMessage("Failed to ping server: " + throwable.getMessage());
            return null;
        });
    }
    
    private void pingCurrentServer() {
        if (isPinging) return;
        
        isPinging = true;
        
        CompletableFuture<ServerInfo> future = ServerPinger.pingCurrentServer();
        future.thenAccept(info -> {
            currentServerInfo = info;
            isPinging = false;
            
            // Update server address box
            if (serverAddressBox != null) {
                serverAddressBox.setValue(info.getFormattedAddress());
            }
            
            // Integrate with ESP system
            integrateWithESP();
            
            sendChatMessage("Current server: " + info.getFormattedAddress() + " - " + 
                          info.getFormattedPing() + " - " + info.getFormattedPlayerCount());
        }).exceptionally(throwable -> {
            isPinging = false;
            sendChatMessage("Failed to ping current server: " + throwable.getMessage());
            return null;
        });
    }
    
    private void toggleAutoPing() {
        autoPingEnabled = !autoPingEnabled;
        if (autoPingButton != null) {
            autoPingButton.setMessage(getAutoPingButtonText());
        }
        
        if (autoPingEnabled) {
            lastAutoPing = 0; // Trigger immediate ping
            sendChatMessage("Auto ping enabled (10s interval)");
        } else {
            sendChatMessage("Auto ping disabled");
        }
    }
    
    private void clearCache() {
        ServerPinger.clearCache();
        sendChatMessage("Server cache cleared");
    }
    
    /**
     * Integrate with ESP system to detect additional players
     */
    private void integrateWithESP() {
        if (currentServerInfo == null) return;
        
        // This would integrate with your ESP system to detect players
        // For now, this is a placeholder - you would call your ESP system here
        // Example: List<String> detectedPlayers = ESPSystem.getDetectedPlayers();
        
        // Placeholder: simulate detecting some players that might be vanished
        // In real implementation, this would come from your ESP/entity detection
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle player list scrolling
        if (currentServerInfo != null && isInPanel(mouseX, mouseY)) {
            int listStartY = (int)(y + titleBarHeight + 150);
            int listEndY = listStartY + (maxVisiblePlayers * 12);
            
            if (mouseY >= listStartY && mouseY <= listEndY) {
                if (button == 0) { // Left click - scroll up
                    playerListScroll = Math.max(0, playerListScroll - 1);
                    return true;
                } else if (button == 1) { // Right click - scroll down
                    int maxScroll = Math.max(0, currentServerInfo.getAllPlayers().size() - maxVisiblePlayers);
                    playerListScroll = Math.min(maxScroll, playerListScroll + 1);
                    return true;
                }
            }
        }
        
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    private Component getAutoPingButtonText() {
        return Component.literal("Auto: " + (autoPingEnabled ? "ON" : "OFF"));
    }
    
    private void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null) {
            mc.player.displayClientMessage(Component.literal("[Server Pinger] " + message), false);
        }
    }
    
    /**
     * Update the panel (called every frame)
     */
    public void update() {
        // Always update component positions first
        updateComponentPositions();

        // Update button states
        if (autoPingButton != null) {
            autoPingButton.setMessage(getAutoPingButtonText());
        }

        // Update ping button state
        if (pingButton != null) {
            pingButton.active = !isPinging;
        }
        if (pingCurrentButton != null) {
            pingCurrentButton.active = !isPinging;
        }
    }
    
    /**
     * Add detected player from external source (like ESP)
     */
    public void addDetectedPlayer(String playerName) {
        if (currentServerInfo != null) {
            currentServerInfo.addDetectedPlayer(playerName);
        }
    }
    
    /**
     * Get current server info
     */
    public ServerInfo getCurrentServerInfo() {
        return currentServerInfo;
    }
}
