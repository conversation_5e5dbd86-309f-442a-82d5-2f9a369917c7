@echo off
setlocal enabledelayedexpansion

echo ===============================================================================
echo                    Minecraft Script Recorder Mod Builder
echo                              Version 3.1.0
echo ===============================================================================
echo.

:: Check if we're in the right directory
if not exist "build.gradle" (
    echo ERROR: build.gradle not found!
    echo Please run this script from the mod's root directory.
    pause
    exit /b 1
)

echo [1/5] Checking Java installation...
java -version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Java not found!
    echo.
    echo Please install Java 21 first:
    echo 1. Run: powershell -ExecutionPolicy Bypass -File install_java21.ps1
    echo 2. Or manually download from: https://adoptium.net/temurin/releases/?version=21
    echo 3. Make sure to add Java to PATH during installation
    echo.
    pause
    exit /b 1
) else (
    echo Java found!
    java -version 2>&1 | findstr "version"
)

echo.
echo [2/5] Checking Gradle wrapper...
if not exist "gradlew.bat" (
    echo ERROR: Gradle wrapper not found!
    pause
    exit /b 1
) else (
    echo Gradle wrapper found!
)

echo.
echo [3/5] Cleaning previous build...
call gradlew.bat clean
if errorlevel 1 (
    echo ERROR: Clean failed!
    pause
    exit /b 1
)

echo.
echo [4/5] Building mod...
call gradlew.bat build
if errorlevel 1 (
    echo ERROR: Build failed!
    echo Check the error messages above for details.
    pause
    exit /b 1
)

echo.
echo [5/5] Checking build output...
if exist "build\libs\minecraft-script-recorder-1.20.4-3.1.0.jar" (
    echo SUCCESS: Mod built successfully!
    echo.
    echo Output file: build\libs\minecraft-script-recorder-1.20.4-3.1.0.jar
    echo.
    echo To install:
    echo 1. Install Minecraft Forge 1.20.4 ^(version 49.2.0+^)
    echo 2. Copy the JAR file to your Minecraft mods folder
    echo 3. Launch Minecraft with Forge profile
    echo.
    echo Mod features:
    echo - Press Shift + \ to open GUI
    echo - F6 to start/stop recording
    echo - F7 to start/stop playback
    echo - Toggle sprint/crouch functionality
    echo - Keystroke display overlay
) else (
    echo ERROR: Build completed but JAR file not found!
    echo Expected: build\libs\minecraft-script-recorder-1.20.4-3.1.0.jar
    dir build\libs\ 2>nul
)

echo.
echo Build process completed.
pause
