<!DOCTYPE html>
<html>
<head>
    <title>Repeating Annotations Demo</title>
</head>
<body>
<h2>Repeating Annotations Demo</h2>

<p>
    This demo shows how to use repeating annotations at runtime and at compile time.
</p>

<ul>
    <li><h3>Dependency checker.</h3>

        <p>
            Shows how to define repeating annotations and process them at compile time.
            The problem domain is some code that performs useful operations on hardware devices.
            The code relies on "modules" to be present on the devices. Applicability of the code to a particular
            device is checked while compiling the code for a particular device.
            A set of modules provided by a device is listed in an xml file that turns red during the compilation
            phase and is compared with the required module set specified by annotations.
            For instance, there is kettle with hardware modules: thermometer, display, and clock.
            There is also a boiler plug-in that requires clock, thermometer, heater, and optionally an LED light.

            Build the PluginChecker annotation processor first.
            Then, run javac with the annotation processor against plug-in sources using the following command: </p>

        <code>javac -cp "PluginChecker.jar" -processor checker.PluginChecker -Adevice=Kettle.xml -proc:only &lt;source
            files&gt;</code>

        <p>
            where <code>PluginChecker.jar</code> - path to jar file that contains PluginChecker annotation processor
            class. </br>
            <code>Kettle.xml</code> - path to device descriptor Kettle.xml                                         </br>
            <code>&lt;source files&gt;</code> - source files in Plugins/src
        </p>
        For more information, see the source files.
        </p>
        <ul>
            <li>Annotation processor sources: <a href="DependencyChecker/PluginChecker/src/">DependencyChecker/PluginChecker/src</a>
            <li>Processing of repeating annotations can be found in <a href="DependencyChecker/PluginChecker/src/checker/PluginChecker.java">PluginChecker.java</a>
            <li>Usage of repeating annotation is shown in modules sources.<a href="DependencyChecker/Plugins/src">DependencyChecker/Plugins/src</a>
        </ul>

    <li><h3>Validator.</h3>

        <p>
            Shows how to define repeating annotations and process them at runtime.
            A problem domain is code that needs to validate provided Suppliers for conformance to some criteria.
            The criteria are implemented by the Validator class which is applied by using annotations that are placed in
            the code whenever validation is needed. For more information, see the
            source files.
        </p>

        <p>
        <ul>
            <li>Usage of repeating annotation is described in <a href="Validator/src/PositiveIntegerSupplier.java">PositiveIntegerSupplier.java</a>
            <li> Example of how to define a repeating annotation type can be found in
                <a href="Validator/src/Validate.java">Validate.java</a>
            <li> Usages of the new reflective methods can be found in <a href="Validator/src/SupplierValidator.java">SupplierValidator.java</a>
        </ul>
        </p>
        Sources: <a href="Validator/src/">Validator/src/</a>
</ul>
</body>
</html>