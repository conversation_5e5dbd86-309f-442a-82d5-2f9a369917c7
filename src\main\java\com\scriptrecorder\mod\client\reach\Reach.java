package com.scriptrecorder.mod.client.reach;

import net.minecraft.client.Minecraft;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import java.util.List;

/**
 * Reach implementation that extends attack and block reach distances
 */
public class Reach {
    
    private static Reach instance;
    
    // Default Minecraft reach values
    public static final double DEFAULT_ATTACK_REACH = 3.0;
    public static final double DEFAULT_BLOCK_REACH = 4.5;
    public static final double MAX_SAFE_REACH = 6.0;
    
    // Settings - loaded from ModSettings
    private boolean enabled = false;
    private double attackReach = DEFAULT_ATTACK_REACH;
    private boolean modifyBlockReach = false;
    private double blockReach = DEFAULT_BLOCK_REACH;
    
    // State tracking
    private Entity lastTargetedEntity = null;
    private long lastReachTime = 0;
    private double currentEffectiveReach = DEFAULT_ATTACK_REACH;
    
    private Reach() {
        // Load settings from ModSettings
        loadSettings();
    }

    /**
     * Load settings from ModSettings
     */
    public void loadSettings() {
        enabled = com.scriptrecorder.mod.client.settings.ModSettings.isReachEnabled();
        modifyBlockReach = com.scriptrecorder.mod.client.settings.ModSettings.isModifyBlockReach();
        attackReach = com.scriptrecorder.mod.client.settings.ModSettings.getAttackReach();
        blockReach = com.scriptrecorder.mod.client.settings.ModSettings.getBlockReach();
    }
    
    public static Reach getInstance() {
        if (instance == null) {
            instance = new Reach();
        }
        return instance;
    }
    
    /**
     * Update reach calculations (called every tick)
     */
    public void update() {
        if (!enabled) {
            currentEffectiveReach = DEFAULT_ATTACK_REACH;
            return;
        }
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null || mc.level == null) return;
        
        // Update effective reach based on current settings
        currentEffectiveReach = attackReach;
        
        // Find entities within extended reach
        updateTargetedEntity();
    }
    
    /**
     * Update the targeted entity based on extended reach
     */
    private void updateTargetedEntity() {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null) return;
        
        Vec3 eyePos = mc.player.getEyePosition();
        Vec3 lookVec = mc.player.getViewVector(1.0f);
        Vec3 reachVec = eyePos.add(lookVec.scale(attackReach));
        
        // Find the closest entity within reach
        Entity closestEntity = null;
        double closestDistance = Double.MAX_VALUE;
        
        List<Entity> entities = mc.level.getEntities(mc.player, 
            mc.player.getBoundingBox().inflate(attackReach, attackReach, attackReach));
        
        for (Entity entity : entities) {
            if (entity == mc.player) continue;
            if (!(entity instanceof LivingEntity)) continue;
            
            // Calculate distance to entity
            AABB entityBB = entity.getBoundingBox();
            Vec3 closestPoint = getClosestPointOnAABB(entityBB, eyePos);
            double distance = eyePos.distanceTo(closestPoint);
            
            // Check if entity is within reach and in line of sight
            if (distance <= attackReach && isInLineOfSight(eyePos, reachVec, entityBB)) {
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestEntity = entity;
                }
            }
        }
        
        lastTargetedEntity = closestEntity;
        lastReachTime = System.currentTimeMillis();
    }
    
    /**
     * Check if an entity is within attack reach
     */
    public boolean isEntityInReach(Entity entity) {
        if (!enabled || entity == null) return false;
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null) return false;
        
        Vec3 eyePos = mc.player.getEyePosition();
        AABB entityBB = entity.getBoundingBox();
        Vec3 closestPoint = getClosestPointOnAABB(entityBB, eyePos);
        double distance = eyePos.distanceTo(closestPoint);
        
        return distance <= attackReach;
    }
    
    /**
     * Check if a block position is within block reach
     */
    public boolean isBlockInReach(Vec3 blockPos) {
        if (!enabled || !modifyBlockReach) return false;
        
        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null) return false;
        
        Vec3 eyePos = mc.player.getEyePosition();
        double distance = eyePos.distanceTo(blockPos);
        
        return distance <= blockReach;
    }
    
    /**
     * Get the closest point on an AABB to a given position
     */
    private Vec3 getClosestPointOnAABB(AABB aabb, Vec3 point) {
        double x = Math.max(aabb.minX, Math.min(point.x, aabb.maxX));
        double y = Math.max(aabb.minY, Math.min(point.y, aabb.maxY));
        double z = Math.max(aabb.minZ, Math.min(point.z, aabb.maxZ));
        return new Vec3(x, y, z);
    }
    
    /**
     * Check if there's a line of sight between two points through an AABB
     */
    private boolean isInLineOfSight(Vec3 start, Vec3 end, AABB targetBB) {
        // Simple line-AABB intersection test
        Vec3 direction = end.subtract(start).normalize();
        
        // Check if the ray intersects with the target bounding box
        return rayIntersectsAABB(start, direction, targetBB, attackReach);
    }
    
    /**
     * Ray-AABB intersection test
     */
    private boolean rayIntersectsAABB(Vec3 rayStart, Vec3 rayDir, AABB aabb, double maxDistance) {
        Vec3 invDir = new Vec3(1.0 / rayDir.x, 1.0 / rayDir.y, 1.0 / rayDir.z);
        
        double t1 = (aabb.minX - rayStart.x) * invDir.x;
        double t2 = (aabb.maxX - rayStart.x) * invDir.x;
        double t3 = (aabb.minY - rayStart.y) * invDir.y;
        double t4 = (aabb.maxY - rayStart.y) * invDir.y;
        double t5 = (aabb.minZ - rayStart.z) * invDir.z;
        double t6 = (aabb.maxZ - rayStart.z) * invDir.z;
        
        double tmin = Math.max(Math.max(Math.min(t1, t2), Math.min(t3, t4)), Math.min(t5, t6));
        double tmax = Math.min(Math.min(Math.max(t1, t2), Math.max(t3, t4)), Math.max(t5, t6));
        
        // Ray intersects AABB if tmax >= 0 and tmin <= tmax and tmin <= maxDistance
        return tmax >= 0 && tmin <= tmax && tmin <= maxDistance;
    }
    
    /**
     * Get the effective attack reach distance
     */
    public double getEffectiveAttackReach() {
        return enabled ? attackReach : DEFAULT_ATTACK_REACH;
    }
    
    /**
     * Get the effective block reach distance
     */
    public double getEffectiveBlockReach() {
        return (enabled && modifyBlockReach) ? blockReach : DEFAULT_BLOCK_REACH;
    }
    
    /**
     * Check if reach is currently extending beyond vanilla limits
     */
    public boolean isExtendingReach() {
        return enabled && (attackReach > DEFAULT_ATTACK_REACH || 
                          (modifyBlockReach && blockReach > DEFAULT_BLOCK_REACH));
    }
    
    /**
     * Get reach advantage over vanilla
     */
    public double getAttackReachAdvantage() {
        return enabled ? (attackReach - DEFAULT_ATTACK_REACH) : 0.0;
    }
    
    public double getBlockReachAdvantage() {
        return (enabled && modifyBlockReach) ? (blockReach - DEFAULT_BLOCK_REACH) : 0.0;
    }
    
    /**
     * Get recommended safe values for different server types
     */
    public static double getSafeAttackReach(ServerType serverType) {
        switch (serverType) {
            case HYPIXEL: return 3.1;
            case MINEMEN: return 3.3;
            case LUNAR: return 3.15;
            case VANILLA: return 3.2;
            default: return 3.1;
        }
    }
    
    public static double getSafeBlockReach(ServerType serverType) {
        switch (serverType) {
            case HYPIXEL: return 4.6;
            case MINEMEN: return 5.0;
            case LUNAR: return 4.7;
            case VANILLA: return 4.8;
            default: return 4.6;
        }
    }
    
    public enum ServerType {
        VANILLA("Vanilla"),
        HYPIXEL("Hypixel"),
        MINEMEN("Minemen"),
        LUNAR("Lunar"),
        CUSTOM("Custom");
        
        private final String name;
        
        ServerType(String name) {
            this.name = name;
        }
        
        public String getName() {
            return name;
        }
    }
    
    // Getters and setters
    public boolean isEnabled() { return enabled; }
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
        com.scriptrecorder.mod.client.settings.ModSettings.setReachEnabled(enabled);
    }

    public double getAttackReach() { return attackReach; }
    public void setAttackReach(double attackReach) {
        this.attackReach = Math.max(DEFAULT_ATTACK_REACH, Math.min(MAX_SAFE_REACH, attackReach));
        com.scriptrecorder.mod.client.settings.ModSettings.setAttackReach(this.attackReach);
    }

    public boolean isModifyBlockReach() { return modifyBlockReach; }
    public void setModifyBlockReach(boolean modifyBlockReach) {
        this.modifyBlockReach = modifyBlockReach;
        com.scriptrecorder.mod.client.settings.ModSettings.setModifyBlockReach(modifyBlockReach);
    }

    public double getBlockReach() { return blockReach; }
    public void setBlockReach(double blockReach) {
        this.blockReach = Math.max(DEFAULT_BLOCK_REACH, Math.min(MAX_SAFE_REACH, blockReach));
        com.scriptrecorder.mod.client.settings.ModSettings.setBlockReach(this.blockReach);
    }
    
    public Entity getLastTargetedEntity() { return lastTargetedEntity; }
    public double getCurrentEffectiveReach() { return currentEffectiveReach; }
    
    /**
     * Get formatted reach values
     */
    public String getFormattedAttackReach() {
        return String.format("%.1f", attackReach);
    }
    
    public String getFormattedBlockReach() {
        return String.format("%.1f", blockReach);
    }
    
    public String getFormattedAdvantage() {
        double advantage = getAttackReachAdvantage();
        if (advantage <= 0) return "None";
        return String.format("+%.1f", advantage);
    }
    
    /**
     * Reset reach state
     */
    public void reset() {
        lastTargetedEntity = null;
        lastReachTime = 0;
        currentEffectiveReach = enabled ? attackReach : DEFAULT_ATTACK_REACH;
    }
}
