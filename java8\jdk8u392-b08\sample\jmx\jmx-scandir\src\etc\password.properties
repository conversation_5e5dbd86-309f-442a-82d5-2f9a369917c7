##############################################################
#        Password File for Remote JMX Monitoring
##############################################################
#
# Password file for Remote JMX API access to monitoring.  This
# file defines the different roles and their passwords.  The access
# control file (jmxremote.access by default) defines the allowed
# access for each role.  To be functional, a role must have an entry
# in both the password and the access files.
#
# Default location of this file is $JRE/lib/management/jmxremote.password
# You can specify an alternate location by specifying a property in 
# the management config file $JRE/lib/management/management.properties
# or by specifying a system property (See that file for details).


##############################################################
#    File permissions of the jmxremote.password file
##############################################################
#      Since there are cleartext passwords stored in this file,
#      this file must be readable by ONLY the owner,
#      otherwise the program will exit with an error. 
#
# The file format for password and access files is syntactically the same
# as the Properties file format.  The syntax is described in the Javadoc
# for java.util.Properties.load.
# Typical password file has multiple  lines, where each line is blank,
# a comment (like this one), or a password entry.
#
#
# A password entry consists of a role name and an associated
# password.  The role name is any string that does not itself contain
# spaces or tabs.  The password is again any string that does not
# contain spaces or tabs.  Note that passwords appear in the clear in
# this file, so it is a good idea not to use valuable passwords.
#
# A given role should have at most one entry in this file.  If a role
# has no entry, it has no access.
# If multiple entries are found for the same role name, then the last one
# is used.
#
# In a typical installation, this file can be read by anybody on the
# local machine, and possibly by people on other machines.
# For # security, you should either restrict the access to this file,
# or specify another, less accessible file in the management config file
# as described above.
#
# Following are two commented-out entries.  The "measureRole" role has
# password "QED".  The "controlRole" role has password "R&D".
#
# monitorRole  QED
# controlRole   R&D

guest  guestpasswd
admin  adminpasswd
