<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN">

<html>
  <head>

<!--
 Copyright (c) 2006, Oracle and/or its affiliates. All rights reserved.

 Redistribution and use in source and binary forms, with or without
 modification, are permitted provided that the following conditions
 are met:

   - Redistributions of source code must retain the above copyright
     notice, this list of conditions and the following disclaimer.

   - Redistributions in binary form must reproduce the above copyright
     notice, this list of conditions and the following disclaimer in the
     documentation and/or other materials provided with the distribution.

   - Neither the name of Oracle nor the names of its
     contributors may be used to endorse or promote products derived
     from this software without specific prior written permission.

 THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
 IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 PURPOSE ARE DISCLAIMED.  IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
-->

    <title>com.sun.jmx.examples.scandir.config</title>
  </head>
  <body>
  <p>
     This package defines plain Java Beans, annotated for
     XML bindings, and used to store and model the scandir 
     application configuration.
  </p>
  <p>All the Java Beans defined in this package have been 
     designed to be <i>naturally serialized</i> by JAXB.
     Their bean properties were designed to minimize
     the number of XML annotation required, as well as 
     making them transparently convertible by the 
     JMX MXBean framework.
  </p>
  <p>The {@link com.sun.jmx.examples.scandir.config.ScanManagerConfig} 
     bean corresponds to the root element of the application's configuration.
     From an instance of this element, the 
     {@link com.sun.jmx.examples.scandir.ScanManagerMXBean} will be
     able to initialize the 
     {@link com.sun.jmx.examples.scandir.ResultLogManagerMXBean} and will
     create, register and initialize 
     {@link com.sun.jmx.examples.scandir.DirectoryScannerMXBean DirectoryScannerMXBeans}
  </p>
  <p>The {@link com.sun.jmx.examples.scandir.config.XmlConfigUtils} class is a simple utility
     classes used to deal with XML and XML configuration files.
  </p>
  <p>The {@link com.sun.jmx.examples.scandir.config.ResultRecord ResultRecords} 
     are used to store the results of directory scans in the result logs
     managed by the {@link com.sun.jmx.examples.scandir.ResultLogManagerMXBean}
  </p>
  </body>
</html>
