package com.scriptrecorder.mod.client.overlay;

import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

/**
 * Simple ESP/Tracers overlay for 1.8.9 compatibility
 */
@SideOnly(Side.CLIENT)
public class TracerESPOverlay189 {
    
    private static boolean enabled = false;
    
    public static void setEnabled(boolean enabled) {
        TracerESPOverlay189.enabled = enabled;
    }
    
    public static boolean isEnabled() {
        return enabled;
    }
    
    public static void loadSettings() {
        // Simple implementation for 1.8.9
        // TODO: Load ESP settings
    }
}
