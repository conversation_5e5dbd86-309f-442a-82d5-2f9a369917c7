package com.scriptrecorder.mod.client;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.client.KeyBindings;
import com.scriptrecorder.mod.client.gui.PanelBasedGUI;
import com.scriptrecorder.mod.client.autoclicker.AutoClicker;
import com.scriptrecorder.mod.client.reach.Reach;
import com.scriptrecorder.mod.client.cheststealer.ChestStealer;

import com.scriptrecorder.mod.client.overlay.KeystrokeOverlay;
import com.scriptrecorder.mod.client.overlay.TracerESPOverlay;
import com.scriptrecorder.mod.client.overlay.WaypointRenderer;
import com.scriptrecorder.mod.client.settings.ModSettings;
import com.scriptrecorder.mod.recording.RecordedScript;
import com.scriptrecorder.mod.recording.ScriptAction;
import net.minecraft.client.Minecraft;
import net.minecraft.client.settings.KeyBinding;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.common.gameevent.InputEvent;
import net.minecraftforge.fml.common.gameevent.TickEvent;
import net.minecraftforge.common.MinecraftForge;
import org.lwjgl.input.Keyboard;
import org.lwjgl.input.Mouse;

/**
 * Client-side initialization and event handling
 */
@SideOnly(Side.CLIENT)
public class ScriptRecorderClient {

    private static boolean initialized = false;
    private static boolean sprintToggled = false;
    private static boolean crouchToggled = false;
    private final Minecraft mc = Minecraft.getMinecraft();

    // Mouse movement tracking for camera recording
    private static int lastMouseX = 0;
    private static int lastMouseY = 0;
    private static boolean mouseInitialized = false;

    /**
     * Reset mouse tracking - call when starting recording to prevent camera jumps
     */
    public static void resetMouseTracking() {
        mouseInitialized = false;
        lastMouseX = 0;
        lastMouseY = 0;
    }

    // Key state tracking for custom key handling
    private static boolean[] keyPressed = new boolean[512]; // Track key states
    private static boolean[] lastKeyPressed = new boolean[512]; // Previous frame states
    private static boolean guiOpenDebounce = false; // Prevent multiple GUI opens

    public static void init() {
        if (initialized) return;

        ScriptRecorderMod.LOGGER.info("Initializing Script Recorder client");

        // Initialize settings system
        ModSettings.initialize();

        // Load saved settings
        ModSettings settings = ModSettings.getInstance();
        sprintToggled = settings.sprintToggled;
        crouchToggled = settings.crouchToggled;

        // Register overlay event handlers
        MinecraftForge.EVENT_BUS.register(new KeystrokeOverlay());
        MinecraftForge.EVENT_BUS.register(new WaypointRenderer());
        // TracerESPOverlay is registered via @Mod.EventBusSubscriber annotation
        // Ensure the class is loaded for static initialization
        TracerESPOverlay.loadSettings();

        // Load CPS overlay settings
        com.scriptrecorder.mod.client.overlay.CPSOverlay.loadSettings();

        // Initialize AutoFisher
        com.scriptrecorder.mod.client.autofisher.AutoFisher.getInstance().loadSettings();

        // Apply saved overlay settings
        KeystrokeOverlay.setEnabled(settings.keystrokeDisplayEnabled);
        TracerESPOverlay.loadSettings();

        initialized = true;
    }
    
    @SubscribeEvent
    public void onClientTick(TickEvent.ClientTickEvent event) {
        if (event.phase != TickEvent.Phase.END) return;

        if (mc.player == null) return;

        // Update key states and check for custom keybind presses
        updateKeyStates();

        // Check for GUI opening with Shift+\ combination
        boolean backslashPressed = Keyboard.isKeyDown(Keyboard.KEY_BACKSLASH);
        boolean shiftHeld = Keyboard.isKeyDown(Keyboard.KEY_LSHIFT) || Keyboard.isKeyDown(Keyboard.KEY_RSHIFT);

        // Use Shift+\ combination to open GUI
        if (backslashPressed && shiftHeld && !guiOpenDebounce) {
            mc.displayGuiScreen(new PanelBasedGUI());
            guiOpenDebounce = true;
        } else if (!backslashPressed) {
            guiOpenDebounce = false;
        }

        if (isKeyJustPressed(ModSettings.getInstance().keyRecord)) {
            handleRecordKey();
        }

        if (isKeyJustPressed(ModSettings.getInstance().keyStopRecord)) {
            handleStopRecordKey();
        }

        if (isKeyJustPressed(ModSettings.getInstance().keyPlay)) {
            handlePlayKey();
        }

        // Update autoclicker
        AutoClicker.getInstance().update();

        // Update reach
        Reach.getInstance().update();

        // Update chest stealer
        ChestStealer.getInstance().update();

        // Update autofisher
        com.scriptrecorder.mod.client.autofisher.AutoFisher.getInstance().update();

        // Check Minecraft-registered keybinds for toggle sprint and crouch
        if (KeyBindings.isToggleSprintPressed()) {
            toggleSprint();
        }

        if (KeyBindings.isToggleCrouchPressed()) {
            toggleCrouch();
        }

        if (ModSettings.getInstance().keyToggleKeystrokes != 0 && isKeyJustPressed(ModSettings.getInstance().keyToggleKeystrokes)) {
            toggleKeystrokeDisplay();
        }

        // Handle toggle sprint - only control when toggle is ON
        if (sprintToggled && mc.player != null) {
            KeyMapping.set(mc.options.keySprint.getKey(), true);
        }
        // When toggle sprint is OFF, let Minecraft handle sprint normally (don't override)

        // Handle toggle crouch - only control when toggle is ON
        if (crouchToggled && mc.player != null) {
            KeyMapping.set(mc.options.keyShift.getKey(), true);
        }
        // When toggle crouch is OFF, let Minecraft handle crouch normally (don't override)



        // Track mouse movement for camera recording
        trackMouseMovement();
    }

    private boolean hasShiftDown() {
        return GLFW.glfwGetKey(mc.getWindow().getWindow(), GLFW.GLFW_KEY_LEFT_SHIFT) == GLFW.GLFW_PRESS ||
               GLFW.glfwGetKey(mc.getWindow().getWindow(), GLFW.GLFW_KEY_RIGHT_SHIFT) == GLFW.GLFW_PRESS;
    }
    
    @SubscribeEvent
    public void onKeyInput(InputEvent.Key event) {
        int keyCode = event.getKey();
        int action = event.getAction();
        boolean pressed = (action == GLFW.GLFW_PRESS || action == GLFW.GLFW_REPEAT);

        // Update keystroke overlay
        KeystrokeOverlay.onKeyInput(keyCode, pressed);

        // Only record if we're currently recording and not in a GUI
        if (!ScriptRecorderMod.scriptManager.isRecording()) return;
        if (mc.screen != null) return;

        // Record key events
        String keyName = getKeyName(keyCode);
        if (keyName != null) {
            // Calculate time since recording started
            double currentTime = ScriptRecorderMod.scriptManager.getRecordingTime();

            if (pressed) {
                ScriptAction scriptAction = ScriptAction.keyPress(currentTime, keyName, keyCode);
                ScriptRecorderMod.scriptManager.recordAction(scriptAction);
            } else if (action == GLFW.GLFW_RELEASE) {
                ScriptAction scriptAction = ScriptAction.keyRelease(currentTime, keyName, keyCode);
                ScriptRecorderMod.scriptManager.recordAction(scriptAction);
            }
        }
    }
    
    @SubscribeEvent
    public void onMouseInput(InputEvent.MouseInputEvent event) {
        // Only record if we're currently recording and not in a GUI
        if (!ScriptRecorderMod.scriptManager.isRecording()) return;
        if (mc.currentScreen != null) return;

        // Get mouse button and state
        int button = Mouse.getEventButton();
        boolean pressed = Mouse.getEventButtonState();

        // Update keystroke overlay
        if (button >= 0) {
            KeystrokeOverlay.onMouseInput(button, pressed);
        }

        // Record mouse clicks (both press and release)
        if (button >= 0) {
            // Get mouse position
            int mouseX = Mouse.getEventX();
            int mouseY = Mouse.getEventY();

            String buttonName = getMouseButtonName(button);
            if (buttonName != null) {
                // Calculate time since recording started
                double currentTime = ScriptRecorderMod.scriptManager.getRecordingTime();
                ScriptAction scriptAction = ScriptAction.mouseClick(currentTime, mouseX, mouseY, buttonName, pressed);
                ScriptRecorderMod.scriptManager.recordAction(scriptAction);

                // Enhanced debug logging
                String actionType = pressed ? "PRESS" : "RELEASE";
                ScriptRecorderMod.LOGGER.info("Recorded mouse " + actionType + ": button=" + buttonName +
                    ", pos=(" + mouseX + "," + mouseY + "), time=" + currentTime);
            }
        }
    }

    @SubscribeEvent
    public void onMouseScroll(InputEvent.MouseScrollingEvent event) {
        // Only record if we're currently recording and not in a GUI
        if (!ScriptRecorderMod.scriptManager.isRecording()) return;
        if (mc.screen != null) return;

        // Get mouse position
        double mouseX = mc.mouseHandler.xpos();
        double mouseY = mc.mouseHandler.ypos();

        // Convert to screen coordinates
        int screenX = (int) (mouseX * mc.getWindow().getGuiScaledWidth() / mc.getWindow().getScreenWidth());
        int screenY = (int) (mouseY * mc.getWindow().getGuiScaledHeight() / mc.getWindow().getScreenHeight());

        // Calculate time since recording started
        double currentTime = ScriptRecorderMod.scriptManager.getRecordingTime();
        // TODO: Fix scroll delta method for 1.20.4 - temporarily disabled
        ScriptAction scriptAction = ScriptAction.mouseScroll(currentTime, screenX, screenY, 1);
        ScriptRecorderMod.scriptManager.recordAction(scriptAction);
    }

    // Helper methods



    private static void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null) {
            mc.player.displayClientMessage(net.minecraft.network.chat.Component.literal("[Toggle Sprint Mod] " + message), false);
        }
    }
    
    private static String getKeyName(int keyCode) {
        // Convert GLFW key codes to readable names (1.20.4)
        switch (keyCode) {
            case GLFW.GLFW_KEY_SPACE: return "space";
            case GLFW.GLFW_KEY_ENTER: return "enter";
            case GLFW.GLFW_KEY_TAB: return "tab";
            case GLFW.GLFW_KEY_BACKSPACE: return "backspace";
            case GLFW.GLFW_KEY_DELETE: return "delete";
            case GLFW.GLFW_KEY_LEFT: return "left";
            case GLFW.GLFW_KEY_RIGHT: return "right";
            case GLFW.GLFW_KEY_UP: return "up";
            case GLFW.GLFW_KEY_DOWN: return "down";
            case GLFW.GLFW_KEY_LEFT_SHIFT: return "left_shift";
            case GLFW.GLFW_KEY_RIGHT_SHIFT: return "right_shift";
            case GLFW.GLFW_KEY_LEFT_CONTROL: return "left_ctrl";
            case GLFW.GLFW_KEY_RIGHT_CONTROL: return "right_ctrl";
            case GLFW.GLFW_KEY_LEFT_ALT: return "left_alt";
            case GLFW.GLFW_KEY_RIGHT_ALT: return "right_alt";
            case GLFW.GLFW_KEY_ESCAPE: return "escape";
            default:
                // For letter keys
                if (keyCode >= GLFW.GLFW_KEY_A && keyCode <= GLFW.GLFW_KEY_Z) {
                    return String.valueOf((char) ('a' + (keyCode - GLFW.GLFW_KEY_A)));
                }
                // For number keys
                if (keyCode >= GLFW.GLFW_KEY_0 && keyCode <= GLFW.GLFW_KEY_9) {
                    return String.valueOf((char) ('0' + (keyCode - GLFW.GLFW_KEY_0)));
                }
                // For function keys
                if (keyCode >= GLFW.GLFW_KEY_F1 && keyCode <= GLFW.GLFW_KEY_F12) {
                    return "f" + (keyCode - GLFW.GLFW_KEY_F1 + 1);
                }
                return "key_" + keyCode;
        }
    }

    private static String getMouseButtonName(int button) {
        switch (button) {
            case 0: return "left";
            case 1: return "right";
            case 2: return "middle";
            default: return "button_" + button;
        }
    }

    // Mouse movement tracking using raw input
    private double lastMouseDeltaX = 0;
    private double lastMouseDeltaY = 0;
    private long lastMouseMoveTime = 0;

    @SubscribeEvent
    public void onMouseMove(InputEvent.MouseButton.Pre event) {
        // Only record if we're recording and not in a GUI
        if (!ScriptRecorderMod.scriptManager.isRecording()) return;
        if (Minecraft.getInstance().screen != null) return;

        Minecraft mc = Minecraft.getInstance();
        if (mc.player == null) return;

        // Get mouse handler for delta information
        // Note: We'll capture this in a different way since raw mouse input is complex in 1.20.4
    }

    // Mouse movement tracking for camera recording

    /**
     * Track mouse movement for camera recording using 1.20.4 APIs
     */
    private void trackMouseMovement() {
        // Only track if we're recording and not in a GUI
        if (!ScriptRecorderMod.scriptManager.isRecording()) return;
        if (mc.screen != null) return;
        if (mc.player == null) return;

        // Get current mouse position
        double currentMouseX = mc.mouseHandler.xpos();
        double currentMouseY = mc.mouseHandler.ypos();

        // Initialize mouse position on first run
        if (!mouseInitialized) {
            lastMouseX = (int) currentMouseX;
            lastMouseY = (int) currentMouseY;
            mouseInitialized = true;
            return;
        }

        // Calculate mouse delta (movement)
        int deltaX = (int) (currentMouseX - lastMouseX);
        int deltaY = (int) (currentMouseY - lastMouseY);

        // Only record if there's actual movement and enough time has passed
        long currentTime = System.currentTimeMillis();
        if ((deltaX != 0 || deltaY != 0) && (currentTime - lastMouseMoveTime > 8)) { // Higher frequency for smoother recording
            double recordingTime = ScriptRecorderMod.scriptManager.getRecordingTime();
            ScriptAction action = ScriptAction.mouseMove(recordingTime, deltaX, deltaY);
            ScriptRecorderMod.scriptManager.recordAction(action);

            // Update last position and time
            lastMouseX = (int) currentMouseX;
            lastMouseY = (int) currentMouseY;
            lastMouseMoveTime = currentTime;
        }
    }

    // Toggle methods
    private void toggleRecording() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            ScriptRecorderMod.scriptManager.stopRecording();
        } else {
            String scriptName = "QuickRecord_" + System.currentTimeMillis();
            ScriptRecorderMod.scriptManager.startRecording(scriptName);
        }
    }

    private void togglePlayback() {
        if (ScriptRecorderMod.scriptManager.isPlaying()) {
            ScriptRecorderMod.scriptManager.stopPlayback();
        } else {
            RecordedScript script = ScriptRecorderMod.scriptManager.getCurrentScript();
            if (script != null) {
                ScriptRecorderMod.scriptManager.startPlayback(script);
            }
        }
    }



    private void toggleSprint() {
        sprintToggled = !sprintToggled;
        ModSettings.getInstance().sprintToggled = sprintToggled;
        ModSettings.getInstance().save();
    }

    private void toggleCrouch() {
        crouchToggled = !crouchToggled;
        ModSettings.getInstance().crouchToggled = crouchToggled;
        ModSettings.getInstance().save();
    }

    private void toggleKeystrokeDisplay() {
        boolean enabled = !KeystrokeOverlay.isEnabled();
        KeystrokeOverlay.setEnabled(enabled);
        ModSettings.getInstance().keystrokeDisplayEnabled = enabled;
        ModSettings.getInstance().save();
    }

    // Getters for GUI
    public static boolean isSprintToggled() { return sprintToggled; }
    public static boolean isCrouchToggled() { return crouchToggled; }
    public static void setSprintToggled(boolean toggled) {
        sprintToggled = toggled;
        ModSettings.getInstance().sprintToggled = toggled;
        ModSettings.getInstance().save();
    }
    public static void setCrouchToggled(boolean toggled) {
        crouchToggled = toggled;
        ModSettings.getInstance().crouchToggled = toggled;
        ModSettings.getInstance().save();
    }

    // === NEW KEY HANDLERS WITH CHAT NOTIFICATIONS ===

    private void handleRecordKey() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            // Already recording, ignore F6 (use F7 to stop)
            sendChatMessage("Already recording! Press F7 to stop.");
        } else {
            // Reset mouse tracking to prevent camera jumps
            resetMouseTracking();

            if (ScriptRecorderMod.scriptManager.startRecording("recorded_script")) {
                sendChatMessage("Recording started. Press F7 to stop.");
            } else {
                sendChatMessage("Failed to start recording!");
            }
        }
    }

    private void handleStopRecordKey() {
        if (ScriptRecorderMod.scriptManager.isRecording()) {
            var script = ScriptRecorderMod.scriptManager.stopRecording();
            if (script != null) {
                sendChatMessage("Recording stopped. " + script.getTotalActions() + " actions captured.", false);
            } else {
                sendChatMessage("Recording stopped.", false);
            }
        } else {
            sendChatMessage("Not currently recording.", true);
        }
    }

    private void handlePlayKey() {
        if (ScriptRecorderMod.scriptManager.isPlaying()) {
            ScriptRecorderMod.scriptManager.stopPlayback();
            sendChatMessage("Playback stopped.", false);
        } else {
            var script = ScriptRecorderMod.scriptManager.getCurrentScript();
            if (script != null) {
                int loops = ModSettings.isInfiniteLoop() ? -1 : ModSettings.getLoopCount();
                script.setLoopCount(loops);

                if (ScriptRecorderMod.scriptManager.startPlayback(script)) {
                    String loopText = ModSettings.isInfiniteLoop() ? "infinite" : String.valueOf(ModSettings.getLoopCount());
                    sendChatMessage("Playing script (" + loopText + " loops). Press F8 to stop.", false);
                } else {
                    sendChatMessage("Failed to start playback!", true);
                }
            } else {
                sendChatMessage("No script to play! Record one first with F6.", true);
            }
        }
    }

    private void sendChatMessage(String message, boolean isError) {
        if (mc != null && mc.player != null) {
            var chatMessage = net.minecraft.network.chat.Component.literal("[Toggle Sprint Mod] " + message);
            if (isError) {
                chatMessage = chatMessage.withStyle(style -> style.withColor(0xFF4444));
            } else {
                chatMessage = chatMessage.withStyle(style -> style.withColor(0x44FF44));
            }
            mc.player.sendSystemMessage(chatMessage);
        }
    }

    // === CUSTOM KEY STATE TRACKING ===

    /**
     * Update key states for custom key detection
     */
    private void updateKeyStates() {
        long window = mc.getWindow().getWindow();

        // Copy current states to last states
        System.arraycopy(keyPressed, 0, lastKeyPressed, 0, keyPressed.length);

        // Update current states for keys we care about
        updateKeyState(window, ModSettings.getInstance().keyOpenGui);
        updateKeyState(window, ModSettings.getInstance().keyRecord);
        updateKeyState(window, ModSettings.getInstance().keyStopRecord);
        updateKeyState(window, ModSettings.getInstance().keyPlay);

        if (ModSettings.getInstance().keyToggleSprint != 0) {
            updateKeyState(window, ModSettings.getInstance().keyToggleSprint);
        }
        if (ModSettings.getInstance().keyToggleCrouch != 0) {
            updateKeyState(window, ModSettings.getInstance().keyToggleCrouch);
        }
        if (ModSettings.getInstance().keyToggleKeystrokes != 0) {
            updateKeyState(window, ModSettings.getInstance().keyToggleKeystrokes);
        }

        // Also track shift keys for GUI opening
        updateKeyState(window, GLFW.GLFW_KEY_LEFT_SHIFT);
        updateKeyState(window, GLFW.GLFW_KEY_RIGHT_SHIFT);
    }

    /**
     * Update the state of a specific key
     */
    private void updateKeyState(long window, int keyCode) {
        if (keyCode >= 0 && keyCode < keyPressed.length) {
            keyPressed[keyCode] = GLFW.glfwGetKey(window, keyCode) == GLFW.GLFW_PRESS;
        }
    }

    /**
     * Check if a key was just pressed (pressed this frame but not last frame)
     */
    private boolean isKeyJustPressed(int keyCode) {
        if (keyCode < 0 || keyCode >= keyPressed.length) return false;
        return keyPressed[keyCode] && !lastKeyPressed[keyCode];
    }
}
