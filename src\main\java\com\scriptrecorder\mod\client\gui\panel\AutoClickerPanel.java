package com.scriptrecorder.mod.client.gui.panel;

import com.scriptrecorder.mod.client.autoclicker.AutoClicker;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.network.chat.Component;
import java.awt.Color;

/**
 * Panel for AutoClicker configuration and control
 */
public class AutoClickerPanel extends Panel {
    
    // UI Components
    private Button enableButton;
    private Button attackEnabledButton;
    private Button useEnabledButton;
    private Button attackMethodButton;
    private Button useMethodButton;
    private Button cpsTrackerButton;
    private EditBox attackCPSMinBox;
    private EditBox attackCPSMaxBox;
    private EditBox useCPSMinBox;
    private EditBox useCPSMaxBox;
    
    // Colors
    private static final Color ENABLED_COLOR = new Color(50, 220, 50);
    private static final Color DISABLED_COLOR = new Color(220, 50, 50);
    private static final Color ATTACK_COLOR = new Color(255, 100, 100);
    private static final Color USE_COLOR = new Color(100, 100, 255);
    
    public AutoClickerPanel() {
        super("AutoClicker", 680, 10, 320, 200);
        initializeComponents();
    }
    
    private void initializeComponents() {
        clearWidgets();
        
        // Calculate positions relative to panel
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Main enable button
        enableButton = Button.builder(
            getEnableButtonText(),
            button -> toggleEnabled())
            .bounds(contentX, contentY, 120, 24)
            .build();
        addWidget(enableButton);

        // CPS Tracker button
        cpsTrackerButton = Button.builder(
            getCPSTrackerButtonText(),
            button -> toggleCPSTracker())
            .bounds(contentX + 130, contentY, 80, 24)
            .build();
        addWidget(cpsTrackerButton);
        
        // Attack section
        attackEnabledButton = Button.builder(
            getAttackEnabledButtonText(),
            button -> toggleAttackEnabled())
            .bounds(contentX, contentY + 35, 90, 20)
            .build();
        addWidget(attackEnabledButton);
        
        attackMethodButton = Button.builder(
            getAttackMethodButtonText(),
            button -> cycleAttackMethod())
            .bounds(contentX + 100, contentY + 35, 80, 20)
            .build();
        addWidget(attackMethodButton);
        
        // Attack CPS inputs
        attackCPSMinBox = new EditBox(Minecraft.getInstance().font, 
                                     contentX + 190, contentY + 35, 50, 20, 
                                     Component.literal("Attack CPS Min"));
        attackCPSMinBox.setValue(String.valueOf((int) AutoClicker.getInstance().getAttackCPSMin()));
        attackCPSMinBox.setMaxLength(2);
        addWidget(attackCPSMinBox);
        
        attackCPSMaxBox = new EditBox(Minecraft.getInstance().font, 
                                     contentX + 250, contentY + 35, 50, 20, 
                                     Component.literal("Attack CPS Max"));
        attackCPSMaxBox.setValue(String.valueOf((int) AutoClicker.getInstance().getAttackCPSMax()));
        attackCPSMaxBox.setMaxLength(2);
        addWidget(attackCPSMaxBox);
        
        // Use section
        useEnabledButton = Button.builder(
            getUseEnabledButtonText(),
            button -> toggleUseEnabled())
            .bounds(contentX, contentY + 65, 90, 20)
            .build();
        addWidget(useEnabledButton);
        
        useMethodButton = Button.builder(
            getUseMethodButtonText(),
            button -> cycleUseMethod())
            .bounds(contentX + 100, contentY + 65, 80, 20)
            .build();
        addWidget(useMethodButton);
        
        // Use CPS inputs
        useCPSMinBox = new EditBox(Minecraft.getInstance().font, 
                                  contentX + 190, contentY + 65, 50, 20, 
                                  Component.literal("Use CPS Min"));
        useCPSMinBox.setValue(String.valueOf((int) AutoClicker.getInstance().getUseCPSMin()));
        useCPSMinBox.setMaxLength(2);
        addWidget(useCPSMinBox);
        
        useCPSMaxBox = new EditBox(Minecraft.getInstance().font, 
                                  contentX + 250, contentY + 65, 50, 20, 
                                  Component.literal("Use CPS Max"));
        useCPSMaxBox.setValue(String.valueOf((int) AutoClicker.getInstance().getUseCPSMax()));
        useCPSMaxBox.setMaxLength(2);
        addWidget(useCPSMaxBox);
    }
    
    @Override
    protected void renderContent(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Always update component positions
        updateComponentPositions();
        
        // Draw labels and status
        int textX = (int)(x + 10);
        int textY = (int)(y + titleBarHeight + 100);
        
        Minecraft mc = Minecraft.getInstance();
        AutoClicker autoClicker = AutoClicker.getInstance();
        
        // Status information
        String status = autoClicker.isEnabled() ? "ACTIVE" : "INACTIVE";
        Color statusColor = autoClicker.isEnabled() ? ENABLED_COLOR : DISABLED_COLOR;
        guiGraphics.drawString(mc.font, "Status: " + status, 
                             textX, textY, statusColor.getRGB());
        
        // Current CPS values
        if (autoClicker.isEnabled()) {
            if (autoClicker.isAttackEnabled()) {
                String attackCPS = String.format("Attack CPS: %.1f", autoClicker.getCurrentAttackCPS());
                guiGraphics.drawString(mc.font, attackCPS, 
                                     textX, textY + 12, ATTACK_COLOR.getRGB());
            }
            
            if (autoClicker.isUseEnabled()) {
                String useCPS = String.format("Use CPS: %.1f", autoClicker.getCurrentUseCPS());
                guiGraphics.drawString(mc.font, useCPS, 
                                     textX + 150, textY + 12, USE_COLOR.getRGB());
            }
        }
        
        // Labels for CPS inputs
        guiGraphics.drawString(mc.font, "Attack:", textX, textY + 30, Color.WHITE.getRGB());
        guiGraphics.drawString(mc.font, "Min:", textX + 190, textY + 20, Color.LIGHT_GRAY.getRGB());
        guiGraphics.drawString(mc.font, "Max:", textX + 250, textY + 20, Color.LIGHT_GRAY.getRGB());
        
        guiGraphics.drawString(mc.font, "Use:", textX, textY + 60, Color.WHITE.getRGB());
        guiGraphics.drawString(mc.font, "Min:", textX + 190, textY + 50, Color.LIGHT_GRAY.getRGB());
        guiGraphics.drawString(mc.font, "Max:", textX + 250, textY + 50, Color.LIGHT_GRAY.getRGB());
        
        // Method descriptions
        guiGraphics.drawString(mc.font, "Constant: Steady clicking", 
                             textX, textY + 80, Color.GRAY.getRGB());
        guiGraphics.drawString(mc.font, "Dynamic: Humanized with variations", 
                             textX, textY + 92, Color.GRAY.getRGB());
        guiGraphics.drawString(mc.font, "Cooldown: Respects attack cooldown", 
                             textX, textY + 104, Color.GRAY.getRGB());
    }
    
    @Override
    protected void updateComponentPositions() {
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Update all component positions
        if (enableButton != null) {
            enableButton.setX(contentX);
            enableButton.setY(contentY);
        }
        if (cpsTrackerButton != null) {
            cpsTrackerButton.setX(contentX + 130);
            cpsTrackerButton.setY(contentY);
        }
        if (attackEnabledButton != null) {
            attackEnabledButton.setX(contentX);
            attackEnabledButton.setY(contentY + 35);
        }
        if (attackMethodButton != null) {
            attackMethodButton.setX(contentX + 100);
            attackMethodButton.setY(contentY + 35);
        }
        if (attackCPSMinBox != null) {
            attackCPSMinBox.setX(contentX + 190);
            attackCPSMinBox.setY(contentY + 35);
        }
        if (attackCPSMaxBox != null) {
            attackCPSMaxBox.setX(contentX + 250);
            attackCPSMaxBox.setY(contentY + 35);
        }
        if (useEnabledButton != null) {
            useEnabledButton.setX(contentX);
            useEnabledButton.setY(contentY + 65);
        }
        if (useMethodButton != null) {
            useMethodButton.setX(contentX + 100);
            useMethodButton.setY(contentY + 65);
        }
        if (useCPSMinBox != null) {
            useCPSMinBox.setX(contentX + 190);
            useCPSMinBox.setY(contentY + 65);
        }
        if (useCPSMaxBox != null) {
            useCPSMaxBox.setX(contentX + 250);
            useCPSMaxBox.setY(contentY + 65);
        }
    }
    
    // Button action methods
    private void toggleEnabled() {
        AutoClicker autoClicker = AutoClicker.getInstance();
        boolean newState = !autoClicker.isEnabled();
        autoClicker.setEnabled(newState);
        
        if (newState) {
            autoClicker.reset();
            sendChatMessage("AutoClicker enabled");
        } else {
            sendChatMessage("AutoClicker disabled");
        }
        
        updateButtonStates();
    }
    
    private void toggleAttackEnabled() {
        AutoClicker autoClicker = AutoClicker.getInstance();
        boolean newState = !autoClicker.isAttackEnabled();
        autoClicker.setAttackEnabled(newState);
        sendChatMessage("Attack clicking: " + (newState ? "ON" : "OFF"));
        updateButtonStates();
    }
    
    private void toggleUseEnabled() {
        AutoClicker autoClicker = AutoClicker.getInstance();
        boolean newState = !autoClicker.isUseEnabled();
        autoClicker.setUseEnabled(newState);
        sendChatMessage("Use clicking: " + (newState ? "ON" : "OFF"));
        updateButtonStates();
    }
    
    private void cycleAttackMethod() {
        AutoClicker autoClicker = AutoClicker.getInstance();
        AutoClicker.ClickMethod[] methods = AutoClicker.ClickMethod.values();
        int currentIndex = autoClicker.getAttackMethod().ordinal();
        int nextIndex = (currentIndex + 1) % methods.length;
        autoClicker.setAttackMethod(methods[nextIndex]);
        sendChatMessage("Attack method: " + methods[nextIndex].getName());
        updateButtonStates();
    }
    
    private void cycleUseMethod() {
        AutoClicker autoClicker = AutoClicker.getInstance();
        AutoClicker.ClickMethod[] methods = AutoClicker.ClickMethod.values();
        int currentIndex = autoClicker.getUseMethod().ordinal();
        int nextIndex = (currentIndex + 1) % methods.length;
        autoClicker.setUseMethod(methods[nextIndex]);
        sendChatMessage("Use method: " + methods[nextIndex].getName());
        updateButtonStates();
    }
    
    private void updateButtonStates() {
        if (enableButton != null) enableButton.setMessage(getEnableButtonText());
        if (attackEnabledButton != null) attackEnabledButton.setMessage(getAttackEnabledButtonText());
        if (useEnabledButton != null) useEnabledButton.setMessage(getUseEnabledButtonText());
        if (attackMethodButton != null) attackMethodButton.setMessage(getAttackMethodButtonText());
        if (useMethodButton != null) useMethodButton.setMessage(getUseMethodButtonText());
        if (cpsTrackerButton != null) cpsTrackerButton.setMessage(getCPSTrackerButtonText());
    }
    
    // Button text methods
    private Component getEnableButtonText() {
        boolean enabled = AutoClicker.getInstance().isEnabled();
        return Component.literal("AutoClicker: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getAttackEnabledButtonText() {
        boolean enabled = AutoClicker.getInstance().isAttackEnabled();
        return Component.literal("Attack: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getUseEnabledButtonText() {
        boolean enabled = AutoClicker.getInstance().isUseEnabled();
        return Component.literal("Use: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getAttackMethodButtonText() {
        return Component.literal("A:" + AutoClicker.getInstance().getAttackMethod().getName());
    }
    
    private Component getUseMethodButtonText() {
        return Component.literal("U:" + AutoClicker.getInstance().getUseMethod().getName());
    }

    private Component getCPSTrackerButtonText() {
        boolean enabled = com.scriptrecorder.mod.client.overlay.CPSOverlay.isEnabled();
        return Component.literal("CPS: " + (enabled ? "ON" : "OFF"));
    }

    private void toggleCPSTracker() {
        com.scriptrecorder.mod.client.overlay.CPSOverlay.toggle();
        sendChatMessage("CPS Tracker: " + (com.scriptrecorder.mod.client.overlay.CPSOverlay.isEnabled() ? "ON" : "OFF"));
        updateButtonStates();
    }
    
    private void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null) {
            mc.player.displayClientMessage(Component.literal("[AutoClicker] " + message), false);
        }
    }
    
    /**
     * Update the panel (called every frame)
     */
    public void update() {
        // Always update component positions first
        updateComponentPositions();
        updateCPSSettings();
        updateButtonStates();
    }
    
    private void updateCPSSettings() {
        AutoClicker autoClicker = AutoClicker.getInstance();
        
        // Update attack CPS settings
        if (attackCPSMinBox != null) {
            try {
                String value = attackCPSMinBox.getValue().trim();
                if (!value.isEmpty()) {
                    double cps = Double.parseDouble(value);
                    if (cps >= 1.0 && cps <= 20.0) {
                        autoClicker.setAttackCPSMin(cps);
                    }
                }
            } catch (NumberFormatException e) {
                attackCPSMinBox.setValue(String.valueOf((int) autoClicker.getAttackCPSMin()));
            }
        }
        
        if (attackCPSMaxBox != null) {
            try {
                String value = attackCPSMaxBox.getValue().trim();
                if (!value.isEmpty()) {
                    double cps = Double.parseDouble(value);
                    if (cps >= 1.0 && cps <= 20.0) {
                        autoClicker.setAttackCPSMax(cps);
                    }
                }
            } catch (NumberFormatException e) {
                attackCPSMaxBox.setValue(String.valueOf((int) autoClicker.getAttackCPSMax()));
            }
        }
        
        // Update use CPS settings
        if (useCPSMinBox != null) {
            try {
                String value = useCPSMinBox.getValue().trim();
                if (!value.isEmpty()) {
                    double cps = Double.parseDouble(value);
                    if (cps >= 1.0 && cps <= 20.0) {
                        autoClicker.setUseCPSMin(cps);
                    }
                }
            } catch (NumberFormatException e) {
                useCPSMinBox.setValue(String.valueOf((int) autoClicker.getUseCPSMin()));
            }
        }
        
        if (useCPSMaxBox != null) {
            try {
                String value = useCPSMaxBox.getValue().trim();
                if (!value.isEmpty()) {
                    double cps = Double.parseDouble(value);
                    if (cps >= 1.0 && cps <= 20.0) {
                        autoClicker.setUseCPSMax(cps);
                    }
                }
            } catch (NumberFormatException e) {
                useCPSMaxBox.setValue(String.valueOf((int) autoClicker.getUseCPSMax()));
            }
        }
    }
}
