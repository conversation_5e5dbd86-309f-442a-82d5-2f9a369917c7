package com.scriptrecorder.mod;

import net.minecraftforge.fml.common.Mod;
import net.minecraftforge.fml.common.SidedProxy;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;

@Mod(modid = ScriptRecorderModMinimal.MODID, name = ScriptRecorderModMinimal.NAME, version = ScriptRecorderModMinimal.VERSION)
public class ScriptRecorderModMinimal {
    public static final String MODID = "waypointmod";
    public static final String NAME = "Waypoint Mod";
    public static final String VERSION = "@VERSION@";
    public static final Logger LOGGER = LogManager.getLogger(MODID);

    @SidedProxy(clientSide = "com.scriptrecorder.mod.ClientProxyMinimal", serverSide = "com.scriptrecorder.mod.CommonProxyMinimal")
    public static CommonProxyMinimal proxy;

    @Mod.EventHandler
    public void preInit(FMLPreInitializationEvent event) {
        LOGGER.info("Waypoint Mod pre-initialization");
        proxy.preInit(event);
    }

    @Mod.EventHandler
    public void init(FMLInitializationEvent event) {
        LOGGER.info("Waypoint Mod initialization");
        proxy.init(event);
    }
}
