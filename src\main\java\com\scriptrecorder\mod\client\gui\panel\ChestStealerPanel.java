package com.scriptrecorder.mod.client.gui.panel;

import com.scriptrecorder.mod.client.cheststealer.ChestStealer;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.network.chat.Component;
import java.awt.Color;

/**
 * Panel for ChestStealer configuration and control
 */
public class ChestStealerPanel extends Panel {
    
    // UI Components
    private Button enableButton;
    private Button intelligentButton;
    private Button checkTitleButton;
    private Button titleCheckTypeButton;
    private Button ignoreCaseButton;
    private EditBox delayMinBox;
    private EditBox delayMaxBox;
    private EditBox openDelayBox;
    private EditBox closeDelayBox;
    private EditBox randomizationBox;
    private EditBox failChanceBox;
    private EditBox titleSubstringBox;
    
    // Colors
    private static final Color ENABLED_COLOR = new Color(50, 220, 50);
    private static final Color DISABLED_COLOR = new Color(220, 50, 50);
    private static final Color STEALING_COLOR = new Color(255, 165, 0);
    private static final Color WAITING_COLOR = new Color(100, 150, 255);
    
    public ChestStealerPanel() {
        super("ChestStealer", 20, 450, 340, 220);
        initializeComponents();
    }
    
    private void initializeComponents() {
        clearWidgets();
        
        // Calculate positions relative to panel
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Main enable button
        enableButton = Button.builder(
            getEnableButtonText(),
            button -> toggleEnabled())
            .bounds(contentX, contentY, 120, 24)
            .build();
        addWidget(enableButton);
        
        // Intelligent mode button
        intelligentButton = Button.builder(
            getIntelligentButtonText(),
            button -> toggleIntelligent())
            .bounds(contentX + 130, contentY, 80, 24)
            .build();
        addWidget(intelligentButton);
        
        // Check title button
        checkTitleButton = Button.builder(
            getCheckTitleButtonText(),
            button -> toggleCheckTitle())
            .bounds(contentX + 220, contentY, 80, 24)
            .build();
        addWidget(checkTitleButton);
        
        // Delay settings row 1
        delayMinBox = new EditBox(Minecraft.getInstance().font, 
                                 contentX + 60, contentY + 35, 50, 20, 
                                 Component.literal("Min Delay"));
        delayMinBox.setValue(String.valueOf(ChestStealer.getInstance().getDelayMin()));
        delayMinBox.setMaxLength(4);
        addWidget(delayMinBox);
        
        delayMaxBox = new EditBox(Minecraft.getInstance().font, 
                                 contentX + 120, contentY + 35, 50, 20, 
                                 Component.literal("Max Delay"));
        delayMaxBox.setValue(String.valueOf(ChestStealer.getInstance().getDelayMax()));
        delayMaxBox.setMaxLength(4);
        addWidget(delayMaxBox);
        
        openDelayBox = new EditBox(Minecraft.getInstance().font, 
                                  contentX + 180, contentY + 35, 50, 20, 
                                  Component.literal("Open Delay"));
        openDelayBox.setValue(String.valueOf(ChestStealer.getInstance().getOpenDelay()));
        openDelayBox.setMaxLength(4);
        addWidget(openDelayBox);
        
        closeDelayBox = new EditBox(Minecraft.getInstance().font, 
                                   contentX + 240, contentY + 35, 50, 20, 
                                   Component.literal("Close Delay"));
        closeDelayBox.setValue(String.valueOf(ChestStealer.getInstance().getCloseDelay()));
        closeDelayBox.setMaxLength(4);
        addWidget(closeDelayBox);
        
        // Humanization settings row 2
        randomizationBox = new EditBox(Minecraft.getInstance().font, 
                                      contentX + 80, contentY + 65, 50, 20, 
                                      Component.literal("Randomization"));
        randomizationBox.setValue(String.valueOf(ChestStealer.getInstance().getRandomization()));
        randomizationBox.setMaxLength(3);
        addWidget(randomizationBox);
        
        failChanceBox = new EditBox(Minecraft.getInstance().font, 
                                   contentX + 180, contentY + 65, 50, 20, 
                                   Component.literal("Fail Chance"));
        failChanceBox.setValue(String.valueOf(ChestStealer.getInstance().getFailChance()));
        failChanceBox.setMaxLength(3);
        addWidget(failChanceBox);
        
        // Title filtering row 3
        titleSubstringBox = new EditBox(Minecraft.getInstance().font, 
                                       contentX + 60, contentY + 95, 100, 20, 
                                       Component.literal("Title Filter"));
        titleSubstringBox.setValue(ChestStealer.getInstance().getTitleSubstring());
        titleSubstringBox.setMaxLength(20);
        addWidget(titleSubstringBox);
        
        titleCheckTypeButton = Button.builder(
            getTitleCheckTypeButtonText(),
            button -> cycleTitleCheckType())
            .bounds(contentX + 170, contentY + 95, 70, 20)
            .build();
        addWidget(titleCheckTypeButton);
        
        ignoreCaseButton = Button.builder(
            getIgnoreCaseButtonText(),
            button -> toggleIgnoreCase())
            .bounds(contentX + 250, contentY + 95, 60, 20)
            .build();
        addWidget(ignoreCaseButton);
    }
    
    @Override
    protected void renderContent(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Always update component positions
        updateComponentPositions();
        
        // Draw labels and status
        int textX = (int)(x + 10);
        int textY = (int)(y + titleBarHeight + 125);
        
        Minecraft mc = Minecraft.getInstance();
        ChestStealer chestStealer = ChestStealer.getInstance();
        
        // Status information
        String status = chestStealer.getStatus();
        Color statusColor = getStatusColor(status);
        guiGraphics.drawString(mc.font, "Status: " + status, 
                             textX, textY, statusColor.getRGB());
        
        // Settings summary
        String summary = chestStealer.getSettingsSummary();
        guiGraphics.drawString(mc.font, summary, 
                             textX, textY + 12, Color.LIGHT_GRAY.getRGB());
        
        // Labels for input boxes
        guiGraphics.drawString(mc.font, "Delays:", textX, textY + 30, Color.WHITE.getRGB());
        guiGraphics.drawString(mc.font, "Min:", textX, textY + 45, Color.GRAY.getRGB());
        guiGraphics.drawString(mc.font, "Max:", textX + 60, textY + 45, Color.GRAY.getRGB());
        guiGraphics.drawString(mc.font, "Open:", textX + 120, textY + 45, Color.GRAY.getRGB());
        guiGraphics.drawString(mc.font, "Close:", textX + 180, textY + 45, Color.GRAY.getRGB());
        
        guiGraphics.drawString(mc.font, "Humanization:", textX, textY + 60, Color.WHITE.getRGB());
        guiGraphics.drawString(mc.font, "Random:", textX, textY + 75, Color.GRAY.getRGB());
        guiGraphics.drawString(mc.font, "Fail %:", textX + 100, textY + 75, Color.GRAY.getRGB());
        
        if (chestStealer.isCheckTitle()) {
            guiGraphics.drawString(mc.font, "Title Filter:", textX, textY + 90, Color.WHITE.getRGB());
        }
        
        // Help text
        guiGraphics.drawString(mc.font, "Open a chest to start stealing", 
                             textX, textY + 105, Color.GRAY.getRGB());
        guiGraphics.drawString(mc.font, "Intelligent mode avoids worse items", 
                             textX, textY + 117, Color.GRAY.getRGB());
    }
    
    private Color getStatusColor(String status) {
        switch (status.toLowerCase()) {
            case "disabled": return DISABLED_COLOR;
            case "stealing items": return STEALING_COLOR;
            case "finishing...": return STEALING_COLOR;
            case "waiting for container": return WAITING_COLOR;
            default: return Color.WHITE;
        }
    }
    
    @Override
    protected void updateComponentPositions() {
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Update all component positions
        if (enableButton != null) {
            enableButton.setX(contentX);
            enableButton.setY(contentY);
        }
        if (intelligentButton != null) {
            intelligentButton.setX(contentX + 130);
            intelligentButton.setY(contentY);
        }
        if (checkTitleButton != null) {
            checkTitleButton.setX(contentX + 220);
            checkTitleButton.setY(contentY);
        }
        if (delayMinBox != null) {
            delayMinBox.setX(contentX + 60);
            delayMinBox.setY(contentY + 35);
        }
        if (delayMaxBox != null) {
            delayMaxBox.setX(contentX + 120);
            delayMaxBox.setY(contentY + 35);
        }
        if (openDelayBox != null) {
            openDelayBox.setX(contentX + 180);
            openDelayBox.setY(contentY + 35);
        }
        if (closeDelayBox != null) {
            closeDelayBox.setX(contentX + 240);
            closeDelayBox.setY(contentY + 35);
        }
        if (randomizationBox != null) {
            randomizationBox.setX(contentX + 80);
            randomizationBox.setY(contentY + 65);
        }
        if (failChanceBox != null) {
            failChanceBox.setX(contentX + 180);
            failChanceBox.setY(contentY + 65);
        }
        if (titleSubstringBox != null) {
            titleSubstringBox.setX(contentX + 60);
            titleSubstringBox.setY(contentY + 95);
        }
        if (titleCheckTypeButton != null) {
            titleCheckTypeButton.setX(contentX + 170);
            titleCheckTypeButton.setY(contentY + 95);
        }
        if (ignoreCaseButton != null) {
            ignoreCaseButton.setX(contentX + 250);
            ignoreCaseButton.setY(contentY + 95);
        }
    }
    
    // Button action methods
    private void toggleEnabled() {
        ChestStealer chestStealer = ChestStealer.getInstance();
        boolean newState = !chestStealer.isEnabled();
        chestStealer.setEnabled(newState);
        
        if (newState) {
            sendChatMessage("ChestStealer enabled");
        } else {
            sendChatMessage("ChestStealer disabled");
        }
        
        updateButtonStates();
    }
    
    private void toggleIntelligent() {
        ChestStealer chestStealer = ChestStealer.getInstance();
        boolean newState = !chestStealer.isIntelligent();
        chestStealer.setIntelligent(newState);
        sendChatMessage("Intelligent mode: " + (newState ? "ON" : "OFF"));
        updateButtonStates();
    }
    
    private void toggleCheckTitle() {
        ChestStealer chestStealer = ChestStealer.getInstance();
        boolean newState = !chestStealer.isCheckTitle();
        chestStealer.setCheckTitle(newState);
        sendChatMessage("Title checking: " + (newState ? "ON" : "OFF"));
        updateButtonStates();
    }
    
    private void cycleTitleCheckType() {
        ChestStealer chestStealer = ChestStealer.getInstance();
        ChestStealer.TitleCheckType[] types = ChestStealer.TitleCheckType.values();
        int currentIndex = chestStealer.getTitleCheckType().ordinal();
        int nextIndex = (currentIndex + 1) % types.length;
        chestStealer.setTitleCheckType(types[nextIndex]);
        sendChatMessage("Title check type: " + types[nextIndex].getName());
        updateButtonStates();
    }
    
    private void toggleIgnoreCase() {
        ChestStealer chestStealer = ChestStealer.getInstance();
        boolean newState = !chestStealer.isIgnoreCase();
        chestStealer.setIgnoreCase(newState);
        sendChatMessage("Ignore case: " + (newState ? "ON" : "OFF"));
        updateButtonStates();
    }
    
    private void updateButtonStates() {
        if (enableButton != null) enableButton.setMessage(getEnableButtonText());
        if (intelligentButton != null) intelligentButton.setMessage(getIntelligentButtonText());
        if (checkTitleButton != null) checkTitleButton.setMessage(getCheckTitleButtonText());
        if (titleCheckTypeButton != null) titleCheckTypeButton.setMessage(getTitleCheckTypeButtonText());
        if (ignoreCaseButton != null) ignoreCaseButton.setMessage(getIgnoreCaseButtonText());
    }
    
    // Button text methods
    private Component getEnableButtonText() {
        boolean enabled = ChestStealer.getInstance().isEnabled();
        return Component.literal("ChestStealer: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getIntelligentButtonText() {
        boolean enabled = ChestStealer.getInstance().isIntelligent();
        return Component.literal("Intel: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getCheckTitleButtonText() {
        boolean enabled = ChestStealer.getInstance().isCheckTitle();
        return Component.literal("Title: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getTitleCheckTypeButtonText() {
        return Component.literal(ChestStealer.getInstance().getTitleCheckType().getName());
    }
    
    private Component getIgnoreCaseButtonText() {
        boolean enabled = ChestStealer.getInstance().isIgnoreCase();
        return Component.literal("Case: " + (enabled ? "IGN" : "SENS"));
    }
    
    private void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null) {
            mc.player.displayClientMessage(Component.literal("[ChestStealer] " + message), false);
        }
    }
    
    /**
     * Update the panel (called every frame)
     */
    public void update() {
        // Always update component positions first
        updateComponentPositions();
        updateSettings();
        updateButtonStates();
    }
    
    private void updateSettings() {
        ChestStealer chestStealer = ChestStealer.getInstance();
        
        // Update delay settings
        updateIntegerSetting(delayMinBox, chestStealer::setDelayMin, chestStealer.getDelayMin());
        updateIntegerSetting(delayMaxBox, chestStealer::setDelayMax, chestStealer.getDelayMax());
        updateIntegerSetting(openDelayBox, chestStealer::setOpenDelay, chestStealer.getOpenDelay());
        updateIntegerSetting(closeDelayBox, chestStealer::setCloseDelay, chestStealer.getCloseDelay());
        updateIntegerSetting(randomizationBox, chestStealer::setRandomization, chestStealer.getRandomization());
        updateIntegerSetting(failChanceBox, chestStealer::setFailChance, chestStealer.getFailChance());
        
        // Update title substring
        if (titleSubstringBox != null) {
            String value = titleSubstringBox.getValue().trim();
            if (!value.isEmpty() && !value.equals(chestStealer.getTitleSubstring())) {
                chestStealer.setTitleSubstring(value);
            }
        }
    }
    
    private void updateIntegerSetting(EditBox box, java.util.function.IntConsumer setter, int currentValue) {
        if (box != null) {
            try {
                String value = box.getValue().trim();
                if (!value.isEmpty()) {
                    int intValue = Integer.parseInt(value);
                    setter.accept(intValue);
                }
            } catch (NumberFormatException e) {
                box.setValue(String.valueOf(currentValue));
            }
        }
    }
}
