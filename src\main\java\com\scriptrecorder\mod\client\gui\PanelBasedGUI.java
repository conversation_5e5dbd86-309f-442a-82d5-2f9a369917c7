package com.scriptrecorder.mod.client.gui;

import com.scriptrecorder.mod.client.gui.panel.*;
import com.scriptrecorder.mod.client.gui.panel.ReachPanel;
import com.scriptrecorder.mod.client.gui.panel.ChestStealerPanel;
import com.scriptrecorder.mod.client.settings.ModSettings;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.AbstractWidget;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import java.awt.Color;

/**
 * Modern panel-based GUI screen
 */
public class PanelBasedGUI extends Screen {
    
    private PanelManager panelManager;
    private boolean initialized = false;
    
    // Panel instances
    private ScriptRecorderPanel scriptRecorderPanel;
    private ESPTracersPanel espTracersPanel;
    private SettingsPanel settingsPanel;
    private ScriptManagerPanel scriptManagerPanel;
    private ServerPingerPanel serverPingerPanel;
    private AutoClickerPanel autoClickerPanel;
    private AutoFisherPanel autoFisherPanel;
    private ReachPanel reachPanel;
    private ChestStealerPanel chestStealerPanel;
    
    public PanelBasedGUI() {
        super(Component.literal("Waypoint Manager"));
        this.panelManager = PanelManager.getInstance();
    }
    
    @Override
    protected void init() {
        super.init();
        
        if (!initialized) {
            initializePanels();
            initialized = true;
        }
        
        // Update panel positions for current screen size
        updatePanelPositions();
    }
    
    private void initializePanels() {
        // Clear existing panels
        panelManager.clear();
        
        // Create and add panels
        scriptRecorderPanel = new ScriptRecorderPanel();
        panelManager.addPanel(scriptRecorderPanel);
        
        espTracersPanel = new ESPTracersPanel();
        panelManager.addPanel(espTracersPanel);
        
        settingsPanel = new SettingsPanel();
        panelManager.addPanel(settingsPanel);
        
        scriptManagerPanel = new ScriptManagerPanel();
        panelManager.addPanel(scriptManagerPanel);

        serverPingerPanel = new ServerPingerPanel();
        panelManager.addPanel(serverPingerPanel);

        autoClickerPanel = new AutoClickerPanel();
        panelManager.addPanel(autoClickerPanel);

        reachPanel = new ReachPanel();
        panelManager.addPanel(reachPanel);

        chestStealerPanel = new ChestStealerPanel();
        panelManager.addPanel(chestStealerPanel);

        autoFisherPanel = new AutoFisherPanel();
        panelManager.addPanel(autoFisherPanel);

        // Set global accent color
        Color accentColor = new Color(0, 162, 232); // Blue theme
        panelManager.setGlobalAccentColor(accentColor);
        
        // Configure background
        panelManager.setBlurBackground(true);
        panelManager.setBackgroundOpacity(0.4);
    }
    
    private void updatePanelPositions() {
        // Auto-layout panels for current screen size
        int screenWidth = this.width;
        int screenHeight = this.height;
        
        // Position panels in a grid layout
        if (scriptRecorderPanel != null) {
            scriptRecorderPanel.setX(20);
            scriptRecorderPanel.setY(20);
        }
        
        if (espTracersPanel != null) {
            espTracersPanel.setX(320);
            espTracersPanel.setY(20);
        }
        
        if (settingsPanel != null) {
            settingsPanel.setX(20);
            settingsPanel.setY(180);
        }
        
        if (scriptManagerPanel != null) {
            scriptManagerPanel.setX(320);
            scriptManagerPanel.setY(240);
        }

        if (serverPingerPanel != null) {
            serverPingerPanel.setX(20);
            serverPingerPanel.setY(340);
        }

        if (autoClickerPanel != null) {
            autoClickerPanel.setX(680);
            autoClickerPanel.setY(240);
        }

        if (reachPanel != null) {
            reachPanel.setX(350);
            reachPanel.setY(240);
        }

        if (chestStealerPanel != null) {
            chestStealerPanel.setX(20);
            chestStealerPanel.setY(450);
        }
    }


    
    @Override
    public void tick() {
        super.tick();

        // Update widget positions when panels move
        updateWidgetPositions();
    }

    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        // Render background
        this.renderBackground(guiGraphics, mouseX, mouseY, partialTick);

        // Update panels
        updatePanels();

        // Render all panels
        panelManager.render(guiGraphics, mouseX, mouseY, partialTick);

        // Render help text
        renderHelpText(guiGraphics);

        super.render(guiGraphics, mouseX, mouseY, partialTick);
    }

    /**
     * Update widget positions when panels are moved
     */
    private void updateWidgetPositions() {
        for (Panel panel : panelManager.getPanels()) {
            // This will trigger the panel's updateComponentPositions method
            panel.update();
        }
    }
    
    private void updatePanels() {
        // Update each panel
        if (scriptRecorderPanel != null) {
            scriptRecorderPanel.update();
        }
        if (espTracersPanel != null) {
            espTracersPanel.update();
        }
        if (settingsPanel != null) {
            settingsPanel.update();
        }
        if (scriptManagerPanel != null) {
            scriptManagerPanel.update();
        }
        if (serverPingerPanel != null) {
            serverPingerPanel.update();
        }
        if (autoClickerPanel != null) {
            autoClickerPanel.update();
        }
        if (reachPanel != null) {
            reachPanel.update();
        }
        if (chestStealerPanel != null) {
            chestStealerPanel.update();
        }
    }
    
    private void renderHelpText(GuiGraphics guiGraphics) {
        // Render help text in bottom-right corner
        String[] helpLines = {
            "§7Drag panels by title bar",
            "§7F6: Record | F7: Stop | F8: Play",
            "§71-8: Toggle panels | ESC: Close",
            "§7ChestStealer: Automated chest looting"
        };
        
        int x = this.width - 200;
        int y = this.height - 60;
        
        for (int i = 0; i < helpLines.length; i++) {
            guiGraphics.drawString(this.font, helpLines[i], x, y + i * 12, Color.WHITE.getRGB());
        }
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle panel clicks first
        if (panelManager.mouseClicked(mouseX, mouseY, button)) {
            return true;
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    @Override
    public boolean mouseDragged(double mouseX, double mouseY, int button, double deltaX, double deltaY) {
        // Handle panel dragging first
        if (panelManager.mouseDragged(mouseX, mouseY, button, deltaX, deltaY)) {
            return true;
        }

        return super.mouseDragged(mouseX, mouseY, button, deltaX, deltaY);
    }

    @Override
    public boolean mouseReleased(double mouseX, double mouseY, int button) {
        // Always reset drag state for all panels when mouse is released
        // This ensures panels don't get stuck in dragging state if mouse is released outside the panel
        for (Panel panel : panelManager.getPanels()) {
            panel.mouseReleased(mouseX, mouseY, button);
        }

        // Handle panel mouse release through panel manager
        if (panelManager.mouseReleased(mouseX, mouseY, button)) {
            return true;
        }

        return super.mouseReleased(mouseX, mouseY, button);
    }
    
    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        // Forward key events to panels first (for text boxes, etc.)
        for (Panel panel : panelManager.getPanels()) {
            // Check if any EditBox in this panel has focus
            for (AbstractWidget widget : panel.getWidgets()) {
                if (widget instanceof EditBox && ((EditBox) widget).isFocused()) {
                    // Forward key press to the panel
                    return panel.keyPressed(keyCode, scanCode, modifiers);
                }
            }
        }

        // If no EditBox has focus, check all panels
        for (Panel panel : panelManager.getPanels()) {
            if (panel.keyPressed(keyCode, scanCode, modifiers)) {
                return true;
            }
        }

        // Handle ESC to close
        if (keyCode == 256) { // ESC key
            this.onClose();
            return true;
        }

        // Handle panel shortcuts
        if (keyCode == 49) { // 1 key
            panelManager.togglePanel("Script Recorder");
            return true;
        }
        if (keyCode == 50) { // 2 key
            panelManager.togglePanel("ESP & Tracers");
            return true;
        }
        if (keyCode == 51) { // 3 key
            panelManager.togglePanel("Settings");
            return true;
        }
        if (keyCode == 52) { // 4 key
            panelManager.togglePanel("Script Manager");
            return true;
        }
        if (keyCode == 53) { // 5 key
            panelManager.togglePanel("Server Pinger");
            return true;
        }
        if (keyCode == 54) { // 6 key
            panelManager.togglePanel("AutoClicker");
            return true;
        }
        if (keyCode == 55) { // 7 key
            panelManager.togglePanel("Reach");
            return true;
        }
        if (keyCode == 56) { // 8 key
            panelManager.togglePanel("ChestStealer");
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean charTyped(char codePoint, int modifiers) {
        // Forward character events to panels first (for text boxes)
        for (Panel panel : panelManager.getPanels()) {
            // Check if any EditBox in this panel has focus
            for (AbstractWidget widget : panel.getWidgets()) {
                if (widget instanceof EditBox && ((EditBox) widget).isFocused()) {
                    // Forward char typed to the panel
                    return panel.charTyped(codePoint, modifiers);
                }
            }
        }

        // If no EditBox has focus, check all panels
        for (Panel panel : panelManager.getPanels()) {
            if (panel.charTyped(codePoint, modifiers)) {
                return true;
            }
        }

        return super.charTyped(codePoint, modifiers);
    }
    
    @Override
    public void onClose() {
        // Force all panels to save their current values
        savePanelValues();

        // Save settings when closing
        ModSettings.getInstance().save();
        super.onClose();
    }

    /**
     * Save all panel values to their respective settings
     */
    private void savePanelValues() {
        // Update each panel to save their current EditBox values
        if (scriptRecorderPanel != null) {
            scriptRecorderPanel.update(); // This calls updateLoopCountFromInput()
        }
        if (espTracersPanel != null) {
            espTracersPanel.update(); // This calls updateDistanceSettings()
        }
        if (reachPanel != null) {
            reachPanel.update(); // This calls updateReachSettings()
        }
        if (chestStealerPanel != null) {
            chestStealerPanel.update(); // This calls updateSettings()
        }
        if (autoClickerPanel != null) {
            autoClickerPanel.update(); // This calls updateCPSSettings()
        }
        if (autoFisherPanel != null) {
            autoFisherPanel.update(); // This calls updateSettings()
        }
        // Other panels don't have settings that need saving
    }
    
    @Override
    public boolean isPauseScreen() {
        return false; // Don't pause the game
    }
    
    /**
     * Toggle specific panel visibility
     */
    public void togglePanel(String panelTitle) {
        panelManager.togglePanel(panelTitle);
    }
    
    /**
     * Show specific panel
     */
    public void showPanel(String panelTitle) {
        panelManager.showPanel(panelTitle);
    }
    
    /**
     * Hide specific panel
     */
    public void hidePanel(String panelTitle) {
        panelManager.hidePanel(panelTitle);
    }
    
    /**
     * Reset all panels to default positions
     */
    public void resetPanelPositions() {
        panelManager.resetPositions();
    }
    
    /**
     * Change the global accent color
     */
    public void setAccentColor(Color color) {
        panelManager.setGlobalAccentColor(color);
    }
    
    /**
     * Get the panel manager
     */
    public PanelManager getPanelManager() {
        return panelManager;
    }
    
    /**
     * Static method to open the GUI
     */
    public static void openGUI() {
        net.minecraft.client.Minecraft.getInstance().setScreen(new PanelBasedGUI());
    }
}
