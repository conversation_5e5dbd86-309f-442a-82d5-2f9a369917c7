# Quick Start Guide - Minecraft 1.8.9 Waypoint Mod

## 🚀 Installation

1. **Download Minecraft Forge 1.8.9** (version 11.15.1.2318 or later)
2. **Build the mod** using the provided build system
3. **Place the JAR** in your `.minecraft/mods/` folder
4. **Launch Minecraft 1.8.9** with Forge

## ⌨️ Controls

### Main Hotkeys
- **Shift + \\** - Open mod settings GUI
- **F6** - Start recording a script
- **F7** - Stop recording a script
- **F8** - Play the last recorded script

### Toggle Features (set via GUI)
- **Toggle Sprint** - Persistent sprint without holding key
- **Toggle Crouch** - Persistent crouch without holding key

## 🎮 Basic Usage

### Script Recording
1. Press **F6** to start recording
2. Perform your actions (mouse clicks, key presses)
3. Press **F7** to stop recording
4. Press **F8** to replay the script

### Using the GUI
1. Press **Shift + \\** to open the settings
2. Toggle features on/off with buttons
3. Use script controls for recording/playback
4. Settings are automatically saved

### Keystroke Display
1. Open GUI with **Shift + \\**
2. Click "Keystroke Display: OFF" to enable
3. <PERSON> will appear on screen when pressed
4. Keys fade out after 1 second

## 📍 Waypoint Commands

- `/waypoint add <name>` - Add waypoint at current location
- `/waypoint remove <name>` - Remove a waypoint
- `/waypoint list` - Show all waypoints

## 🔧 Features

### ✅ Working Features
- Script recording and playback
- Toggle sprint/crouch
- Keystroke display overlay
- Settings GUI
- Waypoint management
- Hotkey system
- Settings persistence

### 🎯 Perfect for
- Automating repetitive tasks
- Recording complex movement sequences
- Showing keystrokes for tutorials
- Managing waypoints
- Quality of life improvements

## 💡 Tips

1. **Recording**: Close all GUIs before recording for best results
2. **Playback**: Scripts replay at the same timing they were recorded
3. **Settings**: All settings persist between game sessions
4. **Hotkeys**: Hotkeys work even when chat is open
5. **Waypoints**: Use descriptive names for easy management

## 🐛 Troubleshooting

### Mod doesn't load
- Ensure you have Minecraft 1.8.9 with Forge 11.15.1.2318+
- Check that the JAR is in the correct mods folder
- Verify Java 8 is being used

### Hotkeys don't work
- Check if other mods are conflicting
- Try rebinding keys in the GUI
- Ensure the mod loaded properly (check logs)

### Script recording issues
- Make sure you're not in a GUI when recording
- Check that F6/F7/F8 keys aren't bound to other functions
- Verify the script was saved (check status in GUI)

## 📁 File Locations

- **Scripts**: `.minecraft/scripts/` folder
- **Settings**: `.minecraft/waypoint_settings.json`
- **Waypoints**: `.minecraft/waypoints.json`

## 🎉 Enjoy!

The mod is fully functional and ready to enhance your Minecraft 1.8.9 experience!
