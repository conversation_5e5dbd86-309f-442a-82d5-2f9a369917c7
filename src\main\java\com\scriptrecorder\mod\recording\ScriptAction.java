package com.scriptrecorder.mod.recording;

import com.google.gson.annotations.SerializedName;

/**
 * Represents a single recorded action (mouse click, key press, etc.)
 */
public class ScriptAction {
    
    public enum ActionType {
        @SerializedName("mouse_click")
        MOUSE_CLICK,
        
        @SerializedName("mouse_scroll")
        MOUSE_SCROLL,
        
        @SerializedName("key_press")
        KEY_PRESS,
        
        @SerializedName("key_release")
        KEY_RELEASE,
        
        @SerializedName("mouse_move")
        MOUSE_MOVE
    }
    
    @SerializedName("type")
    private ActionType type;
    
    @SerializedName("time")
    private double time; // Time in seconds from start of recording
    
    // Mouse-specific fields
    @SerializedName("x")
    private int x;
    
    @SerializedName("y")
    private int y;
    
    @SerializedName("button")
    private String button; // "left", "right", "middle"
    
    @SerializedName("pressed")
    private boolean pressed; // true for press, false for release
    
    @SerializedName("dx")
    private double dx; // Scroll delta X
    
    @SerializedName("dy")
    private double dy; // Scroll delta Y
    
    // Keyboard-specific fields
    @SerializedName("key")
    private String key; // Key name or character
    
    @SerializedName("keyCode")
    private int keyCode; // GLFW key code
    
    // Constructors
    public ScriptAction() {}
    
    public ScriptAction(ActionType type, double time) {
        this.type = type;
        this.time = time;
    }
    
    // Mouse click constructor
    public static ScriptAction mouseClick(double time, int x, int y, String button, boolean pressed) {
        ScriptAction action = new ScriptAction(ActionType.MOUSE_CLICK, time);
        action.x = x;
        action.y = y;
        action.button = button;
        action.pressed = pressed;
        return action;
    }
    
    // Mouse scroll constructor
    public static ScriptAction mouseScroll(double time, int x, int y, double dx, double dy) {
        ScriptAction action = new ScriptAction(ActionType.MOUSE_SCROLL, time);
        action.x = x;
        action.y = y;
        action.dx = dx;
        action.dy = dy;
        return action;
    }
    
    // Key press constructor
    public static ScriptAction keyPress(double time, String key, int keyCode) {
        ScriptAction action = new ScriptAction(ActionType.KEY_PRESS, time);
        action.key = key;
        action.keyCode = keyCode;
        return action;
    }
    
    // Key release constructor
    public static ScriptAction keyRelease(double time, String key, int keyCode) {
        ScriptAction action = new ScriptAction(ActionType.KEY_RELEASE, time);
        action.key = key;
        action.keyCode = keyCode;
        return action;
    }
    
    // Mouse move constructor (delta movement)
    public static ScriptAction mouseMove(double time, int deltaX, int deltaY) {
        ScriptAction action = new ScriptAction(ActionType.MOUSE_MOVE, time);
        action.x = deltaX;
        action.y = deltaY;
        return action;
    }

    // Mouse scroll constructor (simple version)
    public static ScriptAction mouseScroll(double time, int x, int y, int scrollDirection) {
        ScriptAction action = new ScriptAction(ActionType.MOUSE_SCROLL, time);
        action.x = x;
        action.y = y;
        action.dx = 0;
        action.dy = scrollDirection;
        return action;
    }
    
    // Getters
    public ActionType getType() { return type; }
    public double getTime() { return time; }
    public int getX() { return x; }
    public int getY() { return y; }
    public String getButton() { return button; }
    public boolean isPressed() { return pressed; }
    public double getDx() { return dx; }
    public double getDy() { return dy; }
    public String getKey() { return key; }
    public int getKeyCode() { return keyCode; }
    
    // Setters
    public void setType(ActionType type) { this.type = type; }
    public void setTime(double time) { this.time = time; }
    public void setX(int x) { this.x = x; }
    public void setY(int y) { this.y = y; }
    public void setButton(String button) { this.button = button; }
    public void setPressed(boolean pressed) { this.pressed = pressed; }
    public void setDx(double dx) { this.dx = dx; }
    public void setDy(double dy) { this.dy = dy; }
    public void setKey(String key) { this.key = key; }
    public void setKeyCode(int keyCode) { this.keyCode = keyCode; }
    
    @Override
    public String toString() {
        return String.format("ScriptAction{type=%s, time=%.2f, details=%s}", 
            type, time, getActionDetails());
    }
    
    private String getActionDetails() {
        switch (type) {
            case MOUSE_CLICK:
                return String.format("(%d,%d) %s %s", x, y, button, pressed ? "press" : "release");
            case MOUSE_SCROLL:
                return String.format("(%d,%d) scroll(%.1f,%.1f)", x, y, dx, dy);
            case KEY_PRESS:
            case KEY_RELEASE:
                return String.format("key=%s code=%d", key, keyCode);
            case MOUSE_MOVE:
                return String.format("move to (%d,%d)", x, y);
            default:
                return "unknown";
        }
    }
}
