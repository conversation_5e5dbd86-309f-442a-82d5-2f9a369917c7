package com.scriptrecorder.mod.client;

import com.scriptrecorder.mod.CommonProxy;
import com.scriptrecorder.mod.client.overlay.KeystrokeOverlay189;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;

public class ClientProxy extends CommonProxy {

    @Override
    public void preInit(FMLPreInitializationEvent event) {
        super.preInit(event);

        // Register key bindings
        KeyBindings.register();
    }

    @Override
    public void init(FMLInitializationEvent event) {
        super.init(event);

        // Register client event handlers
        MinecraftForge.EVENT_BUS.register(new ScriptRecorderClient189());
        MinecraftForge.EVENT_BUS.register(KeystrokeOverlay189.class);

        // Initialize client
        ScriptRecorderClient189.init();
    }
}
