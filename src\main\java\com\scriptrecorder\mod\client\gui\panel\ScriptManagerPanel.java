package com.scriptrecorder.mod.client.gui.panel;

import com.scriptrecorder.mod.ScriptRecorderMod;
import com.scriptrecorder.mod.recording.RecordedScript;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.network.chat.Component;
import java.awt.Color;
import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * Panel for script management (import/export/list)
 */
public class ScriptManagerPanel extends Panel {
    
    // UI Components
    private EditBox scriptNameBox;
    private Button exportButton;
    private Button importButton;
    private Button deleteButton;
    private Button listScriptsButton;
    private Button loadScriptButton;
    
    // Script list
    private List<String> availableScripts = new ArrayList<>();
    private int selectedScriptIndex = -1;
    
    public ScriptManagerPanel() {
        super("Script Manager", 320, 240, 300, 180);
        initializeComponents();
        refreshScriptList();
    }
    
    private void initializeComponents() {
        clearWidgets();
        
        // Calculate positions relative to panel
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Script name input
        scriptNameBox = new EditBox(Minecraft.getInstance().font,
                                   contentX, contentY, 180, 20,
                                   Component.literal("Script Name"));
        scriptNameBox.setValue(com.scriptrecorder.mod.client.settings.ModSettings.getLastScriptName());
        scriptNameBox.setMaxLength(50);
        addWidget(scriptNameBox);
        
        // Export/Import buttons
        exportButton = Button.builder(
            Component.literal("Export"),
            button -> onExportPressed())
            .bounds(contentX + 190, contentY, 60, 20)
            .build();
        addWidget(exportButton);
        
        importButton = Button.builder(
            Component.literal("Import"),
            button -> onImportPressed())
            .bounds(contentX + 190, contentY + 25, 60, 20)
            .build();
        addWidget(importButton);
        
        // Management buttons
        deleteButton = Button.builder(
            Component.literal("Delete"),
            button -> onDeletePressed())
            .bounds(contentX, contentY + 30, 80, 20)
            .build();
        addWidget(deleteButton);
        
        listScriptsButton = Button.builder(
            Component.literal("Refresh"),
            button -> refreshScriptList())
            .bounds(contentX + 90, contentY + 30, 80, 20)
            .build();
        addWidget(listScriptsButton);
        
        loadScriptButton = Button.builder(
            Component.literal("Load"),
            button -> onLoadPressed())
            .bounds(contentX + 190, contentY + 50, 60, 20)
            .build();
        addWidget(loadScriptButton);
    }
    
    @Override
    protected void renderContent(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Update component positions when panel moves
        updateComponentPositions();
        
        // Draw script list
        int textX = (int)(x + 10);
        int textY = (int)(y + titleBarHeight + 80);
        
        guiGraphics.drawString(Minecraft.getInstance().font, "Available Scripts:", 
                             textX, textY, Color.WHITE.getRGB());
        
        // Draw script list (max 5 scripts visible)
        int maxVisible = Math.min(5, availableScripts.size());
        for (int i = 0; i < maxVisible; i++) {
            String scriptName = availableScripts.get(i);
            Color textColor = (i == selectedScriptIndex) ? Color.YELLOW : Color.LIGHT_GRAY;
            
            // Truncate long names
            if (scriptName.length() > 25) {
                scriptName = scriptName.substring(0, 22) + "...";
            }
            
            guiGraphics.drawString(Minecraft.getInstance().font, "• " + scriptName, 
                                 textX + 10, textY + 12 + (i * 10), textColor.getRGB());
        }
        
        // Show total count if more than 5
        if (availableScripts.size() > 5) {
            guiGraphics.drawString(Minecraft.getInstance().font, 
                                 "... and " + (availableScripts.size() - 5) + " more", 
                                 textX + 10, textY + 12 + (5 * 10), Color.GRAY.getRGB());
        }
        
        // Show current script info
        RecordedScript currentScript = ScriptRecorderMod.scriptManager.getCurrentScript();
        if (currentScript != null) {
            guiGraphics.drawString(Minecraft.getInstance().font, 
                                 "Current: " + currentScript.getName(), 
                                 textX, textY - 15, Color.GREEN.getRGB());
        }
    }
    
    @Override
    protected void updateComponentPositions() {
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Update all component positions
        if (scriptNameBox != null) {
            scriptNameBox.setX(contentX);
            scriptNameBox.setY(contentY);
        }
        if (exportButton != null) {
            exportButton.setX(contentX + 190);
            exportButton.setY(contentY);
        }
        if (importButton != null) {
            importButton.setX(contentX + 190);
            importButton.setY(contentY + 25);
        }
        if (deleteButton != null) {
            deleteButton.setX(contentX);
            deleteButton.setY(contentY + 30);
        }
        if (listScriptsButton != null) {
            listScriptsButton.setX(contentX + 90);
            listScriptsButton.setY(contentY + 30);
        }
        if (loadScriptButton != null) {
            loadScriptButton.setX(contentX + 190);
            loadScriptButton.setY(contentY + 50);
        }
    }
    
    // Button action methods
    private void onExportPressed() {
        RecordedScript currentScript = ScriptRecorderMod.scriptManager.getCurrentScript();
        if (currentScript == null) {
            sendChatMessage("No script to export!");
            return;
        }

        String scriptName = scriptNameBox != null ? scriptNameBox.getValue().trim() : "exported_script";
        if (scriptName.isEmpty()) {
            scriptName = "exported_script";
        }

        // Save the script name for future use
        com.scriptrecorder.mod.client.settings.ModSettings.setLastScriptName(scriptName);

        if (ScriptRecorderMod.scriptManager.exportScript(currentScript, scriptName)) {
            sendChatMessage("Script exported as: " + scriptName);
            refreshScriptList();
        } else {
            sendChatMessage("Failed to export script!");
        }
    }
    
    private void onImportPressed() {
        String scriptName = scriptNameBox != null ? scriptNameBox.getValue().trim() : "";
        if (scriptName.isEmpty()) {
            sendChatMessage("Enter a script name to import!");
            return;
        }

        // Save the script name for future use
        com.scriptrecorder.mod.client.settings.ModSettings.setLastScriptName(scriptName);

        RecordedScript importedScript = ScriptRecorderMod.scriptManager.importScript(scriptName);
        if (importedScript != null) {
            sendChatMessage("Script imported: " + scriptName);
            ScriptRecorderMod.scriptManager.setCurrentScript(importedScript);
        } else {
            sendChatMessage("Failed to import script: " + scriptName);
        }
    }
    
    private void onDeletePressed() {
        String scriptName = scriptNameBox != null ? scriptNameBox.getValue().trim() : "";
        if (scriptName.isEmpty()) {
            sendChatMessage("Enter a script name to delete!");
            return;
        }
        
        if (ScriptRecorderMod.scriptManager.deleteScript(scriptName)) {
            sendChatMessage("Script deleted: " + scriptName);
            refreshScriptList();
        } else {
            sendChatMessage("Failed to delete script: " + scriptName);
        }
    }
    
    private void onLoadPressed() {
        if (selectedScriptIndex >= 0 && selectedScriptIndex < availableScripts.size()) {
            String scriptName = availableScripts.get(selectedScriptIndex);
            RecordedScript script = ScriptRecorderMod.scriptManager.importScript(scriptName);
            if (script != null) {
                ScriptRecorderMod.scriptManager.setCurrentScript(script);
                sendChatMessage("Loaded script: " + scriptName);
            } else {
                sendChatMessage("Failed to load script: " + scriptName);
            }
        } else {
            sendChatMessage("No script selected!");
        }
    }
    
    private void refreshScriptList() {
        availableScripts.clear();
        selectedScriptIndex = -1;
        
        // Get scripts from the data folder
        File dataFolder = new File("data");
        if (dataFolder.exists() && dataFolder.isDirectory()) {
            File[] files = dataFolder.listFiles((dir, name) -> name.endsWith(".json"));
            if (files != null) {
                for (File file : files) {
                    String fileName = file.getName();
                    // Remove .json extension
                    if (fileName.endsWith(".json")) {
                        fileName = fileName.substring(0, fileName.length() - 5);
                    }
                    availableScripts.add(fileName);
                }
            }
        }
        
        sendChatMessage("Found " + availableScripts.size() + " scripts");
    }
    
    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle script list selection
        if (isInPanel(mouseX, mouseY)) {
            int textY = (int)(y + titleBarHeight + 92); // Start of script list
            int relativeY = (int)(mouseY - textY);
            
            if (relativeY >= 0 && relativeY < 50) { // Within script list area
                int clickedIndex = relativeY / 10;
                if (clickedIndex < availableScripts.size()) {
                    selectedScriptIndex = clickedIndex;
                    if (scriptNameBox != null) {
                        scriptNameBox.setValue(availableScripts.get(clickedIndex));
                    }
                    return true;
                }
            }
        }
        
        return super.mouseClicked(mouseX, mouseY, button);
    }
    
    private void sendChatMessage(String message) {
        Minecraft mc = Minecraft.getInstance();
        if (mc.player != null) {
            mc.player.displayClientMessage(Component.literal("[Script Manager] " + message), false);
        }
    }
    
    /**
     * Update the panel (called every frame)
     */
    public void update() {
        // Always update component positions first
        updateComponentPositions();
    }
}
