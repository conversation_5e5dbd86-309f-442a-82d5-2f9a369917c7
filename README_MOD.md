# Minecraft Script Recorder Mod

A comprehensive Minecraft 1.8.9 Forge mod that provides script recording, autofishing, keystroke display, Spotify integration, and more! Features a sleek, discreet GUI accessible via **Shift + \\**.

## 🌟 Features

### Core Script Recording
- **🎮 In-Game Recording**: Record mouse clicks, keyboard presses, and timing while playing
- **🔄 Script Playback**: Replay recorded actions with customizable loop counts (including infinite)
- **⌨️ Hotkey Access**: Open GUI with **Shift + \\**, quick controls with **F6** (record) and **F7** (play)
- **💾 Script Management**: Save and load scripts as JSON files
- **🎨 Sleek GUI**: Minimalist, semi-transparent interface that doesn't obstruct gameplay

### Quality of Life Features
- **🏃 Toggle Sprint**: Keep sprinting without holding the key
- **🐌 Toggle Crouch**: Stay crouched without holding the key
- **⌨️ Keystroke Display**: Real-time overlay showing pressed keys with fade effects
- **🎵 Spotify Integration**: Display current song and album with progress bar
- **🎣 Autofishing**: Intelligent fishing bot with human-like reaction times

### Performance & Design
- **⚡ Performance Optimized**: Lightweight with minimal impact on game performance
- **🎯 Minecraft 1.8.9 Support**: Built specifically for the popular PvP version
- **🔒 Window-Focused**: Only operates when Minecraft window is active

## Installation

1. **Download Minecraft Forge** for version 1.8.9 (recommended: `1.8.9-11.15.1.2318-1.8.9`)
2. **Build the mod**:
   ```bash
   # Windows
   gradlew.bat build

   # macOS/Linux
   ./gradlew build
   ```
3. **Install the mod**: Place the generated JAR file (`minecraft-script-recorder-1.8.9-1.0.0.jar`) in your `mods` folder

## Usage

### Opening the GUI
- Press **Shift + \\** to open the Script Recorder interface
- The GUI is semi-transparent and won't pause your game

### Recording Scripts
1. Enter a name for your script in the text field
2. Click **Record** or press **F6**
3. Perform the actions you want to record in Minecraft
4. Click **Stop** or press **F6** again to finish recording

### Playing Scripts
1. Set the number of loops (or click **∞** for infinite)
2. Click **Play** or press **F7**
3. The script will replay your recorded actions
4. Click **Stop** to interrupt playback

### Managing Scripts
- **Save Script**: Saves the current recorded script to a JSON file
- **Load Script**: Loads a previously saved script (shows available scripts in status)

## 🎣 Autofishing System

The mod includes an intelligent autofishing system with human-like behavior:

### Features
- **Smart Detection**: Monitors fishing bobber position and velocity to detect fish bites
- **Human-like Reactions**: Random reaction times between 150-400ms
- **Auto-Recast**: Automatically recasts after 20 seconds if no fish is caught
- **Bobber Tracking**: Tracks bobber state (in water, moving, etc.)
- **Safety**: Only operates when holding a fishing rod

### How It Works
1. **Enable**: Press F8 or toggle in GUI while holding a fishing rod
2. **Auto-Cast**: Automatically casts if no bobber exists
3. **Detection**: Monitors bobber for sudden downward movement (fish bite)
4. **Reaction**: Reels in with realistic human reaction time
5. **Timeout**: Recasts automatically after 20 seconds if no fish
6. **Repeat**: Continues the cycle until disabled

### Status Display
The GUI shows real-time autofishing status:
- "Autofishing: Ready" - System enabled, waiting for fishing rod
- "Autofishing: Casting..." - Currently casting line
- "Autofishing: Waiting (15s)" - Waiting for fish, shows remaining time
- "Autofishing: OFF" - System disabled

## 🎮 Controls & Keybinds

| Key Combination | Action |
|----------------|--------|
| **Shift + \\** | Open/Close GUI |
| **F6** | Quick Start/Stop Recording |
| **F7** | Quick Start/Stop Playback |
| **F8** | Toggle Autofishing |

### GUI Features
- **Script Recording**: Start/Stop recording with separate buttons
- **Script Playback**: Play with custom loop counts or infinite loops
- **Toggle Features**: Sprint, Crouch, Keystroke Display, Spotify, Autofishing
- **File Management**: Save/Load scripts with descriptive names
- **Real-time Status**: See current recording/playback status and autofishing state

## Script Format

Scripts are saved as JSON files in the `scriptrecorder` folder within your Minecraft directory:

```json
{
  "version": "1.0",
  "name": "MyScript",
  "created": "2025-06-29T12:00:00",
  "actions": [
    {
      "type": "key_press",
      "time": 0.0,
      "key": "w",
      "keyCode": 87
    },
    {
      "type": "mouse_click",
      "time": 1.5,
      "x": 960,
      "y": 540,
      "button": "left",
      "pressed": true
    }
  ],
  "totalActions": 2,
  "duration": 3.7,
  "loopCount": 1,
  "playbackSpeed": 1.0
}
```

## Development

### Building
```bash
./gradlew build
```

### Running in Development
```bash
./gradlew runClient
```

### Project Structure
```
src/main/java/com/scriptrecorder/mod/
├── ScriptRecorderMod.java          # Main mod class
├── client/
│   ├── KeyBindings.java            # Keybind registration
│   ├── ScriptRecorderClient.java   # Client-side event handling
│   └── gui/
│       └── ScriptRecorderScreen.java # Main GUI
└── recording/
    ├── ScriptAction.java           # Individual action data
    ├── RecordedScript.java         # Script container
    └── ScriptManager.java          # Recording/playback logic
```

## Technical Details

- **Minecraft Version**: 1.8.9
- **Forge Version**: 11.15.1.2318-1.8.9+
- **Java Version**: 8+
- **Dependencies**:
  - Gson for JSON handling
  - Apache HttpClient for Spotify API
  - LWJGL for input handling

## Safety and Ethics

This mod is designed for legitimate automation of repetitive tasks. Please use responsibly:

- ✅ Single-player automation
- ✅ Approved multiplayer servers
- ❌ Unfair advantages in competitive play
- ❌ Violating server rules or terms of service

## Future Enhancements

- File browser for script selection
- Script editing capabilities
- Variable playback speeds
- Conditional actions based on game state
- Integration with game files and world data

## License

This project is provided for educational and personal use. Please respect Minecraft's Terms of Service and server rules when using this mod.
