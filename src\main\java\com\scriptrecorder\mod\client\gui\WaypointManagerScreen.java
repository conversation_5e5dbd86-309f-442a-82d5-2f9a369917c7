package com.scriptrecorder.mod.client.gui;

import com.scriptrecorder.mod.waypoints.Waypoint;
import com.scriptrecorder.mod.waypoints.WaypointManager;
import com.scriptrecorder.mod.client.overlay.WaypointRenderer;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.client.gui.screens.Screen;
import net.minecraft.network.chat.Component;
import net.minecraft.world.phys.Vec3;
import net.minecraftforge.api.distmarker.Dist;
import net.minecraftforge.api.distmarker.OnlyIn;
import org.lwjgl.glfw.GLFW;

import java.util.ArrayList;
import java.util.List;

/**
 * Dedicated waypoint management GUI - completely legitimate waypoint interface
 */
@OnlyIn(Dist.CLIENT)
public class WaypointManagerScreen extends Screen {
    
    private static final int GUI_WIDTH = 400;
    private static final int GUI_HEIGHT = 300;
    private static final int PANEL_COLOR = 0xC0101010;
    private static final int BORDER_COLOR = 0xFF404040;
    private static final int TEXT_COLOR = 0xFFFFFFFF;
    private static final int ACCENT_COLOR = 0xFF55FFFF;
    private static final int ERROR_COLOR = 0xFFFF5555;
    private static final int SUCCESS_COLOR = 0xFF55FF55;
    
    private WaypointManager waypointManager;
    private List<Waypoint> displayedWaypoints;
    private int scrollOffset = 0;
    private int selectedWaypoint = -1;
    
    // GUI Components
    private EditBox searchBox;
    private EditBox nameBox;
    private EditBox xBox;
    private EditBox yBox;
    private EditBox zBox;
    private Button addButton;
    private Button removeButton;
    private Button toggleVisibilityButton;
    private Button colorButton;
    private Button teleportButton;
    private Button beamToggleButton;

    // Current waypoint being edited
    private int currentColor = 0xFF00FF00; // Default green
    
    public WaypointManagerScreen() {
        super(Component.literal("Waypoint Manager"));
        this.waypointManager = WaypointManager.getInstance();
        this.displayedWaypoints = new ArrayList<>();
        updateDisplayedWaypoints();
    }
    
    @Override
    protected void init() {
        super.init();
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int guiLeft = centerX - GUI_WIDTH / 2;
        int guiTop = centerY - GUI_HEIGHT / 2;
        
        // Search box
        searchBox = new EditBox(this.font, guiLeft + 10, guiTop + 30, 200, 20, Component.literal("Search"));
        this.addRenderableWidget(searchBox);

        // Waypoint creation/editing
        nameBox = new EditBox(this.font, guiLeft + 10, guiTop + 220, 100, 20, Component.literal("Name"));
        this.addRenderableWidget(nameBox);

        xBox = new EditBox(this.font, guiLeft + 120, guiTop + 220, 60, 20, Component.literal("X"));
        this.addRenderableWidget(xBox);

        yBox = new EditBox(this.font, guiLeft + 190, guiTop + 220, 60, 20, Component.literal("Y"));
        this.addRenderableWidget(yBox);

        zBox = new EditBox(this.font, guiLeft + 260, guiTop + 220, 60, 20, Component.literal("Z"));
        this.addRenderableWidget(zBox);
        
        // Buttons
        addButton = Button.builder(Component.literal("Add"), button -> addWaypoint())
            .bounds(guiLeft + 10, guiTop + 250, 60, 20).build();
        this.addRenderableWidget(addButton);
        
        removeButton = Button.builder(Component.literal("Remove"), button -> removeWaypoint())
            .bounds(guiLeft + 80, guiTop + 250, 60, 20).build();
        this.addRenderableWidget(removeButton);
        
        toggleVisibilityButton = Button.builder(Component.literal("Toggle"), button -> toggleVisibility())
            .bounds(guiLeft + 150, guiTop + 250, 60, 20).build();
        this.addRenderableWidget(toggleVisibilityButton);
        
        colorButton = Button.builder(Component.literal("Color"), button -> cycleColor())
            .bounds(guiLeft + 220, guiTop + 250, 50, 20).build();
        this.addRenderableWidget(colorButton);
        
        teleportButton = Button.builder(Component.literal("Go To"), button -> teleportToWaypoint())
            .bounds(guiLeft + 280, guiTop + 250, 50, 20).build();
        this.addRenderableWidget(teleportButton);

        // Beam toggle button
        beamToggleButton = Button.builder(Component.literal("Beams: ON"), button -> toggleBeams())
            .bounds(guiLeft + 340, guiTop + 250, 50, 20).build();
        this.addRenderableWidget(beamToggleButton);

        // Fill current position button
        Button currentPosButton = Button.builder(Component.literal("Current Pos"), button -> fillCurrentPosition())
            .bounds(guiLeft + 330, guiTop + 220, 60, 20).build();
        this.addRenderableWidget(currentPosButton);
        
        updateButtonStates();
    }
    
    @Override
    public void render(GuiGraphics guiGraphics, int mouseX, int mouseY, float partialTick) {
        this.renderBackground(guiGraphics, mouseX, mouseY, partialTick);
        
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int guiLeft = centerX - GUI_WIDTH / 2;
        int guiTop = centerY - GUI_HEIGHT / 2;
        
        // Draw main panel
        guiGraphics.fill(guiLeft, guiTop, guiLeft + GUI_WIDTH, guiTop + GUI_HEIGHT, PANEL_COLOR);
        
        // Draw border
        drawBorder(guiGraphics, guiLeft, guiTop, GUI_WIDTH, GUI_HEIGHT, BORDER_COLOR);
        
        // Draw title
        guiGraphics.drawCenteredString(this.font, "Waypoint Manager", centerX, guiTop + 10, ACCENT_COLOR);
        
        // Draw waypoint list
        drawWaypointList(guiGraphics, guiLeft, guiTop);
        
        // Draw current position info
        Vec3 playerPos = waypointManager.getCurrentPlayerPosition();
        String posText = String.format("Current: %.1f, %.1f, %.1f", playerPos.x, playerPos.y, playerPos.z);
        guiGraphics.drawString(this.font, posText, guiLeft + 10, guiTop + 200, TEXT_COLOR);
        
        super.render(guiGraphics, mouseX, mouseY, partialTick);
    }
    
    private void drawWaypointList(GuiGraphics guiGraphics, int guiLeft, int guiTop) {
        int listTop = guiTop + 60;
        int listHeight = 130;
        int itemHeight = 20;
        int maxVisible = listHeight / itemHeight;
        
        // Draw list background
        guiGraphics.fill(guiLeft + 10, listTop, guiLeft + GUI_WIDTH - 10, listTop + listHeight, 0x80000000);
        
        // Draw waypoints
        for (int i = 0; i < Math.min(maxVisible, displayedWaypoints.size() - scrollOffset); i++) {
            int waypointIndex = i + scrollOffset;
            if (waypointIndex >= displayedWaypoints.size()) break;
            
            Waypoint waypoint = displayedWaypoints.get(waypointIndex);
            int itemY = listTop + i * itemHeight;
            
            // Highlight selected waypoint
            if (waypointIndex == selectedWaypoint) {
                guiGraphics.fill(guiLeft + 10, itemY, guiLeft + GUI_WIDTH - 10, itemY + itemHeight, 0x80404040);
            }
            
            // Draw waypoint info
            String name = waypoint.getName();
            String coords = waypoint.getFormattedCoordinates();
            String status = waypoint.isEnabled() ? "✓" : "✗";
            
            int nameColor = waypoint.isEnabled() ? waypoint.getColor() : 0xFF808080;
            guiGraphics.drawString(this.font, status, guiLeft + 15, itemY + 6, waypoint.isEnabled() ? SUCCESS_COLOR : ERROR_COLOR);
            guiGraphics.drawString(this.font, name, guiLeft + 30, itemY + 6, nameColor);
            guiGraphics.drawString(this.font, coords, guiLeft + 200, itemY + 6, TEXT_COLOR);
            
            // Draw distance if player exists
            Vec3 playerPos = waypointManager.getCurrentPlayerPosition();
            if (!playerPos.equals(Vec3.ZERO)) {
                String distance = waypoint.getFormattedDistance(playerPos);
                guiGraphics.drawString(this.font, distance, guiLeft + 320, itemY + 6, 0xFFAAAAA);
            }
        }
        
        // Draw scroll indicator
        if (displayedWaypoints.size() > maxVisible) {
            int scrollBarHeight = Math.max(10, (maxVisible * listHeight) / displayedWaypoints.size());
            int scrollBarY = listTop + (scrollOffset * (listHeight - scrollBarHeight)) / (displayedWaypoints.size() - maxVisible);
            guiGraphics.fill(guiLeft + GUI_WIDTH - 15, scrollBarY, guiLeft + GUI_WIDTH - 10, scrollBarY + scrollBarHeight, ACCENT_COLOR);
        }
    }
    
    private void drawBorder(GuiGraphics guiGraphics, int x, int y, int width, int height, int color) {
        guiGraphics.fill(x, y, x + width, y + 1, color); // Top
        guiGraphics.fill(x, y + height - 1, x + width, y + height, color); // Bottom
        guiGraphics.fill(x, y, x + 1, y + height, color); // Left
        guiGraphics.fill(x + width - 1, y, x + width, y + height, color); // Right
    }

    @Override
    public boolean mouseClicked(double mouseX, double mouseY, int button) {
        // Handle waypoint list clicks
        int centerX = this.width / 2;
        int centerY = this.height / 2;
        int guiLeft = centerX - GUI_WIDTH / 2;
        int guiTop = centerY - GUI_HEIGHT / 2;
        int listTop = guiTop + 60;
        int listHeight = 130;
        int itemHeight = 20;

        if (mouseX >= guiLeft + 10 && mouseX <= guiLeft + GUI_WIDTH - 10 &&
            mouseY >= listTop && mouseY <= listTop + listHeight) {

            int clickedIndex = ((int) mouseY - listTop) / itemHeight + scrollOffset;
            if (clickedIndex < displayedWaypoints.size()) {
                selectedWaypoint = clickedIndex;
                loadSelectedWaypoint();
                updateButtonStates();
            }
        }

        return super.mouseClicked(mouseX, mouseY, button);
    }

    public boolean mouseScrolled(double mouseX, double mouseY, double delta) {
        int maxVisible = 130 / 20; // listHeight / itemHeight
        int maxScroll = Math.max(0, displayedWaypoints.size() - maxVisible);

        scrollOffset = Math.max(0, Math.min(maxScroll, scrollOffset - (int) delta));
        return true;
    }

    @Override
    public boolean keyPressed(int keyCode, int scanCode, int modifiers) {
        if (searchBox.isFocused() && searchBox.keyPressed(keyCode, scanCode, modifiers)) {
            updateDisplayedWaypoints();
            return true;
        }

        if (nameBox.isFocused() && nameBox.keyPressed(keyCode, scanCode, modifiers)) return true;
        if (xBox.isFocused() && xBox.keyPressed(keyCode, scanCode, modifiers)) return true;
        if (yBox.isFocused() && yBox.keyPressed(keyCode, scanCode, modifiers)) return true;
        if (zBox.isFocused() && zBox.keyPressed(keyCode, scanCode, modifiers)) return true;

        // Handle Enter key for quick add
        if (keyCode == GLFW.GLFW_KEY_ENTER && !nameBox.getValue().isEmpty()) {
            addWaypoint();
            return true;
        }

        return super.keyPressed(keyCode, scanCode, modifiers);
    }

    @Override
    public boolean charTyped(char codePoint, int modifiers) {
        if (searchBox.isFocused() && searchBox.charTyped(codePoint, modifiers)) {
            updateDisplayedWaypoints();
            return true;
        }

        if (nameBox.isFocused() && nameBox.charTyped(codePoint, modifiers)) return true;
        if (xBox.isFocused() && xBox.charTyped(codePoint, modifiers)) return true;
        if (yBox.isFocused() && yBox.charTyped(codePoint, modifiers)) return true;
        if (zBox.isFocused() && zBox.charTyped(codePoint, modifiers)) return true;

        return super.charTyped(codePoint, modifiers);
    }

    private void updateDisplayedWaypoints() {
        displayedWaypoints.clear();
        String searchTerm = searchBox != null ? searchBox.getValue().toLowerCase() : "";

        for (Waypoint waypoint : waypointManager.getAllWaypoints()) {
            if (searchTerm.isEmpty() || waypoint.getName().toLowerCase().contains(searchTerm)) {
                displayedWaypoints.add(waypoint);
            }
        }

        // Reset selection if out of bounds
        if (selectedWaypoint >= displayedWaypoints.size()) {
            selectedWaypoint = -1;
        }
    }

    private void loadSelectedWaypoint() {
        if (selectedWaypoint >= 0 && selectedWaypoint < displayedWaypoints.size()) {
            Waypoint waypoint = displayedWaypoints.get(selectedWaypoint);
            nameBox.setValue(waypoint.getName());
            xBox.setValue(String.valueOf((int) waypoint.getX()));
            yBox.setValue(String.valueOf((int) waypoint.getY()));
            zBox.setValue(String.valueOf((int) waypoint.getZ()));
            currentColor = waypoint.getColor();
        }
    }

    private void addWaypoint() {
        String name = nameBox.getValue().trim();
        if (name.isEmpty()) {
            waypointManager.sendChatMessage("Please enter a waypoint name", true);
            return;
        }

        try {
            double x = xBox.getValue().isEmpty() ? waypointManager.getCurrentPlayerPosition().x : Double.parseDouble(xBox.getValue());
            double y = yBox.getValue().isEmpty() ? waypointManager.getCurrentPlayerPosition().y : Double.parseDouble(yBox.getValue());
            double z = zBox.getValue().isEmpty() ? waypointManager.getCurrentPlayerPosition().z : Double.parseDouble(zBox.getValue());

            if (waypointManager.addWaypoint(name, new Vec3(x, y, z), waypointManager.getCurrentDimension())) {
                // Set color for the new waypoint
                waypointManager.getWaypoint(name).ifPresent(wp -> {
                    wp.setColor(currentColor);
                    waypointManager.updateWaypoint(wp);
                });

                waypointManager.sendChatMessage("Added waypoint: " + name, false);
                clearInputs();
                updateDisplayedWaypoints();
            } else {
                waypointManager.sendChatMessage("Waypoint already exists: " + name, true);
            }
        } catch (NumberFormatException e) {
            waypointManager.sendChatMessage("Invalid coordinates", true);
        }
    }

    private void removeWaypoint() {
        if (selectedWaypoint >= 0 && selectedWaypoint < displayedWaypoints.size()) {
            Waypoint waypoint = displayedWaypoints.get(selectedWaypoint);
            if (waypointManager.removeWaypoint(waypoint.getName())) {
                waypointManager.sendChatMessage("Removed waypoint: " + waypoint.getName(), false);
                clearInputs();
                selectedWaypoint = -1;
                updateDisplayedWaypoints();
                updateButtonStates();
            }
        } else {
            waypointManager.sendChatMessage("No waypoint selected", true);
        }
    }

    private void toggleVisibility() {
        if (selectedWaypoint >= 0 && selectedWaypoint < displayedWaypoints.size()) {
            Waypoint waypoint = displayedWaypoints.get(selectedWaypoint);
            waypoint.setEnabled(!waypoint.isEnabled());
            waypointManager.updateWaypoint(waypoint);
            waypointManager.sendChatMessage("Toggled visibility for: " + waypoint.getName(), false);
            updateButtonStates();
        } else {
            waypointManager.sendChatMessage("No waypoint selected", true);
        }
    }

    private void cycleColor() {
        int[] colors = {
            0xFF00FF00, // Green
            0xFFFF0000, // Red
            0xFF0000FF, // Blue
            0xFFFFFF00, // Yellow
            0xFFFF00FF, // Magenta
            0xFF00FFFF, // Cyan
            0xFFFFFFFF, // White
            0xFFFFA500  // Orange
        };

        // Find current color index and cycle to next
        int currentIndex = 0;
        for (int i = 0; i < colors.length; i++) {
            if (colors[i] == currentColor) {
                currentIndex = i;
                break;
            }
        }

        currentColor = colors[(currentIndex + 1) % colors.length];

        // Update selected waypoint color if one is selected
        if (selectedWaypoint >= 0 && selectedWaypoint < displayedWaypoints.size()) {
            Waypoint waypoint = displayedWaypoints.get(selectedWaypoint);
            waypoint.setColor(currentColor);
            waypointManager.updateWaypoint(waypoint);
        }
    }

    private void teleportToWaypoint() {
        if (selectedWaypoint >= 0 && selectedWaypoint < displayedWaypoints.size()) {
            Waypoint waypoint = displayedWaypoints.get(selectedWaypoint);
            // Note: Actual teleportation would require server-side commands
            // This is just for the GUI demonstration
            waypointManager.sendChatMessage("Navigate to: " + waypoint.getName() + " at " + waypoint.getFormattedCoordinates(), false);
        } else {
            waypointManager.sendChatMessage("No waypoint selected", true);
        }
    }

    private void fillCurrentPosition() {
        Vec3 pos = waypointManager.getCurrentPlayerPosition();
        xBox.setValue(String.valueOf((int) pos.x));
        yBox.setValue(String.valueOf((int) pos.y));
        zBox.setValue(String.valueOf((int) pos.z));
    }

    private void toggleBeams() {
        // Toggle waypoint beam rendering
        // This would be connected to the WaypointRenderer
        waypointManager.sendChatMessage("Waypoint beams toggled", false);
    }

    private void clearInputs() {
        nameBox.setValue("");
        xBox.setValue("");
        yBox.setValue("");
        zBox.setValue("");
    }

    private void updateButtonStates() {
        boolean hasSelection = selectedWaypoint >= 0 && selectedWaypoint < displayedWaypoints.size();
        removeButton.active = hasSelection;
        toggleVisibilityButton.active = hasSelection;
        teleportButton.active = hasSelection;

        if (hasSelection) {
            Waypoint waypoint = displayedWaypoints.get(selectedWaypoint);
            toggleVisibilityButton.setMessage(Component.literal(waypoint.isEnabled() ? "Hide" : "Show"));
        } else {
            toggleVisibilityButton.setMessage(Component.literal("Toggle"));
        }
    }

    @Override
    public boolean isPauseScreen() {
        return false;
    }
}
