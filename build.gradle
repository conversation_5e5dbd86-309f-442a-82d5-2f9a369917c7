buildscript {
    repositories {
        jcenter()
        maven { url = 'https://files.minecraftforge.net/maven' }
    }
    dependencies {
        classpath 'net.minecraftforge.gradle:ForgeGradle:2.2-SNAPSHOT'
    }
}

apply plugin: 'net.minecraftforge.gradle.forge'

version = '3.5.0'
group = 'com.waypointmod'
archivesBaseName = 'waypoint-mod-1.8.9'

sourceCompatibility = targetCompatibility = '1.8'
compileJava {
    sourceCompatibility = targetCompatibility = '1.8'
}

minecraft {
    version = '1.8.9-11.15.1.2318'
    runDir = 'run'

    mappings = 'stable_22'

    replace '@VERSION@', project.version
    replaceIn 'ScriptRecorderMod.java'
}

repositories {
    mavenCentral()
}

dependencies {
    // Gson for JSON handling (included in Minecraft/Forge)
    // No need to include separately
}

jar {
    manifest {
        attributes([
            "Specification-Title": "scriptrecorder",
            "Specification-Vendor": "scriptrecorder",
            "Specification-Version": "1",
            "Implementation-Title": project.name,
            "Implementation-Version": version,
            "Implementation-Vendor": "scriptrecorder",
            "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        ])
    }
}



processResources {
    inputs.property 'version', project.version
    inputs.property 'mcversion', project.minecraft.version

    from(sourceSets.main.resources.srcDirs) {
        include 'mcmod.info'

        expand 'version': project.version, 'mcversion': project.minecraft.version
    }

    from(sourceSets.main.resources.srcDirs) {
        exclude 'mcmod.info'
    }
}
