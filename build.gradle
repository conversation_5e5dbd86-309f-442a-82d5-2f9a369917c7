buildscript {
    repositories {
        maven { url = 'https://maven.minecraftforge.net' }
        mavenCentral()
    }
    dependencies {
        classpath group: 'net.minecraftforge.gradle', name: 'ForgeGradle', version: '6.0.24', changing: true
    }
}

plugins {
    id 'eclipse'
    id 'maven-publish'
}

apply plugin: 'net.minecraftforge.gradle'

version = '3.5.0'
group = 'com.waypointmod'
archivesBaseName = 'waypoint-mod-1.20.4'

sourceCompatibility = targetCompatibility = JavaVersion.VERSION_17

minecraft {
    mappings channel: 'official', version: '1.20.4'

    runs {
        client {
            workingDirectory project.file('run')
            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'

            mods {
                scriptrecorder {
                    source sourceSets.main
                }
            }
        }

        server {
            workingDirectory project.file('run')
            property 'forge.logging.markers', 'REGISTRIES'
            property 'forge.logging.console.level', 'debug'
            mods {
                scriptrecorder {
                    source sourceSets.main
                }
            }
        }
    }
}

repositories {
    mavenCentral()
}

dependencies {
    minecraft 'net.minecraftforge:forge:1.20.4-49.2.0'

    // Gson for JSON handling (included in Minecraft/Forge)
    // No need to include separately
}

jar {
    manifest {
        attributes([
            "Specification-Title": "scriptrecorder",
            "Specification-Vendor": "scriptrecorder",
            "Specification-Version": "1",
            "Implementation-Title": project.name,
            "Implementation-Version": version,
            "Implementation-Vendor": "scriptrecorder",
            "Implementation-Timestamp": new Date().format("yyyy-MM-dd'T'HH:mm:ssZ")
        ])
    }
}



processResources {
    var replaceProperties = [
        minecraft_version: minecraft_version,
        minecraft_version_range: minecraft_version_range,
        forge_version: forge_version,
        forge_version_range: forge_version_range,
        loader_version_range: loader_version_range,
        mod_id: mod_id,
        mod_name: mod_name,
        mod_license: mod_license,
        mod_version: mod_version,
        mod_authors: mod_authors,
        mod_description: mod_description
    ]
    inputs.properties replaceProperties

    filesMatching(['META-INF/mods.toml', 'pack.mcmeta']) {
        expand replaceProperties + [project: project]
    }
}
