buildscript {
    repositories {
        mavenCentral()
        maven {
            name = "forge"
            url = "https://maven.minecraftforge.net"
        }
    }
    dependencies {
        classpath 'net.minecraftforge.gradle:ForgeGradle:2.1-SNAPSHOT'
    }
}

apply plugin: 'net.minecraftforge.gradle.forge'

version = "3.6.1"
group = "com.scriptrecordermod"
archivesBaseName = "script-recorder-mod-1.8.9"

sourceCompatibility = targetCompatibility = '1.8'

minecraft {
    version = "1.8.9-11.15.1.1722"
    runDir = "run"

    mappings = "stable_22"

    replace "@VERSION@", project.version
    replaceIn "ScriptRecorderMod.java"
}

repositories {
    mavenCentral()
}

dependencies {
    // No additional dependencies needed for basic functionality
}

processResources
{
    // this will ensure that this task is redone when the versions change.
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    // replace stuff in mcmod.info, nothing else
    from(sourceSets.main.resources.srcDirs) {
        include 'mcmod.info'

        // replace version and mcversion
        expand 'version':project.version, 'mcversion':project.minecraft.version
    }

    // copy everything else, thats not the mcmod.info
    from(sourceSets.main.resources.srcDirs) {
        exclude 'mcmod.info'
    }
}
