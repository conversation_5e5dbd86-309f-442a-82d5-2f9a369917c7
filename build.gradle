buildscript {
    repositories {
        jcenter()
        maven {
            name = "forge"
            url = "http://files.minecraftforge.net/maven"
        }
    }
    dependencies {
        classpath 'net.minecraftforge.gradle:ForgeGradle:2.1-SNAPSHOT'
    }
}

apply plugin: 'forge'

version = "3.5.0"
group = "com.waypointmod"
archivesBaseName = "waypoint-mod-1.8.9"

minecraft {
    version = "1.8.9-11.15.1.2318"
    runDir = "eclipse"

    mappings = "stable_22"

    replace "@VERSION@", project.version
    replaceIn "ScriptRecorderModMinimal.java"
}

dependencies {
    // No additional dependencies needed for basic functionality
}

processResources
{
    // this will ensure that this task is redone when the versions change.
    inputs.property "version", project.version
    inputs.property "mcversion", project.minecraft.version

    // replace stuff in mcmod.info, nothing else
    from(sourceSets.main.resources.srcDirs) {
        include 'mcmod.info'

        // replace version and mcversion
        expand 'version':project.version, 'mcversion':project.minecraft.version
    }

    // copy everything else, thats not the mcmod.info
    from(sourceSets.main.resources.srcDirs) {
        exclude 'mcmod.info'
    }
}
