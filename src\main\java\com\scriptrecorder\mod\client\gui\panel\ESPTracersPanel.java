package com.scriptrecorder.mod.client.gui.panel;

import com.scriptrecorder.mod.client.overlay.TracerESPOverlay;
import com.scriptrecorder.mod.client.settings.ModSettings;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.GuiGraphics;
import net.minecraft.client.gui.components.Button;
import net.minecraft.client.gui.components.EditBox;
import net.minecraft.network.chat.Component;
import java.awt.Color;

/**
 * Panel for ESP and Tracers controls
 */
public class ESPTracersPanel extends Panel {
    
    // UI Components
    private Button toggleTracersButton;
    private Button toggleESPButton;
    private Button tracerFilterButton;
    private Button espFilterButton;
    private Button toggleShowNPCsButton;
    private Button toggleEntityDetectionButton;
    private EditBox tracerDistanceBox;
    private EditBox espDistanceBox;
    private EditBox entityDetectionRangeBox;
    
    // Colors
    private static final Color ENABLED_COLOR = new Color(50, 220, 50);
    private static final Color DISABLED_COLOR = new Color(220, 50, 50);
    
    public ESPTracersPanel() {
        super("ESP & Tracers", 300, 10, 320, 200);
        initializeComponents();
    }
    
    private void initializeComponents() {
        clearWidgets();
        
        // Calculate positions relative to panel
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Row 1: Main toggles
        toggleTracersButton = Button.builder(
            getTracersButtonText(),
            button -> toggleTracers())
            .bounds(contentX, contentY, 90, 20)
            .build();
        addWidget(toggleTracersButton);
        
        toggleESPButton = Button.builder(
            getESPButtonText(),
            button -> toggleESP())
            .bounds(contentX + 100, contentY, 90, 20)
            .build();
        addWidget(toggleESPButton);
        
        toggleShowNPCsButton = Button.builder(
            getShowNPCsButtonText(),
            button -> toggleShowNPCs())
            .bounds(contentX + 200, contentY, 90, 20)
            .build();
        addWidget(toggleShowNPCsButton);
        
        // Row 2: Filters
        tracerFilterButton = Button.builder(
            getTracerFilterButtonText(),
            button -> cycleTracerFilter())
            .bounds(contentX, contentY + 30, 90, 20)
            .build();
        addWidget(tracerFilterButton);
        
        espFilterButton = Button.builder(
            getESPFilterButtonText(),
            button -> cycleESPFilter())
            .bounds(contentX + 100, contentY + 30, 90, 20)
            .build();
        addWidget(espFilterButton);
        
        toggleEntityDetectionButton = Button.builder(
            getEntityDetectionButtonText(),
            button -> toggleEntityDetection())
            .bounds(contentX + 200, contentY + 30, 90, 20)
            .build();
        addWidget(toggleEntityDetectionButton);
        
        // Row 3: Distance inputs
        tracerDistanceBox = new EditBox(Minecraft.getInstance().font, 
                                       contentX, contentY + 60, 80, 20, 
                                       Component.literal("Tracer Distance"));
        tracerDistanceBox.setValue(String.valueOf((int) ModSettings.getTracerDistance()));
        tracerDistanceBox.setMaxLength(3);
        addWidget(tracerDistanceBox);
        
        espDistanceBox = new EditBox(Minecraft.getInstance().font, 
                                    contentX + 90, contentY + 60, 80, 20, 
                                    Component.literal("ESP Distance"));
        espDistanceBox.setValue(String.valueOf((int) ModSettings.getESPDistance()));
        espDistanceBox.setMaxLength(3);
        addWidget(espDistanceBox);
        
        entityDetectionRangeBox = new EditBox(Minecraft.getInstance().font, 
                                             contentX + 180, contentY + 60, 80, 20, 
                                             Component.literal("Detection Range"));
        entityDetectionRangeBox.setValue(String.valueOf((int) ModSettings.getInstance().entityDetectionRange));
        entityDetectionRangeBox.setMaxLength(4);
        addWidget(entityDetectionRangeBox);
    }
    
    @Override
    protected void renderContent(GuiGraphics guiGraphics, int mouseX, int mouseY, float delta) {
        // Always update component positions when panel moves
        updateComponentPositions();
        
        // Draw labels
        int textX = (int)(x + 10);
        int textY = (int)(y + titleBarHeight + 90);
        
        // Distance labels
        guiGraphics.drawString(Minecraft.getInstance().font, "Tracer Range:", 
                             textX, textY, Color.WHITE.getRGB());
        guiGraphics.drawString(Minecraft.getInstance().font, "ESP Range:", 
                             textX + 90, textY, Color.WHITE.getRGB());
        guiGraphics.drawString(Minecraft.getInstance().font, "Scan Range:", 
                             textX + 180, textY, Color.WHITE.getRGB());
        
        // Current values
        String tracerDist = tracerDistanceBox != null ? tracerDistanceBox.getValue() : 
                           String.valueOf((int) ModSettings.getTracerDistance());
        String espDist = espDistanceBox != null ? espDistanceBox.getValue() : 
                        String.valueOf((int) ModSettings.getESPDistance());
        String detectionRange = entityDetectionRangeBox != null ? entityDetectionRangeBox.getValue() : 
                               String.valueOf((int) ModSettings.getInstance().entityDetectionRange);
        
        guiGraphics.drawString(Minecraft.getInstance().font, "(" + tracerDist + ")", 
                             textX, textY + 12, Color.LIGHT_GRAY.getRGB());
        guiGraphics.drawString(Minecraft.getInstance().font, "(" + espDist + ")", 
                             textX + 90, textY + 12, Color.LIGHT_GRAY.getRGB());
        guiGraphics.drawString(Minecraft.getInstance().font, "(" + detectionRange + ")", 
                             textX + 180, textY + 12, Color.LIGHT_GRAY.getRGB());
        
        // Status info
        guiGraphics.drawString(Minecraft.getInstance().font, "Filter: All → Players → Mobs", 
                             textX, textY + 30, Color.GRAY.getRGB());
        guiGraphics.drawString(Minecraft.getInstance().font, "Enhanced entity tracking & navigation", 
                             textX, textY + 42, Color.GRAY.getRGB());
    }
    
    @Override
    protected void updateComponentPositions() {
        int contentX = (int)(x + 10);
        int contentY = (int)(y + titleBarHeight + 10);
        
        // Update all component positions
        if (toggleTracersButton != null) {
            toggleTracersButton.setX(contentX);
            toggleTracersButton.setY(contentY);
        }
        if (toggleESPButton != null) {
            toggleESPButton.setX(contentX + 100);
            toggleESPButton.setY(contentY);
        }
        if (toggleShowNPCsButton != null) {
            toggleShowNPCsButton.setX(contentX + 200);
            toggleShowNPCsButton.setY(contentY);
        }
        if (tracerFilterButton != null) {
            tracerFilterButton.setX(contentX);
            tracerFilterButton.setY(contentY + 30);
        }
        if (espFilterButton != null) {
            espFilterButton.setX(contentX + 100);
            espFilterButton.setY(contentY + 30);
        }
        if (toggleEntityDetectionButton != null) {
            toggleEntityDetectionButton.setX(contentX + 200);
            toggleEntityDetectionButton.setY(contentY + 30);
        }
        if (tracerDistanceBox != null) {
            tracerDistanceBox.setX(contentX);
            tracerDistanceBox.setY(contentY + 60);
        }
        if (espDistanceBox != null) {
            espDistanceBox.setX(contentX + 90);
            espDistanceBox.setY(contentY + 60);
        }
        if (entityDetectionRangeBox != null) {
            entityDetectionRangeBox.setX(contentX + 180);
            entityDetectionRangeBox.setY(contentY + 60);
        }
    }
    
    // Button action methods
    private void toggleTracers() {
        boolean newState = !ModSettings.isTracersEnabled();
        ModSettings.setTracersEnabled(newState);
        TracerESPOverlay.setTracersEnabled(newState);
        updateButtonStates();
    }

    private void toggleESP() {
        boolean newState = !ModSettings.isESPEnabled();
        ModSettings.setESPEnabled(newState);
        TracerESPOverlay.setESPEnabled(newState);
        updateButtonStates();
    }
    
    private void toggleShowNPCs() {
        boolean newState = !ModSettings.getInstance().espShowNPCs;
        ModSettings.getInstance().espShowNPCs = newState;
        updateButtonStates();
    }
    
    private void toggleEntityDetection() {
        boolean newState = !ModSettings.getInstance().entityDetectionEnabled;
        ModSettings.getInstance().entityDetectionEnabled = newState;
        updateButtonStates();
    }
    
    private void cycleTracerFilter() {
        String current = ModSettings.getTracerFilter();
        String next = current.equals("both") ? "players" : 
                     current.equals("players") ? "mobs" : "both";
        ModSettings.setTracerFilter(next);
        updateButtonStates();
    }
    
    private void cycleESPFilter() {
        String current = ModSettings.getESPFilter();
        String next = current.equals("both") ? "players" : 
                     current.equals("players") ? "mobs" : "both";
        ModSettings.setESPFilter(next);
        updateButtonStates();
    }
    
    private void updateButtonStates() {
        if (toggleTracersButton != null) toggleTracersButton.setMessage(getTracersButtonText());
        if (toggleESPButton != null) toggleESPButton.setMessage(getESPButtonText());
        if (toggleShowNPCsButton != null) toggleShowNPCsButton.setMessage(getShowNPCsButtonText());
        if (toggleEntityDetectionButton != null) toggleEntityDetectionButton.setMessage(getEntityDetectionButtonText());
        if (tracerFilterButton != null) tracerFilterButton.setMessage(getTracerFilterButtonText());
        if (espFilterButton != null) espFilterButton.setMessage(getESPFilterButtonText());
    }
    
    // Button text methods
    private Component getTracersButtonText() {
        boolean enabled = ModSettings.isTracersEnabled();
        return Component.literal("Tracers: " + (enabled ? "ON" : "OFF"));
    }

    private Component getESPButtonText() {
        boolean enabled = ModSettings.isESPEnabled();
        return Component.literal("ESP: " + (enabled ? "ON" : "OFF"));
    }
    
    private Component getShowNPCsButtonText() {
        return Component.literal("NPCs: " + (ModSettings.getInstance().espShowNPCs ? "ON" : "OFF"));
    }
    
    private Component getEntityDetectionButtonText() {
        return Component.literal("Scan: " + (ModSettings.getInstance().entityDetectionEnabled ? "ON" : "OFF"));
    }
    
    private Component getTracerFilterButtonText() {
        String filter = ModSettings.getTracerFilter();
        String displayText = filter.equals("both") ? "All" :
                           filter.equals("players") ? "Players" : "Mobs";
        return Component.literal("T:" + displayText);
    }
    
    private Component getESPFilterButtonText() {
        String filter = ModSettings.getESPFilter();
        String displayText = filter.equals("both") ? "All" :
                           filter.equals("players") ? "Players" : "Mobs";
        return Component.literal("E:" + displayText);
    }
    
    /**
     * Update the panel (called every frame)
     */
    public void update() {
        // Always update component positions first
        updateComponentPositions();
        updateDistanceSettings();
        updateButtonStates();
    }
    
    private void updateDistanceSettings() {
        // Update tracer distance
        if (tracerDistanceBox != null) {
            try {
                String value = tracerDistanceBox.getValue().trim();
                if (!value.isEmpty()) {
                    double distance = Double.parseDouble(value);
                    if (distance > 0 && distance <= 999) {
                        ModSettings.setTracerDistance(distance);
                    }
                }
            } catch (NumberFormatException e) {
                // Invalid input, reset to current value
                tracerDistanceBox.setValue(String.valueOf((int) ModSettings.getTracerDistance()));
            }
        }
        
        // Update ESP distance
        if (espDistanceBox != null) {
            try {
                String value = espDistanceBox.getValue().trim();
                if (!value.isEmpty()) {
                    double distance = Double.parseDouble(value);
                    if (distance > 0 && distance <= 999) {
                        ModSettings.setESPDistance(distance);
                    }
                }
            } catch (NumberFormatException e) {
                // Invalid input, reset to current value
                espDistanceBox.setValue(String.valueOf((int) ModSettings.getESPDistance()));
            }
        }
        
        // Update entity detection range
        if (entityDetectionRangeBox != null) {
            try {
                String value = entityDetectionRangeBox.getValue().trim();
                if (!value.isEmpty()) {
                    double range = Double.parseDouble(value);
                    if (range > 0 && range <= 9999) {
                        ModSettings.getInstance().entityDetectionRange = range;
                    }
                }
            } catch (NumberFormatException e) {
                // Invalid input, reset to current value
                entityDetectionRangeBox.setValue(String.valueOf((int) ModSettings.getInstance().entityDetectionRange));
            }
        }
    }
}
