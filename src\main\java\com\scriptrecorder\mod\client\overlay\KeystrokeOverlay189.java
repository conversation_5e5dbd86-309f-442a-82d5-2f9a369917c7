package com.scriptrecorder.mod.client.overlay;

import net.minecraft.client.Minecraft;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

/**
 * Simple keystroke overlay for 1.8.9 compatibility
 */
@SideOnly(Side.CLIENT)
public class KeystrokeOverlay189 {
    
    private static boolean enabled = false;
    
    public static void setEnabled(boolean enabled) {
        KeystrokeOverlay189.enabled = enabled;
    }
    
    public static boolean isEnabled() {
        return enabled;
    }
    
    public static void onKeyInput(int keyCode, boolean pressed) {
        // Simple implementation for 1.8.9
        if (!enabled) return;
        
        // TODO: Implement keystroke display for 1.8.9
    }
    
    public static void onMouseInput(int button, boolean pressed) {
        // Simple implementation for 1.8.9
        if (!enabled) return;
        
        // TODO: Implement mouse display for 1.8.9
    }
}
