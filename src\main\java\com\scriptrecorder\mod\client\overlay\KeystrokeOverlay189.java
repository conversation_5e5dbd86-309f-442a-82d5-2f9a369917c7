package com.scriptrecorder.mod.client.overlay;

import com.scriptrecorder.mod.client.settings.ModSettings;
import net.minecraft.client.Minecraft;
import net.minecraft.client.gui.FontRenderer;
import net.minecraft.client.gui.Gui;
import net.minecraftforge.client.event.RenderGameOverlayEvent;
import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;
import org.lwjgl.input.Keyboard;

import java.util.HashMap;
import java.util.Map;

/**
 * Keystroke overlay for 1.8.9 compatibility
 */
@SideOnly(Side.CLIENT)
public class KeystrokeOverlay189 {

    private static boolean enabled = false;
    private static final Map<Integer, Long> keyPressTime = new HashMap<>();
    private static final Map<Integer, Long> mousePressTime = new HashMap<>();
    private static final long DISPLAY_TIME = 1000; // Show keys for 1 second

    public static void setEnabled(boolean enabled) {
        KeystrokeOverlay189.enabled = enabled;
    }

    public static boolean isEnabled() {
        return enabled;
    }

    public static void onKeyInput(int keyCode, boolean pressed) {
        if (!enabled) return;

        if (pressed) {
            keyPressTime.put(keyCode, System.currentTimeMillis());
        }
    }

    public static void onMouseInput(int button, boolean pressed) {
        if (!enabled) return;

        if (pressed && button >= 0) {
            mousePressTime.put(button, System.currentTimeMillis());
        }
    }

    @SubscribeEvent
    public static void onRenderOverlay(RenderGameOverlayEvent.Post event) {
        if (!enabled || event.type != RenderGameOverlayEvent.ElementType.ALL) {
            return;
        }

        Minecraft mc = Minecraft.getMinecraft();
        if (mc.currentScreen != null) return;

        FontRenderer fontRenderer = mc.fontRendererObj;
        ModSettings settings = ModSettings.getInstance();

        int x = settings.keystrokeDisplayX;
        int y = settings.keystrokeDisplayY;
        long currentTime = System.currentTimeMillis();

        // Clean up old entries
        keyPressTime.entrySet().removeIf(entry -> currentTime - entry.getValue() > DISPLAY_TIME);
        mousePressTime.entrySet().removeIf(entry -> currentTime - entry.getValue() > DISPLAY_TIME);

        // Draw active keys
        int lineHeight = 12;
        int currentY = y;

        for (Map.Entry<Integer, Long> entry : keyPressTime.entrySet()) {
            String keyName = getKeyDisplayName(entry.getKey());
            if (keyName != null) {
                // Calculate fade based on time
                long timeSince = currentTime - entry.getValue();
                float alpha = Math.max(0.0f, 1.0f - (float)timeSince / DISPLAY_TIME);
                int color = (int)(alpha * 255) << 24 | 0xFFFFFF;

                fontRenderer.drawString(keyName, x, currentY, color);
                currentY += lineHeight;
            }
        }

        // Draw active mouse buttons
        for (Map.Entry<Integer, Long> entry : mousePressTime.entrySet()) {
            String buttonName = getMouseDisplayName(entry.getKey());
            if (buttonName != null) {
                // Calculate fade based on time
                long timeSince = currentTime - entry.getValue();
                float alpha = Math.max(0.0f, 1.0f - (float)timeSince / DISPLAY_TIME);
                int color = (int)(alpha * 255) << 24 | 0xFFFFFF;

                fontRenderer.drawString(buttonName, x, currentY, color);
                currentY += lineHeight;
            }
        }
    }

    private static String getKeyDisplayName(int keyCode) {
        switch (keyCode) {
            case Keyboard.KEY_SPACE: return "SPACE";
            case Keyboard.KEY_RETURN: return "ENTER";
            case Keyboard.KEY_TAB: return "TAB";
            case Keyboard.KEY_LSHIFT: return "LSHIFT";
            case Keyboard.KEY_RSHIFT: return "RSHIFT";
            case Keyboard.KEY_LCONTROL: return "LCTRL";
            case Keyboard.KEY_RCONTROL: return "RCTRL";
            case Keyboard.KEY_LMENU: return "LALT";
            case Keyboard.KEY_RMENU: return "RALT";
            default:
                // For letter keys
                if (keyCode >= Keyboard.KEY_A && keyCode <= Keyboard.KEY_Z) {
                    return String.valueOf((char) ('A' + (keyCode - Keyboard.KEY_A)));
                }
                // For number keys
                if (keyCode >= Keyboard.KEY_0 && keyCode <= Keyboard.KEY_9) {
                    return String.valueOf((char) ('0' + (keyCode - Keyboard.KEY_0)));
                }
                return null;
        }
    }

    private static String getMouseDisplayName(int button) {
        switch (button) {
            case 0: return "LMB";
            case 1: return "RMB";
            case 2: return "MMB";
            default: return "M" + button;
        }
    }
}
